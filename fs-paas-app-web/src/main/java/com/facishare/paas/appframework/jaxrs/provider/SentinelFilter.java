package com.facishare.paas.appframework.jaxrs.provider;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.ResourceTypeConstants;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.context.ContextUtil;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.SentinelConfig;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.rest.CEPXHeader;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.facishare.paas.appframework.jaxrs.annotation.CepAPI;
import com.facishare.paas.appframework.jaxrs.annotation.InnerAPI;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.resource.BaseResource;
import com.fxiaoke.common.sentinel.resteasy.SentinelResteasyProviderFilter;
import com.fxiaoke.common.sentinel.resteasy.fallback.DefaultSentinelResteasyFallback;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Priority;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerResponseContext;
import javax.ws.rs.container.ResourceInfo;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import javax.ws.rs.ext.Provider;
import java.io.IOException;
import java.util.*;

/**
 * Created by zhaooju on 2024/5/31
 */
@Slf4j
@Priority(0)
@Provider
@Component
public class SentinelFilter extends SentinelResteasyProviderFilter {
    public static final String APP_NAME = ConfigHelper.getProcessInfo().getAppName();
    public static final String PROFILE = ConfigHelper.getProcessInfo().getProfile();

    public static final String SENTINEL_JAX_RS_PROVIDER_ENTRY_PROPERTY = "sentinel_jax_rs_provider_entry_property";
    public static final String SENTINEL_JAX_RS_PROVIDER_FILTER_CONTEXT_PROPERTY = "sentinel_jax_rs_provider_filter_context_property";
    public static final DefaultSentinelResteasyFallback DEFAULT_SENTINEL_RESTEASY_FALLBACK = new DefaultSentinelResteasyFallback();

    @Getter
    @Context
    private ResourceInfo resourceInfo;

    @Override
    public void filter(ContainerRequestContext containerRequestContext) throws IOException {
        try {
            doFilter(containerRequestContext);
        } catch (Exception e) {
            log.warn("sentinel filter fail!", e);
        }
    }

    private void doFilter(ContainerRequestContext containerRequestContext) {
        // 判断应用是否需要走限流
        if (!SentinelConfig.getInstance().enableByProfile(PROFILE)) {
            return;
        }
        FilterParamsContext filterContext = makeFilterContext(containerRequestContext);
        Deque<Entry> entries = Lists.newLinkedList();
        boolean abortRequest = false;
        try {
            Set<String> resourceNames = getResourceNames(containerRequestContext, filterContext);
            // 获取热点参数 ["74255","fs-paas-function-service-runtime-provider_74255","74255_AccountObj","74255_AccountObj_Choose"]
            Object[] entryArgs = getEntryArgs(containerRequestContext, filterContext);
            for (String resourceName : resourceNames) {
                // 增加EntryArgs支持热点参数
                Entry entry = SphU.entry(resourceName, ResourceTypeConstants.COMMON_WEB, EntryType.IN, entryArgs);
                entries.push(entry);
            }
        } catch (BlockException e) {
            String url = containerRequestContext.getUriInfo().getPath();
            // 根据应用，环境、企业判断是否需要阻断请求
            abortRequest = abortRequest(filterContext);
            if (abortRequest) {
                try {
                    log.warn("sentinel abort! URI:{}, ei:{}", url, filterContext.getTenantId(), e);
                    containerRequestContext.abortWith(fallbackResponse(url, e));
                } finally {
                    ContextUtil.exit();
                }
            }
        } finally {
            if (abortRequest) {
                entries.forEach(Entry::exit);
            } else if (CollectionUtils.notEmpty(entries)) {
                containerRequestContext.setProperty(SENTINEL_JAX_RS_PROVIDER_ENTRY_PROPERTY, entries);
                containerRequestContext.setProperty(SENTINEL_JAX_RS_PROVIDER_FILTER_CONTEXT_PROPERTY, filterContext);
            }
        }
    }

    @Override
    public void filter(ContainerRequestContext containerRequestContext, ContainerResponseContext containerResponseContext) throws IOException {
        try {
            List<Entry> entries = (List<Entry>) containerRequestContext.getProperty(SENTINEL_JAX_RS_PROVIDER_ENTRY_PROPERTY);
            if (CollectionUtils.notEmpty(entries)) {
                entries.forEach(Entry::exit);
            }
        } catch (Exception e) {
            log.warn("exit fail!", e);
        } finally {
            containerRequestContext.removeProperty(SENTINEL_JAX_RS_PROVIDER_ENTRY_PROPERTY);
            containerRequestContext.removeProperty(SENTINEL_JAX_RS_PROVIDER_FILTER_CONTEXT_PROPERTY);
            ContextUtil.exit();
        }
    }

    private FilterParamsContext makeFilterContext(ContainerRequestContext requestContext) {
        RequestContext.RequestSource requestSource = getRequestSource(resourceInfo);
        String tenantId = getTenantId(requestContext, requestSource);
        String peerName = getPeerName(requestContext);
        String requestUrl = requestContext.getUriInfo().getRequestUri().getPath();
        return FilterParamsContext.builder()
                .peerName(peerName)
                .tenantId(tenantId)
                .requestSource(Optional.ofNullable(requestSource).map(Enum::name).orElse(null))
                .requestUrl(requestUrl)
                .build();
    }

    private RequestContext.RequestSource getRequestSource(ResourceInfo resourceInfo) {
        Class<?> resourceClass = resourceInfo.getResourceClass();
        if (Objects.nonNull(resourceClass.getAnnotation(CepAPI.class))) {
            return RequestContext.RequestSource.CEP;
        }
        if (Objects.nonNull(resourceClass.getAnnotation(RestAPI.class))) {
            return RequestContext.RequestSource.REST;
        }
        if (Objects.nonNull(resourceClass.getAnnotation(InnerAPI.class))) {
            return RequestContext.RequestSource.INNER;
        }
        return null;
    }

    private Response fallbackResponse(String url, BlockException e) {
        return DEFAULT_SENTINEL_RESTEASY_FALLBACK.fallbackResponse(url, e);
    }

    private boolean abortRequest(FilterParamsContext filterContext) {
        String tenantId = filterContext.getTenantId();
        return SentinelConfig.getInstance().abortRequest(PROFILE, tenantId);
    }


    public Set<String> getResourceNames(ContainerRequestContext requestContext, FilterParamsContext filterContext) {
        String requestUrl = filterContext.getRequestUrl();
        return SentinelConfig.getInstance().getResourceNames(requestUrl);
    }

    protected Object[] getEntryArgs(ContainerRequestContext request, FilterParamsContext filterContext) {
        String tenantId = filterContext.getTenantId();
        String peerName = filterContext.getOrigin();
        String requestUrl = filterContext.getRequestUrl();
        if (!BaseResource.class.isAssignableFrom(resourceInfo.getResourceClass())) {
            return new Object[]{tenantId, peerName + "_" + tenantId, "", "", requestUrl};
        }
        UriInfo uriInfo = request.getUriInfo();
        String firstPathParam = getFirstPathParam(uriInfo);
        String secondPathParam = getSecondPathParam(uriInfo);
        return new Object[]{tenantId, peerName + "_" + tenantId, tenantId + "_" + firstPathParam, tenantId + "_" + firstPathParam + "_" + secondPathParam, requestUrl};
    }

    private String getSecondPathParam(UriInfo uriInfo) {
        MultivaluedMap<String, String> pathParameters = uriInfo.getPathParameters();
        if (pathParameters.containsKey("actionCode")) {
            return pathParameters.getFirst("actionCode");
        }
        if (pathParameters.containsKey("methodName")) {
            return pathParameters.getFirst("methodName");
        }
        if (pathParameters.containsKey("serviceMethod")) {
            return pathParameters.getFirst("serviceMethod");
        }
        return null;
    }

    private String getFirstPathParam(UriInfo uriInfo) {
        MultivaluedMap<String, String> pathParameters = uriInfo.getPathParameters();
        if (pathParameters.containsKey("apiName")) {
            return pathParameters.getFirst("apiName");
        }
        if (pathParameters.containsKey("serviceName")) {
            return pathParameters.getFirst("serviceName");
        }
        return null;
    }

    private String getPeerName(ContainerRequestContext request) {
        return request.getHeaderString(InnerHeaders.PEER_NAME);
    }

    protected String getTenantId(ContainerRequestContext requestContext, RequestContext.RequestSource requestSource) {
        if (RequestContext.RequestSource.CEP == requestSource) {
            return requestContext.getHeaderString(CEPXHeader.TENANT_ID.key());
        }
        return requestContext.getHeaderString(InnerHeaders.TENANT_ID);
    }

    @Data
    @Builder
    public static class FilterParamsContext {
        private final String tenantId;
        private final String peerName;
        private final String requestSource;
        private final String requestUrl;

        public String getOrigin() {
            if (StringUtil.isNotEmpty(peerName)) {
                return peerName;
            }
            if (Objects.equals(RequestContext.RequestSource.CEP.name(), requestSource)) {
                return requestSource;
            }
            return null;
        }
    }
}
