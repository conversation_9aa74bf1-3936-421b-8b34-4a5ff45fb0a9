package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by luxin on 2018/8/7.
 */
public interface UserRole {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class BatchAddUser2RoleArg {
        private String roleCode;
        private List<String> userIds;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class BatchAddUser2RoleResult {
        private Integer Code;
        private String msg;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class BatchAddUser2ForbiddenUserListArg {
        private List<String> userIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class RoleOfUsersArg{
        private List<String> userIds;
    }

}
