package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * IntelliForm Describe object的请求和返回实体
 * Created by Yuanxl on 2018/5/9
 */
public interface IntelliFormDescribe {

    @Data
    class AddFieldArg {

        @JSONField(name = "describe_api_name")
        @JsonProperty(value = "describe_api_name")
        @SerializedName("describe_api_name")
        String describeAPIName;

        @JSONField(name = "field_describe")
        @JsonProperty(value = "field_describe")
        @SerializedName("field_describe")
        String fieldDescribe;
    }

    @Data
    class AddFieldResult {

        @J<PERSON>NField(name = "object_describe")
        @JsonProperty(value = "object_describe")
        @SerializedName("object_describe")
        ObjectDescribeDocument objectDescribeDocument;
    }

    @Data
    class CreateArg {

        @JSONField(name = "json_data")
        @JsonProperty(value = "json_data")
        @SerializedName("json_data")
        String jsonData;

        @JSONField(name = "include_layout")
        @JsonProperty(value = "include_layout")
        @SerializedName("include_layout")
        Boolean includeLayout;

        @JSONField(name = "json_layout")
        @JsonProperty(value = "json_layout")
        @SerializedName("json_layout")
        String jsonLayout;

        @JSONField(name = "is_show_in_menu")
        @JsonProperty(value = "is_show_in_menu")
        @SerializedName("is_show_in_menu")
        Boolean isShowInMenu;

        @JSONField(name = "json_list_layout")
        @JsonProperty(value = "json_list_layout")
        @SerializedName("json_list_layout")
        String jsonListLayout;
    }

    @Data
    class CreateResult {

        @JSONField(name = "object_describe")
        @JsonProperty(value = "object_describe")
        @SerializedName("object_describe")
        ObjectDescribeDocument objectDescribeDocument;

        @JSONField(name = "layout")
        @JsonProperty(value = "layout")
        @SerializedName("layout")
        LayoutDocument layoutDocument;
    }

    @Data
    class DetailArg {

        @JSONField(name = "describe_api_name")
        @JsonProperty(value = "describe_api_name")
        @SerializedName("describe_api_name")
        String describeAPIName;

        @JSONField(name = "is_exclude_describe")
        @JsonProperty(value = "is_exclude_describe")
        @SerializedName("is_exclude_describe")
        Boolean isExcludeDescribe;
    }

    @Data
    class DetailResult {

        @JSONField(name = "object_describe")
        @JsonProperty(value = "object_describe")
        @SerializedName("object_describe")
        ObjectDescribeDocument objectDescribeDocument;

        @JSONField(name = "layout")
        @JsonProperty(value = "layout")
        @SerializedName("layout")
        LayoutDocument layoutDocument;
    }

    @Data
    class DisableArg {

        @JSONField(name = "describe_api_name")
        @JsonProperty(value = "describe_api_name")
        @SerializedName("describe_api_name")
        String describeAPIName;
    }


    @Data
    class DisableResult {

        @JSONField(name = "object_describe")
        @JsonProperty(value = "object_describe")
        @SerializedName("object_describe")
        ObjectDescribeDocument objectDescribeDocument;
    }

    @Data
    class FindByIdArg {

        @JSONField(name = "describe_id")
        @JsonProperty(value = "describe_id")
        @SerializedName("describe_id")
        String describeId;
    }

    @Data
    class FindByIdResult {

        @JSONField(name = "object_describe")
        @JsonProperty(value = "object_describe")
        @SerializedName("object_describe")
        ObjectDescribeDocument objectDescribeDocument;
    }

    @Data
    class PageInfo {
        private Integer pageCount;
        private Integer pageNumber;
        private Integer pageSize;
        private Integer totalCount;
        private Integer totalPage;

        public void setTotalCountAndTotalPage(Integer totalCount) {
            this.totalCount = totalCount;
            this.totalPage = totalCount % this.getPageSize() == 0 ? totalCount / this.getPageSize() : totalCount / this
                    .getPageSize() + 1;
        }
    }

    @Data
    class FindByPageArg {

        PageInfo page = new PageInfo();

        @JSONField(name = "orderBy")
        @JsonProperty(value = "orderBy")
        @SerializedName("orderBy")
        String orderBy = "create_time"; //还可使用last_modify_time

        @JSONField(name = "isAsc")
        @JsonProperty(value = "isAsc")
        @SerializedName("isAsc")
        boolean isAsc; //默认false

        @JSONField(name = "isActive")
        @JsonProperty(value = "isActive")
        @SerializedName("isActive")
        Boolean isActive = null;

        @JSONField(name = "defineType")
        @JsonProperty(value = "defineType")
        @SerializedName("defineType")
        String defineType = null;

        public Map<String, Object> transToPGQueryInfo() {
            Map<String, Object> queryInfo = Maps.newHashMap();
            queryInfo.put("is_active", this.isActive);
            queryInfo.put("limit", this.getPage().getPageSize());
            queryInfo.put("offset", (this.getPage().getPageNumber() - 1) * this.getPage().getPageSize());
            queryInfo.put("orderby", this.getOrderBy());
            queryInfo.put("is_asc", this.isAsc());
            queryInfo.put("define_type", this.getDefineType());

            return queryInfo;
        }
    }

    @Data
    class FindByPageResult {

        @JSONField(name = "describes")
        @JsonProperty(value = "describes")
        @SerializedName("describes")
        List<ObjectDescribeDocument> objectDescribeDocumentList;

        @JSONField(name = "page")
        @JsonProperty(value = "page")
        @SerializedName("pages")
        private PageInfo page;
    }

    @Data
    class ReplaceArg {

        @JSONField(name = "json_data")
        @JsonProperty(value = "json_data")
        @SerializedName("json_data")
        String data;
    }

    @Data
    class ReplaceResult {

        @JSONField(name = "object_describe")
        @JsonProperty(value = "object_describe")
        @SerializedName("object_describe")
        ObjectDescribeDocument objectDescribeDocument;
    }

}
