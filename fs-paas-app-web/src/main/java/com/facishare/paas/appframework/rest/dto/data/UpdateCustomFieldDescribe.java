package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/1/14 5:38 PM
 */
public interface UpdateCustomFieldDescribe {
    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String fieldDescribeJson;
        private String fieldApiName;
        private String describeApiName;
    }

    @Data
    class Result {
        @JSONField(name = "describe")
        @JsonProperty(value = "describe")
        @SerializedName("describe")
        ObjectDescribeDocument objectDescribeDocument;
    }
}
