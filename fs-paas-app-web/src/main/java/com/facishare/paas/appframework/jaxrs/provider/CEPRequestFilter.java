package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.rest.CEPXHeader;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.jaxrs.annotation.CepAPI;
import com.google.common.base.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Priority;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.ext.Provider;

/**
 * 从Request请求上下文中获取用户身份
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
@CepAPI
@Priority(100)
@Provider
@Component
public class CEPRequestFilter extends AbstractRequestFilter {

    protected RequestContext getRequestContext(ContainerRequestContext context) {

        String tenantId = context.getHeaderString(CEPXHeader.TENANT_ID.key());
        String employeeId = context.getHeaderString(CEPXHeader.USER_ID.key());
        String outTenantId = context.getHeaderString(CEPXHeader.OUT_TENANT_ID.key());
        String outUserId = context.getHeaderString(CEPXHeader.OUT_USER_ID.key());
        String appId = context.getHeaderString(CEPXHeader.APP_ID.key());
        String outLinkType = context.getHeaderString(CEPXHeader.OUT_LINK_TYPE.key());
        String postId = context.getHeaderString(CEPXHeader.POST_ID.key());
        String clientInfo = context.getHeaderString(CEPXHeader.CLIENT_INFO.key());
        String localeInfo = context.getHeaderString(CEPXHeader.LOCALE.key());
        String clientIp = context.getHeaderString(CEPXHeader.CLIENT_IP.key());
        String upstreamOwnerId = context.getHeaderString(CEPXHeader.UPSTREAM_OWNER_ID.key());
        String ea = context.getHeaderString(CEPXHeader.ENTERPRISE_ACCOUNT.key());
        String bizId = context.getHeaderString(CEPXHeader.BIZ_ID.key());
        String sessionId = context.getHeaderString(InnerHeaders.SESSION_ID);
        String outIdentityType = context.getHeaderString(CEPXHeader.OUT_IDENTITY_TYPE.key());
        String thirdAppId = context.getHeaderString(CEPXHeader.THIRD_APP_ID.key());
        String thirdUserId = context.getHeaderString(CEPXHeader.THIRD_USER_ID.key());
        String thirdType = context.getHeaderString(CEPXHeader.THIRD_TYPE.key());
        String deviceType = context.getHeaderString(CEPXHeader.FS_DEVICE_TYPE.key());
        String deviceId = context.getHeaderString(CEPXHeader.FS_DEVICE_ID.key());
        String marketingMemberId = context.getHeaderString(CEPXHeader.FS_MARKETING_MEMBER.key());

        User user = User.builder()
                .tenantId(tenantId)
                .userId(employeeId)
                .outTenantId(outTenantId)
                .outUserId(outUserId)
                .upstreamOwnerId(upstreamOwnerId)
                .build();

        RequestContext.ContentType contentType =
                RequestContext.ContentType.getType(context.getHeaderString(HttpHeaders.CONTENT_TYPE));

        RequestContext ret = RequestContext.builder()
                .requestSource(RequestContext.RequestSource.CEP)
                .peerName(RequestContext.RequestSource.CEP.name())
                .x_peerName(RequestContext.RequestSource.CEP.name())
                .contentType(contentType)
                .tenantId(tenantId)
                .user(user)
                .appId(appId)
                .outLinkType(outLinkType)
                .postId(postId)
                .clientInfo(clientInfo)
                .lang(Lang.of(localeInfo))
                .requestUri(context.getUriInfo().getPath())
                .clientIp(clientIp)
                .upstreamOwnerId(upstreamOwnerId)
                .ea(ea)
                .bizId(bizId)
                .batch(false)
                .sessionId(sessionId)
                .outIdentityType(outIdentityType)
                .thirdAppId(thirdAppId)
                .thirdUserId(thirdUserId)
                .thirdType(thirdType)
                .build();

        if (!Strings.isNullOrEmpty(deviceType)) {
            ret.setAttribute(CEPXHeader.FS_DEVICE_TYPE.key(), deviceType);
        }
        if (!Strings.isNullOrEmpty(deviceId)) {
            ret.setAttribute(CEPXHeader.FS_DEVICE_ID.key(), deviceId);
        }
        if (!Strings.isNullOrEmpty(marketingMemberId)) {
            ret.setAttribute(CEPXHeader.FS_MARKETING_MEMBER.key(), marketingMemberId);
        }
        return ret;
    }

}
