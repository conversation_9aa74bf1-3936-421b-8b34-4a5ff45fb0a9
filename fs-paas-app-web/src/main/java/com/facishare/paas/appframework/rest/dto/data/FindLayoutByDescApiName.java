package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface FindLayoutByDescApiName {

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        String descApiName;
        private String appId;
    }

    class Result extends CommonResult.LayoutListResult {}
}
