package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.paas.appframework.core.rest.APIResult;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.jaxrs.model.InnerAPIResult;
import com.facishare.paas.appframework.jaxrs.model.RestAPIResult;
import org.springframework.stereotype.Component;

import javax.annotation.Priority;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerResponseContext;
import javax.ws.rs.container.ContainerResponseFilter;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.Provider;
import java.util.Objects;

@RestAPI
@Priority(100)
@Provider
@Component
public class RestResponseFilter implements ContainerResponseFilter {
    @Override
    public void filter(ContainerRequestContext requestContext, ContainerResponseContext responseContext) {
        Object entity = responseContext.getEntity();
        if (responseContext.getStatusInfo() == Response.Status.TOO_MANY_REQUESTS) {
            String message = Objects.isNull(entity) ? "Blocked by flow limiting" : entity.toString();
            responseContext.setEntity(RestAPIResult.fail(Response.Status.TOO_MANY_REQUESTS.getStatusCode(), message));
            return;
        }
        if (Objects.isNull(entity)) {
            responseContext.setEntity(RestAPIResult.success(null));
        } else if (entity instanceof APIResult) {
            responseContext.setEntity(RestAPIResult.from((APIResult) entity));
        } else if (entity instanceof InnerAPIResult) {
            responseContext.setEntity(RestAPIResult.from((InnerAPIResult) entity));
        } else if (!(entity instanceof RestAPIResult)) {
            responseContext.setEntity(RestAPIResult.success(entity));
        }
    }
}
