package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.TeamMemberInfoPoJo;
import com.facishare.paas.appframework.rest.dto.data.FindTeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class DefObjectRestService {

    @Autowired
    ServiceFacade serviceFacade;

    public FindTeamMember.Result getTeamMember(FindTeamMember.Arg arg, RequestContext requestContext) {
        FindTeamMember.Result result = new FindTeamMember.Result();

        List<TeamMemberInfoPoJo> teamMemberInfoPoJos = getTeamMemberList(arg, requestContext);
        result.setTeamMemberInfoPoJos(teamMemberInfoPoJos);
        return result;
    }

    @SuppressWarnings("unchecked")
    private List<TeamMemberInfoPoJo> getTeamMemberList(FindTeamMember.Arg arg, RequestContext requestContext) {
        IObjectData objectData = serviceFacade.findObjectDataIgnoreFormula(requestContext.getUser(),
                arg.getDataId(), arg.getDescribeAPIName());
        List<TeamMemberInfoPoJo> result = Lists.newArrayList();
        List<TeamMember> teamMembers = ObjectDataExt.of(objectData).getTeamMembers();
        if (CollectionUtils.notEmpty(teamMembers)) {
            teamMembers.forEach(teamMemberInfo ->
                    result.add(TeamMemberInfoPoJo.builder()
                            .teamMemberEmployee(teamMemberInfo.getEmployeeList())
                            .teamMemberRole(teamMemberInfo.getRoleCode())
                            .teamMemberPermissionType(teamMemberInfo.getPermission().getValue())
                            .teamMemberType(teamMemberInfo.getMemberType().getValue())
                            .sourceType(teamMemberInfo.getSourceType())
                            .outTenantId(teamMemberInfo.getOutTenantId())
                            .build()
                    ));
        }
        Collections.sort(result);
        return result;
    }
}

