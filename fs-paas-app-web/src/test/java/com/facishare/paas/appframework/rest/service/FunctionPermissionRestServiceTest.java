package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.FuncCodePrivilege;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 功能权限服务单元测试
 */
@ExtendWith(MockitoExtension.class)
@Timeout(3)
class FunctionPermissionRestServiceTest {

    @Mock
    private FunctionPrivilegeProxy functionPrivilegeProxy;

    @InjectMocks
    private FunctionPermissionRestService functionPermissionRestService;

    private RequestContext requestContext;
    private User user;

    @BeforeEach
    void setUp() {
        user = new User("74255", "1000");
        requestContext = RequestContext.builder()
                .user(user)
                .tenantId("74255")
                .appId("testAppId")
                .build();
    }

    @Test
    void testGetHaveListPermissionRolesByApiNames_Success() {
        // Given
        List<String> apiNames = Lists.newArrayList("api1", "api2");

        Map<String, List<String>> funcCodeRoleMapping = Maps.newHashMap();
        funcCodeRoleMapping.put("api1", Lists.newArrayList("role1", "role2"));
        funcCodeRoleMapping.put("api2", Lists.newArrayList("role3"));

        FuncCodePrivilege.Result mockResult = new FuncCodePrivilege.Result();
        mockResult.setSuccess(true);
        mockResult.setResult(funcCodeRoleMapping);

        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(mockResult);

        // When
        FuncCodePrivilege.Result result = functionPermissionRestService.getHaveListPermissionRolesByApiNames(apiNames, requestContext);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getResult());
        assertEquals(2, result.getResult().size());
        assertTrue(result.getResult().containsKey("api1"));
        assertTrue(result.getResult().containsKey("api2"));
        assertEquals(Lists.newArrayList("role1", "role2"), result.getResult().get("api1"));
        assertEquals(Lists.newArrayList("role3"), result.getResult().get("api2"));

        verify(functionPrivilegeProxy, times(1)).getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap());
    }

    @Test
    void testGetHaveListPermissionRolesByApiNames_EmptyApiNames() {
        // Given
        List<String> apiNames = Collections.emptyList();

        // When
        FuncCodePrivilege.Result result = functionPermissionRestService.getHaveListPermissionRolesByApiNames(apiNames, requestContext);

        // Then
        assertNull(result);
        verify(functionPrivilegeProxy, never()).getHaveFuncCodesPrivilegeRoles(any(), anyMap());
    }

    @ParameterizedTest
    @NullSource
    void testGetHaveListPermissionRolesByApiNames_NullApiNames(List<String> apiNames) {
        // When
        FuncCodePrivilege.Result result = functionPermissionRestService.getHaveListPermissionRolesByApiNames(apiNames, requestContext);

        // Then
        assertNull(result);
        verify(functionPrivilegeProxy, never()).getHaveFuncCodesPrivilegeRoles(any(), anyMap());
    }

    @Test
    void testGetHaveListPermissionRolesByApiNames_ProxyReturnsFailure() {
        // Given
        List<String> apiNames = Lists.newArrayList("api1");

        FuncCodePrivilege.Result mockResult = new FuncCodePrivilege.Result();
        mockResult.setSuccess(false);
        mockResult.setResult(null);

        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(mockResult);

        // When
        FuncCodePrivilege.Result result = functionPermissionRestService.getHaveListPermissionRolesByApiNames(apiNames, requestContext);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNull(result.getResult());
    }

    @Test
    void testGetHaveListPermissionRolesByApiNames_AuthContextCreation() {
        // Given
        List<String> apiNames = Lists.newArrayList("api1");

        FuncCodePrivilege.Result mockResult = new FuncCodePrivilege.Result();
        mockResult.setSuccess(true);
        mockResult.setResult(Maps.newHashMap());

        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(mockResult);

        // When
        functionPermissionRestService.getHaveListPermissionRolesByApiNames(apiNames, requestContext);

        // Then
        verify(functionPrivilegeProxy, times(1)).getHaveFuncCodesPrivilegeRoles(
                argThat(arg -> {
                    AuthContext context = arg.getAuthContext();
                    return context.getTenantId().equals("74255") &&
                            context.getUserId().equals("1000") &&
                            context.getAppId().equals("testAppId");
                }),
                anyMap()
        );
    }

    @Test
    void testGetHaveListPermissionRolesByApiNames_HeadersCreation() {
        // Given
        List<String> apiNames = Lists.newArrayList("api1");

        FuncCodePrivilege.Result mockResult = new FuncCodePrivilege.Result();
        mockResult.setSuccess(true);
        mockResult.setResult(Maps.newHashMap());

        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(mockResult);

        // When
        functionPermissionRestService.getHaveListPermissionRolesByApiNames(apiNames, requestContext);

        // Then
        verify(functionPrivilegeProxy, times(1)).getHaveFuncCodesPrivilegeRoles(
                any(FuncCodePrivilege.Arg.class),
                anyMap()
        );
    }

    @ParameterizedTest
    @ValueSource(strings = {"api1", "api2", "api3"})
    void testGetHaveListPermissionRolesByApiNames_SingleApiName(String apiName) {
        // Given
        List<String> apiNames = Lists.newArrayList(apiName);

        Map<String, List<String>> funcCodeRoleMapping = Maps.newHashMap();
        funcCodeRoleMapping.put(apiName, Lists.newArrayList("role1"));

        FuncCodePrivilege.Result mockResult = new FuncCodePrivilege.Result();
        mockResult.setSuccess(true);
        mockResult.setResult(funcCodeRoleMapping);

        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(mockResult);

        // When
        FuncCodePrivilege.Result result = functionPermissionRestService.getHaveListPermissionRolesByApiNames(apiNames, requestContext);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getResult().size());
        assertTrue(result.getResult().containsKey(apiName));
    }

    @Test
    void testGetHaveListPermissionRolesByApiNames_NullRequestContext() {
        // Given
        List<String> apiNames = Lists.newArrayList("api1");

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            functionPermissionRestService.getHaveListPermissionRolesByApiNames(apiNames, null);
        });
    }


    @Test
    void testGetHaveListPermissionRolesByApiNames_LargeApiNamesList() {
        // Given
        List<String> apiNames = Lists.newArrayList();
        for (int i = 0; i < 100; i++) {
            apiNames.add("api" + i);
        }

        Map<String, List<String>> funcCodeRoleMapping = Maps.newHashMap();
        for (String apiName : apiNames) {
            funcCodeRoleMapping.put(apiName, Lists.newArrayList("role1"));
        }

        FuncCodePrivilege.Result mockResult = new FuncCodePrivilege.Result();
        mockResult.setSuccess(true);
        mockResult.setResult(funcCodeRoleMapping);

        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(mockResult);

        // When
        FuncCodePrivilege.Result result = functionPermissionRestService.getHaveListPermissionRolesByApiNames(apiNames, requestContext);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(100, result.getResult().size());
    }
}
