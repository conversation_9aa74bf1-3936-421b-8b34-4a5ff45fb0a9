package com.facishare.paas.appframework.common.service.model;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/7/8
 * @Description :
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FunctionPluginConfig {
    @Builder.Default
    private Set<String> showModuleName = Sets.newHashSet(
            "Export", "ExportExcelTemplate",
            "List", "ListHeader", "RelatedList", "SearchList", "WebDetail", "WhatList"
    );
    @Builder.Default
    private Set<String> executedModuleName = Sets.newHashSet(
            "Export", "ExportExcelTemplate",
            "List", "ListHeader", "RelatedList", "SearchList", "WebDetail", "WhatList", "DuplicateSearch"
    );

    public static final FunctionPluginConfig DEFAULT = FunctionPluginConfig.builder().build();
}
