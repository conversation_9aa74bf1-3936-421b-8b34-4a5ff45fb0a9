package com.facishare.paas.appframework.common.util;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
public abstract class FieldManyMaxConfig {

    /**
     *  delimiter
     */
    public static String VERTICAL = "|";

    /**
     * empMany and objMany default limit
     */
    public static int LIMIT = 10;


    //empMany field max limit
    private static Map<String, Integer> GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME_VERTICAL_TENANT = Maps.newHashMap();
    private static Map<String, Integer> GRAY_EMPLOYEE_MANY_MAX_LIMIT_TENANT = Maps.newHashMap();
    private static Map<String, Integer> GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME = Maps.newHashMap();

    //objMany field max limit
    private static Map<String, Integer> GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_API_NAME_VERTICAL_TENANT = Maps.newHashMap();
    private static Map<String, Integer> GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_TENANT = Maps.newHashMap();
    private static Map<String, Integer> GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_API_NAME = Maps.newHashMap();


    /**
     * FieldManyMaxConfigItem的配置文件可能只配置了employeeManyLimit或objectReferenceManyLimit，原程序会将另一个没有配置的设为10，
     * 导致后面值给某租户配置了其中一项却将另一项配置给覆盖了的问题
     * 修复过程：
     * 1. employeeManyLimit,objectReferenceManyLimit取消初始值，即初始值为0
     * 2. 如果employeeManyLimit或者objectReferenceManyLimit值为0，则不进行赋值操作
     *
     * 历史逻辑梳理：
     * if tenantId not null
     *      if employeeManyLimit>0
     *          tenantId:employeeManyLimit put GRAY_EMPLOYEE_MANY_MAX_LIMIT_TENANT
     *          @see GRAY_EMPLOYEE_MANY_MAX_LIMIT_TENANT
     *      if objectReferenceManyLimit>0
     *          tenantId:objectReferenceManyLimit put GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_TENANT
     *          @see GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_TENANT
     * else apiName not null
     *      apiName:employeeManyLimit put GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME
     *          @see GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME
     *
     *
     *新逻辑：
     * if employeeManyLimit>0
     *      if tenantId not empty and apiName not empty
     *          apiName|tenantId:employeeManyLimit put GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME_VERTICAL_TENANT
     *          @see GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME_VERTICAL_TENANT new
     *      if tenantId not empty
     *          tenantId:employeeManyLimit put GRAY_EMPLOYEE_MANY_MAX_LIMIT_TENANT
     *          @see GRAY_EMPLOYEE_MANY_MAX_LIMIT_TENANT
     *      if apiName not empty
     *          apiName:employeeManyLimit put GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME
     *          @see GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME
     *
     *  if objectReferenceManyLimit>0
     *      if tenantId not empty and apiName not empty
     *          apiName|tenantId:objectReferenceManyLimit put
     *              @see GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_API_NAME_VERTICAL_TENANT new
     *      if tenantId not empty
     *          tenantId:objectReferenceManyLimit put
     *              @see GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_TENANT
     *      if apiName not empty
     *          apiName:objectReferenceManyLimit put
     *              @see GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_API_NAME new
     *
     *原则：
     *  api|tenant>tenant>api
     */
    static {
        ConfigFactory.getConfig("fs-paas-appframework-employee-many-config", config -> {
            String content = config.getString();
            log.warn("reload config fs-paas-appframework-employee-many-config,content:{}", content);
            load(content);
        });
    }

    private static void load(String content) {
        List<FieldManyMaxConfigItem> items = getEmployeeManyMaxConfigItem(content);
        if (items != null) {
            Map<String, Integer> empManyObjVerticalTenant = Maps.newHashMap();
            Map<String, Integer> empManyOnlyTenant = Maps.newHashMap();
            Map<String, Integer> empManyOnlyObj = Maps.newHashMap();
            Map<String, Integer> objManyObjVerticalTenant = Maps.newHashMap();
            Map<String, Integer> objManyOnlyTenant = Maps.newHashMap();
            Map<String, Integer> objManyOnlyObj = Maps.newHashMap();


            items.forEach(item -> {
                if (item == null) {
                    return;
                }
                if (0 < item.getEmployeeManyLimit()) {
                    if (CollectionUtils.notEmpty(item.getTenantId()) && StringUtils.isNotEmpty(item.getApiName())) {
                        item.getTenantId().forEach(ei -> empManyObjVerticalTenant.put(item.getApiName() + "|" + ei, item.getEmployeeManyLimit()));
                    } else if (CollectionUtils.notEmpty(item.getTenantId())) {
                        item.getTenantId().forEach(tenantId -> empManyOnlyTenant.put(tenantId, item.getEmployeeManyLimit()));
                    } else if (StringUtils.isNotEmpty(item.getApiName())) {
                        empManyOnlyObj.put(item.getApiName(), item.getEmployeeManyLimit());
                    }
                }

                if (0 < item.getObjectReferenceManyLimit()) {
                    if (CollectionUtils.notEmpty(item.getTenantId()) && StringUtils.isNotEmpty(item.getApiName())) {
                        item.getTenantId().forEach(ei -> objManyObjVerticalTenant.put(item.getApiName() + "|" + ei, item.getObjectReferenceManyLimit()));
                    } else if (CollectionUtils.notEmpty(item.getTenantId())) {
                        item.getTenantId().forEach(tenantId -> objManyOnlyTenant.put(tenantId, item.getObjectReferenceManyLimit()));
                    } else if (StringUtils.isNotEmpty(item.getApiName())) {
                        objManyOnlyObj.put(item.getApiName(), item.getObjectReferenceManyLimit());
                    }
                }
            });
            GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME_VERTICAL_TENANT = empManyObjVerticalTenant;
            GRAY_EMPLOYEE_MANY_MAX_LIMIT_TENANT = empManyOnlyTenant;
            GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME = empManyOnlyObj;
            GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_API_NAME_VERTICAL_TENANT = objManyObjVerticalTenant;
            GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_TENANT = objManyOnlyTenant;
            GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_API_NAME = objManyOnlyObj;
        }
    }

    public static String buildApiNameDelimiterTenant(String apiName, String tenantId) {
        return apiName + VERTICAL + tenantId;
    }

    public static List<FieldManyMaxConfigItem> getEmployeeManyMaxConfigItem(String content) {
        try {
            List<FieldManyMaxConfigItem> items = JSON.parseArray(content, FieldManyMaxConfigItem.class);
            return items;
        } catch (Throwable throwable) {
            log.error("getEmployeeManyMaxConfigItem error:{}", throwable.getMessage(), throwable);
        }
        return null;
    }

    /**
     * 历史逻辑梳理：1.先按objApi找limit，找到返回; 2.没有找到按照tenantId找limit，找到返回; 3.还没有找到返回limit=10
     *
     * 新逻辑：0.先按objApi|tenantId找，找到返回tenantLimit; 1.按objApi找limit，找到返回; 2.没有找到按照tenantId找limit，找到返回; 3.还没有找到返回limit=10
     * @param tenantId 租户ID
     * @param apiName 对象API Name
     * @return 人员（多选）字段最大可选项
     */
    public static int getEmployeeManyMaxLimit(String tenantId, String apiName) {

        return ObjectUtils.firstNonNull(
                GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME_VERTICAL_TENANT.get(buildApiNameDelimiterTenant(apiName, tenantId)),
                GRAY_EMPLOYEE_MANY_MAX_LIMIT_API_NAME.get(apiName),
                GRAY_EMPLOYEE_MANY_MAX_LIMIT_TENANT.get(tenantId),
                LIMIT);
    }

    public static int getObjectReferenceManyMaxLimit(String tenantId, String apiName) {
        return ObjectUtils.firstNonNull(
                GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_API_NAME_VERTICAL_TENANT.get(buildApiNameDelimiterTenant(apiName, tenantId)),
                GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_API_NAME.get(apiName),
                GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_TENANT.get(tenantId),
                LIMIT);
    }

    /**
     * @see FieldManyMaxConfig#getObjectReferenceManyMaxLimit(String, String)
     */
    @Deprecated
    public static int getObjectReferenceManyMaxLimit(String tenantId) {
        Integer limit = GRAY_OBJECT_REFERENCE_MANY_MAX_LIMIT_TENANT.get(tenantId);
        if (limit != null) {
            return limit;
        }
        return 10;
    }

    public static String outBoundsForManyField(String fieldType, String fieldLabel, Object value, IObjectDescribe describe) {
        if (Objects.isNull(value) || !StringUtils.equalsAny(fieldType, IFieldType.EMPLOYEE_MANY, IFieldType.DEPARTMENT_MANY, IFieldType.OBJECT_REFERENCE_MANY)) {
            return "";
        }

        if (!(value instanceof List)) {
            throw new ValidateException(I18NExt.text(I18NKey.VALUE_TYPE_ERROR, fieldLabel));
        }
        int maxCount;

        switch (fieldType) {
            case IFieldType.EMPLOYEE_MANY:
            case IFieldType.DEPARTMENT_MANY:
                maxCount = getEmployeeManyMaxLimit(describe.getTenantId(), describe.getApiName());
                break;
            case IFieldType.OBJECT_REFERENCE_MANY:
                maxCount = getObjectReferenceManyMaxLimit(describe.getTenantId(), describe.getApiName());
                break;
            default:
                maxCount = LIMIT;
        }
        if (maxCount < ((List<?>) value).size()) {
            return I18NExt.text(I18NKey.MANY_FIELD_LABEL_APPEND_MAX_COUNT, fieldLabel, maxCount);
        }
        return "";
    }


}
