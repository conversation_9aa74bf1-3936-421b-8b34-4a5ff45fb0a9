package com.facishare.paas.appframework.flow.dto;

import lombok.Builder;
import lombok.Data;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/12/03
 */
public interface ApprovalFlowDefinition {
    @Data
    class Result {
        private int code;
        private String message;
        private boolean data;

        public boolean isExist() {
            return data;
        }

        public boolean isSuccess() {
            return code == 0;
        }
    }

    @Data
    @Builder
    class Arg {
        private String entityId;
        private String triggerType;
    }
}
