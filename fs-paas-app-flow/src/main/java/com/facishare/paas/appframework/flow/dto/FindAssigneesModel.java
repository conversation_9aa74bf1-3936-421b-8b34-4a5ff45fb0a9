
package com.facishare.paas.appframework.flow.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by yusb on 2017/9/13.
 */
public interface FindAssigneesModel {

  @Data
  @Builder()
  class Arg {
    AuthContext context;
    String entityId;
    List<String> objectIds;
  }

  @Data
  class Result {
    int errCode;//错误码0 || 200是正常的
    String errKey;//错误码英文说明
    String errMessage;//错误信息（由业务端生成）
    String errDescription;//错误码中文说明
    List<FindAssignees> result;

    public boolean isSuccess(){
      return errCode == 0 || errCode == 200;
    }

    public Set<String> getAllAssignees(){
      if(result == null){
        return Collections.emptySet();
      }
      Set<String> ret = result.stream()
              .map(x->x.getAssignees()).flatMap(y->y.stream()).collect(Collectors.toSet());
      return ret;
    }
  }

  @Data
  public class FindAssignees{
    String tenantId;
    String entityId;
    String objectId;
    List<String> assignees;
  }
}