package com.facishare.paas.appframework.flow;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.flow.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service("flowCommonServiceImpl")
public class FlowCommonServiceImpl implements FlowCommonService {

    @Autowired
    private FlowCommonProxy flowCommonProxy;

    @Override
    public ExistAvailableDef.ExistAvailable existAvailableDef(User user, String describeApiName, String dataId) {

        Map<String, String> header = buildHeaders(user);
        ExistAvailableDef.Arg arg = ExistAvailableDef.Arg.builder()
                .entityId(describeApiName)
                .objectId(dataId)
                .build();

        ExistAvailableDef.ExistAvailable existAvailable = ExistAvailableDef.ExistAvailable.builder()
                .existAvailableBpmDef(false)
                .existAvailableStageDef(false)
                .build();
        try {
            ExistAvailableDef.Result result = flowCommonProxy.existAvailableDef(arg, header);
            if (result.getCode() == 0) {
                existAvailable = result.getData();
            } else {
                log.warn("existAvailableDef warn, header:{}, arg:{}, result:{}", JSON.toJSONString(header), JSON.toJSONString(arg), JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("existAvailableDef error, header:{}, arg:{}", JSON.toJSONString(header), JSON.toJSONString(arg), e);
        }

        return existAvailable;
    }

    private Map<String, String> buildHeaders(User user) {
        Map<String, String> ret = RestUtils.buildHeaders(user);
        ret.put("x-tenant-id", String.valueOf(user.getTenantId()));
        ret.put("x-user-id", user.getUserIdWithFlowGray());
        return ret;
    }


    @Override
    public OneFlowExecuteResponse executeFlow(User user, OneFlowExecuteRequest request) {
        try {
            TriggerFlow.Arg triggerArg = convertToTriggerArg(request);
            log.info("Start calling WorkFlow service, request parameters: {}", triggerArg);

            // Build request headers
            Map<String, String> headers = buildHeaders(user);

            // Call fs-paas-workflow service
            TriggerFlow.Result response = flowCommonProxy.triggerFlow(triggerArg, headers);
            log.info("WorkFlow service call completed, response result: {}", response);

            return convertToExecuteResponse(response);
        } catch (Exception e) {
            log.error("Exception calling WorkFlow service", e);
            throw e;
        }
    }

    /**
     * 将OneFlow执行请求转换为触发流程请求参数
     *
     * @param request OneFlow执行请求
     * @return 触发流程请求参数
     */
    private TriggerFlow.Arg convertToTriggerArg(OneFlowExecuteRequest request) {
        FlowContext context = new FlowContext();
        context.setTenantId(String.valueOf(request.getTenantId()));
        context.setUserId(String.valueOf(request.getUserId()));

        FlowSource source = new FlowSource();
        source.setBusinessId(UUID.randomUUID().toString());
        source.setBusinessType("button");

        return TriggerFlow.Arg.builder()
                .sourceWorkflowId(request.getFlowApiName())
                .inputs(request.getFlowArgs())
                .async(request.isAsync())
                .context(context)
                .source(source)
                .build();
    }

    /**
     * 将触发流程响应转换为OneFlow执行响应
     *
     * @param result 触发流程响应结果
     * @return OneFlow执行响应
     */
    private OneFlowExecuteResponse convertToExecuteResponse(TriggerFlow.Result result) {
        OneFlowExecuteResponse executeResponse = new OneFlowExecuteResponse();
        if (result.getCode() == 0 && result.getData() != null) {
            TriggerFlow.FlowData data = result.getData();
            Map<String, Object> currentTask = CollectionUtils.nullToEmpty(data.getCurrentTask());
            if (StringUtils.equals(String.valueOf(currentTask.get("state")), "error")) {
                throw new ValidateException(I18NExt.text(I18NKey.ONE_FLOW_FAIL));
            }
            executeResponse.setWorkflowInstanceId(data.getWorkflowInstanceId());
        } else {
            log.warn("Failed to trigger workflow, response code: {}, response message: {}", result.getCode(), result.getMessage());
            throw new ValidateException(result.getMessage());
        }
        return executeResponse;
    }

    @Override
    public QueryOneFlowDefinition.SimpleDefinitionData queryOneFlowDefinitionSimpleList(User user, List<String> sourceWorkflowIds) {
        try {
            if (!CollectionUtils.notEmpty(sourceWorkflowIds)) {
                return QueryOneFlowDefinition.SimpleDefinitionData.builder()
                        .simpleDefinitions(Collections.emptyList())
                        .build();
            }

            QueryOneFlowDefinition.Arg arg = QueryOneFlowDefinition.Arg.builder()
                    .sourceWorkflowIds(sourceWorkflowIds)
                    .build();

            Map<String, String> headers = buildHeaders(user);
            QueryOneFlowDefinition.Result result = flowCommonProxy.queryOneFlowDefinitionSimpleList(arg, headers);

            if (result.isSuccess() && result.getData() != null) {
                return result.getData();
            } else {
                log.warn("Failed to query OneFlow definition simple list, sourceWorkflowIds: {}, response: {}", sourceWorkflowIds, JSON.toJSONString(result));
                return QueryOneFlowDefinition.SimpleDefinitionData.builder()
                        .simpleDefinitions(Collections.emptyList())
                        .build();
            }
        } catch (Exception e) {
            log.error("Exception querying OneFlow definition simple list, sourceWorkflowIds: {}", sourceWorkflowIds, e);
            return QueryOneFlowDefinition.SimpleDefinitionData.builder()
                    .simpleDefinitions(Collections.emptyList())
                    .build();
        }
    }
}
