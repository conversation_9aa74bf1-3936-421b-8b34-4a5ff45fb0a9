package com.facishare.paas.appframework.license.quota.checker;

import com.facishare.paas.appframework.license.util.ModulePara;
import lombok.Builder;

import java.util.Map;

public class LayoutRuleQuota<PERSON>hecker extends BaseQuotaChecker {
    @Builder
    public LayoutRuleQuotaChecker(Map<String, Integer> paraMap) {
        super(paraMap);
    }

    @Override
    public int getMaxCount(QuotaChecker.QuotaInfo quotaInfo) {
        return paraMap.getOrDefault(ModulePara.LAYOUT_RULES_LIMIT.getParaKey(), 0);
    }

    @Override
    public boolean check(QuotaInfo quotaInfo) {
        return getMaxCount(quotaInfo) >= quotaInfo.getActualCount();
    }
}
