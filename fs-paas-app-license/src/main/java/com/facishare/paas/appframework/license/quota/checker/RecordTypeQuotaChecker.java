package com.facishare.paas.appframework.license.quota.checker;

import com.facishare.paas.appframework.license.util.ModulePara;
import lombok.Builder;

import java.util.Map;

public class RecordTypeQuotaChecker extends BaseQuotaChecker{
    @Builder
    public RecordTypeQuotaChecker(Map<String, Integer> paraMap) {
        super(paraMap);
    }

    @Override
    public int getMaxCount(QuotaChecker.QuotaInfo quotaInfo) {
        return paraMap.getOrDefault(ModulePara.CUSTOM_OBJECTS_TYPE_LIMIT.getParaKey(), 0);
    }
}
