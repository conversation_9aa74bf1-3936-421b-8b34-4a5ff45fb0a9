<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-paas-appframework</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.5-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>fs-paas-app-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-common-gray-release</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-timezone-api</artifactId>
        </dependency>

        <!-- 组织架构 Mongo 服务接口 -->
        <!-- https://wiki.firstshare.cn/pages/viewpage.action?pageId=155421662 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-adapter-api</artifactId>
        </dependency>
    </dependencies>
</project>