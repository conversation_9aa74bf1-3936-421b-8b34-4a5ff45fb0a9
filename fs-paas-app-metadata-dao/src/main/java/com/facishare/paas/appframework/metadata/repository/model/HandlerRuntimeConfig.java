package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.*;

/**
 * Created by zhouwr on 2023/2/6.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity(apiName = HandlerRuntimeConfig.HANDLER_RUNTIME_CONFIG_API_NAME)
public class HandlerRuntimeConfig extends BaseEntity {
    public static final String HANDLER_RUNTIME_CONFIG_API_NAME = "HandlerRuntimeConfigObj";

    public static final String IS_ACTIVE = "is_active";
    public static final String HANDLER_API_NAME = "handler_api_name";
    public static final String BINDING_OBJECT_API_NAME = "binding_object_api_name";
    public static final String EXECUTE_ORDER = "execute_order";
    public static final String EXECUTE_ASYNC = "execute_async";

    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = "是否删除"))
    private boolean deleted;

    @Builder.Default
    @TrueOrFalseField(field = @ObjectField(apiName = IS_ACTIVE, label = "是否启用"))
    private boolean active = true;

    @TextField(field = @ObjectField(apiName = HANDLER_API_NAME, label = "Handler的ApiName"))
    private String handlerApiName;

    @TextField(field = @ObjectField(apiName = BINDING_OBJECT_API_NAME, label = "Handler绑定的对象ApiName"))
    private String bindingObjectApiName;

    @NumberField(field = @ObjectField(apiName = EXECUTE_ORDER, label = "Handler的执行顺序"))
    private Integer executeOrder;

    @TrueOrFalseField(field = @ObjectField(apiName = EXECUTE_ASYNC, label = "Handler是否异步执行"))
    private boolean executeAsync;

    @TextField(field = @ObjectField(apiName = HandlerDefinition.INTERFACE_CODE, label = "Handler绑定的接口Code"))
    private String interfaceCode;

    public static HandlerRuntimeConfig of(HandlerDefinition handlerDefinition, String objectApiName, String interfaceCode) {
        return HandlerRuntimeConfig.builder()
                .bindingObjectApiName(objectApiName)
                .interfaceCode(interfaceCode)
                .handlerApiName(handlerDefinition.getApiName())
                .executeOrder(handlerDefinition.getDefaultOrder())
                .build();
    }
}
