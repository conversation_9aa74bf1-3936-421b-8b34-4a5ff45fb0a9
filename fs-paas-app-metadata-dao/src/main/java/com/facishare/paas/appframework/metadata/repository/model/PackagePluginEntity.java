package com.facishare.paas.appframework.metadata.repository.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/3
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = "PackagePluginObj")
public class PackagePluginEntity extends BaseEntity {

    public static final String OBJECT_API_NAME = "PackagePluginObj";
    public static final String PLUGIN_ENTITY_FILED_DEFINE_TYPE = "define_type";
    public static final String PLUGIN_ENTITY_FILED_APP_TYPE = "app_type";
    public static final String PLUGIN_ENTITY_FILED_NAME = "name";
    public static final String PLUGIN_ENTITY_FILED_ICON = "icon";
    public static final String PLUGIN_ENTITY_FILED_ICON_SIGN = "icon_sign";
    public static final String PLUGIN_ENTITY_FILED_IS_ACTIVE = "is_active";
    public static final String PLUGIN_ENTITY_FILED_IS_BINDING = "is_binding";
    public static final String PLUGIN_ENTITY_FILED_PLUGIN_API_NAME = "plugin_api_name";
    public static final String PLUGIN_ENTITY_FILED_EXTRA_INFO = "extra_info";
    public static final String PLUGIN_ENTITY_FILED_DEV_INFO = "dev_info";
    public static final String PLUGIN_ENTITY_FILED_RUNTIME_DATA = "runtime_data";
    //扩展字段中字段key
    public static final String PLUGIN_ENTITY_FILED_PWC_API_NAME = "pwc_api_name";
    public static final String PLUGIN_ENTITY_FILED_FUNCTION_API_NAME = "function_api_name";

    @TextField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_DEFINE_TYPE))
    private String defineType;

    @TextField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_NAME))
    private String name;

    @TextField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_ICON))
    private String icon;

    @TextField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_APP_TYPE))
    private String appType;

    @TextField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_ICON_SIGN))
    private String iconSign;

    @TrueOrFalseField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_IS_ACTIVE, label = PLUGIN_ENTITY_FILED_IS_ACTIVE))
    @JsonProperty(PLUGIN_ENTITY_FILED_IS_ACTIVE)
    @JSONField(name = PLUGIN_ENTITY_FILED_IS_ACTIVE)
    private Boolean active;

    @TrueOrFalseField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_IS_BINDING, label = PLUGIN_ENTITY_FILED_IS_BINDING))
    @JsonProperty(PLUGIN_ENTITY_FILED_IS_BINDING)
    @JSONField(name = PLUGIN_ENTITY_FILED_IS_BINDING)
    private Boolean binding;

    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = IObjectData.IS_DELETED))
    @JsonProperty(IObjectData.IS_DELETED)
    @JSONField(name = IObjectData.IS_DELETED)
    private Boolean deleted;

    @TextField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_PLUGIN_API_NAME))
    private String pluginApiName;

    @LongTextField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_EXTRA_INFO), fieldType = ObjectFieldType.JSON)
    private Map<String, String> extraInfo;

    @LongTextField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_DEV_INFO), fieldType = ObjectFieldType.JSON)
    private Map<String, String> devInfo;

    @LongTextField(field = @ObjectField(apiName = PLUGIN_ENTITY_FILED_RUNTIME_DATA), fieldType = ObjectFieldType.JSON)
    private Map<String, Object> runtimeData;

    //日程同步业务icon是完整的http路径
    public boolean isHttpIcon() {
        if (StringUtils.isBlank(icon)) {
            return false;
        }
        return icon.startsWith("http://") || icon.startsWith("https://");
    }
}
