package com.facishare.paas.appframework.metadata.repository.converters;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/19
 */
public class BooleanConvert implements Converter<Boolean> {
    private static final BooleanConvert BOOLEAN_CONVERT = new BooleanConvert();

    public static ConverterFactory getConverterFactory() {
        return Converters.newFactory(Boolean.class, getConvert());
    }

    private static BooleanConvert getConvert() {
        return BOOLEAN_CONVERT;
    }

    private BooleanConvert() {
    }

    @Override
    public Boolean convert(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue() != 0;
        }
        return Boolean.parseBoolean(value.toString());
    }
}
