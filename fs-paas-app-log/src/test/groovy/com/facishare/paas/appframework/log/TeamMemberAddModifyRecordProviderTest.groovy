package com.facishare.paas.appframework.log

import com.facishare.paas.I18N
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.TeamMember
import com.facishare.paas.appframework.log.dto.LogInfo
import com.facishare.paas.appframework.log.dto.TeamMemberInfo
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.TeamRoleInfo
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * GenerateByAI
 * Test class for TeamMemberAddModifyRecordProvider
 */
class TeamMemberAddModifyRecordProviderTest extends Specification {
    TeamMemberAddModifyRecordProvider provider
    IObjectData oldData
    IObjectData newData
    IObjectDescribe objectDescribe
    Map<String, List<TeamMemberInfo.Member>> memberInfos
    List<TeamMemberInfo.Msg> msgList
    List<LogInfo.LintMessage> textMsg
    List<TeamRoleInfo> teamRoleInfos

    void setup() {
        provider = new TeamMemberAddModifyRecordProvider()
        oldData = Mock(IObjectData)
        newData = Mock(IObjectData)
        objectDescribe = Mock(IObjectDescribe)
        memberInfos = [:]
        msgList = []
        textMsg = []
        teamRoleInfos = []
    }

    /**
     * GenerateByAI
     * Test getApiName returns correct value
     */
    def "testGetApiNameTest"() {
        when:
        def result = provider.getApiName()

        then:
        result == ActionType.AddEmployee.getId()
    }

    /**
     * GenerateByAI
     * Test logTeamMember when adding new team member
     */
    def "testLogTeamMemberWhenAddingNewMemberTest"() {
        given:
        def memberId = "test123"
        def memberName = "Test User"
        def objectApiName = "TestObject"
        def displayName = "Test Display Name"
        def dataName = "Test Data"
        def dataId = "data123"

        // Mock TeamMember
        def teamMember = Mock(TeamMember) {
            getEmployee() >> memberId
            getMemberType() >> TeamMember.MemberType.EMPLOYEE
            getPermission() >> TeamMember.Permission.READANDWRITE
            getRoleCode() >> "role_code"
        }

        // Setup object data
        newData.getName() >> dataName
        newData.getId() >> dataId
        newData.getOwner() >> []
        objectDescribe.getDisplayName() >> displayName
        objectDescribe.getApiName() >> objectApiName

        // Setup memberInfos
        memberInfos[TeamMember.MemberType.EMPLOYEE.getValue()] = [
            new TeamMemberInfo.Member(id: memberId, name: memberName)
        ]

        // Mock ObjectDataExt
        def objectDataExt = Mock(ObjectDataExt)
        objectDataExt.getTeamMembers() >> [teamMember]
        ObjectDataExt.metaClass.static.of = { IObjectData data -> 
            data == newData ? objectDataExt : Mock(ObjectDataExt) { getTeamMembers() >> [] }
        }

        // Mock I18N
        GroovyMock(I18N, global: true)
        I18N.text(_) >> { String key -> key }

        when:
        provider.logTeamMember(oldData, newData, objectDescribe, memberInfos, msgList, textMsg, teamRoleInfos)

        then:
        textMsg.size() == 2
        textMsg[0].text == displayName
        textMsg[1].text == dataName
        textMsg[1].dataID == dataId
        textMsg[1].objectApiName == objectApiName
        msgList.size() == 1
        msgList[0].operationType == ActionType.AddEmployee.getId()
        msgList[0].member.id == memberId
        msgList[0].member.name == memberName

        cleanup:
        ObjectDataExt.metaClass = null
    }

    /**
     * GenerateByAI
     * Test logTeamMember with empty new team members
     */
    def "testLogTeamMemberWithEmptyNewMembersTest"() {
        given:
        def objectDataExt = Mock(ObjectDataExt)
        objectDataExt.getTeamMembers() >> []
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }

        when:
        provider.logTeamMember(oldData, newData, objectDescribe, memberInfos, msgList, textMsg, teamRoleInfos)

        then:
        textMsg.isEmpty()
        msgList.isEmpty()

        cleanup:
        ObjectDataExt.metaClass = null
    }

    /**
     * GenerateByAI
     * Test logTeamMember with null messages
     */
    def "testLogTeamMemberWithNullMessagesTest"() {
        given:
        def objectDataExt = Mock(ObjectDataExt)
        objectDataExt.getTeamMembers() >> null
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }

        when:
        provider.logTeamMember(oldData, newData, objectDescribe, memberInfos, msgList, textMsg, teamRoleInfos)

        then:
        textMsg.isEmpty()
        msgList.isEmpty()

        cleanup:
        ObjectDataExt.metaClass = null
    }
} 