package com.facishare.paas.appframework.log.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by weilh on 2017/4/20.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmpSimpleInfo {

    @JSONField(name = "M1")
    private int employeeID;

    @JSONField(name = "M2")
    private String name;

    @JSONField(name = "M3")
    private String profileImage;

    @JSONField(name = "M4")
    private String department;

    @JSONField(name = "M5")
    private String post;

    @JSONField(name = "M6")
    private String mobile;
}
