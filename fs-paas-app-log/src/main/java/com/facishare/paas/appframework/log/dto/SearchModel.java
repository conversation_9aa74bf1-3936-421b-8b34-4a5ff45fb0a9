package com.facishare.paas.appframework.log.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * Created by weilh on 2017/4/18.
 */
public class SearchModel {

    @Data
    public static class Arg{
        @JSONField(name = "condition")
        private SearchCondition condition;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Result extends BaseResult {

        @JSONField(name = "page")
        private int page;

        @JSONField(name = "pageSize")
        private int pageSize;

        @JSONField(name = "totalCount")
        private int totalCount;

        @JSONField(name = "totalPage")
        private int totalPage;

        @JSONField(name = "results")
        private List<LogInfo> results;

        @JSONField(name = "richResults")
        private Map<String, List<LogInfo>> richResults;

        @JSONField(name = "hasMore")
        private Boolean hasMore;

    }
}
