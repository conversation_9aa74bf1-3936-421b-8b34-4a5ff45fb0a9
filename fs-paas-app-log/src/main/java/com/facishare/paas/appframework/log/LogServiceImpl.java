package com.facishare.paas.appframework.log;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.ConnectionServiceProxy;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.UserRoleInformationService;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.exception.NotElementPresentException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.log.dto.*;
import com.facishare.paas.appframework.log.metadatahandle.FillFieldInfoHandle;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.UdefFunctionService;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.rest.core.exception.RestProxyInvokeException;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.log.LogModuleGroupEnum.DATA_PRIVILEGE_MANAGEMENT;

/**
 * LogInfo service implements
 * <p>
 * Created by liyiguang on 2017/7/9.
 */
@Service("logService")
@Slf4j
public class LogServiceImpl implements LogService {
    @Autowired
    private OrgService orgService;
    @Autowired
    private AsyncLogSender asyncLogSender;
    @Autowired
    private LogServiceProxy logServiceProxy;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private UdefFunctionService udefFunctionService;
    @Autowired
    private ModifyRecordInitManager modifyRecordInitManager;
    @Autowired
    private FillFieldInfoHandle fillFieldInfoHandle;
    @Autowired
    private ConnectionServiceProxy connectionServiceProxy;
    @Autowired
    @Qualifier("optionalFeaturesService")
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    private TeamMemberRoleService teamMemberRoleService;
    @Autowired
    private UserRoleInformationService userRoleInformationService;

    private static List<String> newAuditLogListForActionType = Lists.newArrayList(ActionType.Add.getId(),
            ActionType.Modify.getId(), ActionType.ChangeOwner.getId(), ActionType.Invalid.getId(),
            ActionType.Recovery.getId(), ActionType.Delete.getId(),
            ActionType.AddEmployee.getId(), ActionType.AddSale.getId(), ActionType.RemoveEmployee.getId(),
            ActionType.RemoveSale.getId(), ActionType.ModifySale.getId(), ActionType.Import.getId(),
            ActionType.UpdateImport.getId(), ActionType.ASSOCIATE.getId(), ActionType.DISASSOCIATE.getId(),
            ActionType.SFA_WORK_FLOW_START.getId(), ActionType.SFA_WORK_FLOW_TACK_BACK.getId(),
            ActionType.SFA_WORK_FLOW_COMPLETE.getId(), ActionType.SFA_WORK_FLOW_REJECT.getId(),
            ActionType.CHANGE_PARTNER_OWNER.getId(), ActionType.IMPORT_ADD.getId(),
            ActionType.MOVE.getId(), ActionType.RETURN.getId()
    );
    private static final List<String> SPECIFIC_OBJECT_NAME = Lists.newArrayList("41", "42", "43", "44", "workflow_bpm", "stage");

    @Override
    public void log(User user, EventType eventType, ActionType actionType, String objectApiName, String textMessage) {
        // Add parameter validation
        if (user == null || eventType == null || actionType == null) {
            return;
        }

        try {
            Optional<IObjectDescribe> objectDescribeOptional = objectDescribeService.findDescribeListByApiNames(user.getTenantId(), Lists.newArrayList(objectApiName)).stream().findFirst();
            if (objectDescribeOptional.isPresent() && isModifyRecordClosed(user, objectDescribeOptional.get())) {
                return;
            }
        } catch (MetadataServiceException e) {
            log.error("findDescribeListByApiNames tenantId:{},objectApiName:{}", user.getTenantId(), objectApiName, e);
        }
        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder().textMsg(
                Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, objectApiName))).build();
        LogInfo info = getLogInfo(user, eventType, actionType, objectApiName, getModule(), snapshot, textMessage, null);

        asyncLogSender.offer(info);
    }

    @Override
    public void logInternational(User user, EventType eventType, ActionType actionType, String objectApiName, String textMessage, InternationalItem internationalTextMessage) {
        logInternational(user, eventType, actionType, objectApiName, "", textMessage, internationalTextMessage);
    }

    @Override
    public void logInternational(User user, EventType eventType, ActionType actionType, String objectApiName, String module, String textMessage, InternationalItem internationalTextMessage) {
        try {
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), objectApiName);
            if (isModifyRecordClosed(user, describe)) {
                return;
            }
        } catch (MetadataServiceException e) {
            log.error("findByTenantIdAndDescribeApiName tenantId:{},objectApiName:{}", user.getTenantId(), objectApiName, e);
        }
        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, objectApiName, internationalTextMessage)))
                .build();
        if (StringUtils.isBlank(module)) {
            module = getModule();
        }
        LogInfo info = getLogInfo(user, eventType, actionType, objectApiName, module, snapshot, textMessage, null, internationalTextMessage);
        asyncLogSender.offer(info);
    }

    @Override
    public void logInternational(User user,
                                 EventType eventType, ActionType actionType,
                                 IObjectDescribe describe, String module, String dataId,
                                 String textMessage, InternationalItem internationalTextMessage) {
        String objectApiName = describe.getApiName();
        if (isModifyRecordClosed(user, describe)) {
            return;
        }
        LogInfo info = getLogInfo(user, eventType, actionType, objectApiName, module, null, textMessage, dataId, internationalTextMessage);
        asyncLogSender.offer(info);
    }



    private boolean isModifyRecordClosed(User user, IObjectDescribe describe) { // 基本信息扩展功能: 修改记录
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describe);
        if (!optionalFeaturesSwitch.getIsModifyRecordEnabled()) {
            return true;
        }
        return false;
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, String objectApiName,
                    Map<String, Object> objectSnapshot, String textMessage) {
        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, objectApiName)))
                .snapshot(objectSnapshot)
                .build();
        LogInfo info = getLogInfo(user, eventType, actionType, objectApiName, getModule(), snapshot, textMessage, null);
        asyncLogSender.offer(info);
    }


    private LogInfo getLogInfo(User user, EventType eventType, ActionType actionType, String apiName, String module,
                               LogInfo.ObjectSnapshot snapshot, String textMessage, String objectId, String bizId) {
        LogInfo logInfo = getLogInfo(user, eventType, actionType, apiName, module, snapshot, textMessage, objectId);
        logInfo.setBizId(bizId);
        return logInfo;
    }


    private LogInfo getLogInfo(User user, EventType eventType, ActionType actionType, String apiName, String module,
                               LogInfo.ObjectSnapshot snapshot, String textMessage, String objectId, InternationalItem internationalTextMessage) {
        User newUser = user;
        if (user.isOutUser() || Strings.isNullOrEmpty(user.getUserName())) {
            newUser = orgService.getUser(user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser());
        }
        // 通过requestContext获取来源模块和具体名称 (如：peerDisplayName 自定义按钮1；modelName：customButton)
        RequestContext requestContext = Optional.ofNullable(RequestContextManager.getContext()).orElse(RequestContext.builder().build());
        if (Objects.nonNull(snapshot)) {
            snapshot.setPeerDisplayName(requestContext.getPeerDisplayName());
            snapshot.setModelName(requestContext.getModelName());
        }

        return LogInfo.builder()
                .operationTime(System.currentTimeMillis())
                .operation(eventType.getId())
                .bizOperationName(actionType.getId())
                .corpId(newUser.getTenantId())
                .outTenantId(user.getOutTenantId())
                .userId(newUser.getUserId())
                .userName(newUser.getUserName())
                .appId("CRM")
                .module(module)
                .objectName(apiName)
                .textMessage(textMessage)
                .internationalTextMessage(internationalTextMessage)
                .snapshot(snapshot)
                .objectId(Objects.isNull(objectId) ? "" : objectId)
                .peerName(StringUtils.trimToEmpty(RequestUtil.getPeerName()))
                .bizId(RequestUtil.getBizId())
                .otherBizId(RequestUtil.getOtherBizId())
                .hidden(RequestUtil.isModifyLogHidden())
                .bizAppId(RequestUtil.getAppId())
                .build();
    }

    private LogInfo getLogInfo(User user, EventType eventType, ActionType actionType, String apiName, String module,
                               LogInfo.ObjectSnapshot snapshot, String textMessage, String objectId) {
        User newUser = orgService.fillUserName(user);
        // 通过requestContext获取来源模块和具体名称 (如：peerDisplayName 自定义按钮1；modelName：customButton)
        RequestContext requestContext = Optional.ofNullable(RequestContextManager.getContext()).orElse(RequestContext.builder().build());
        if (Objects.nonNull(snapshot)) {
            snapshot.setPeerDisplayName(requestContext.getPeerDisplayName());
            snapshot.setModelName(requestContext.getModelName());
        }

        return LogInfo.builder()
                .operationTime(System.currentTimeMillis())
                .operation(eventType.getId())
                .bizOperationName(actionType.getId())
                .corpId(newUser.getTenantId())
                .outTenantId(user.getOutTenantId())
                .userId(newUser.getUserId())
                .userName(newUser.getUserName())
                .appId("CRM")
                .module(module)
                .objectName(apiName)
                .textMessage(textMessage)
                .snapshot(snapshot)
                .objectId(Objects.isNull(objectId) ? "" : objectId)
                .peerName(StringUtils.trimToEmpty(RequestUtil.getPeerName()))
                .bizId(RequestUtil.getBizId())
                .otherBizId(RequestUtil.getOtherBizId())
                .hidden(RequestUtil.isModifyLogHidden())
                .bizAppId(RequestUtil.getAppId())
                .build();
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data) {
        log(user, eventType, actionType, describe, data, Maps.newHashMap());
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, Map<String, Object> extendsInfo) {
        if (isModifyRecordClosed(user, describe)) {
            return;
        }
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(describe, data, extendsInfo);
        LogInfo info = getLogInfo(user, eventType, actionType, describe.getApiName(), describe.getApiName(), snapshot, snapshot.getMessage(), data.getId());

        fillMasterIdIfNecessary(actionType, describe, data, info);
        asyncLogSender.offer(info);
    }

    private void fillMasterIdIfNecessary(ActionType actionType, IObjectDescribe describe, IObjectData data, LogInfo info) {
        if (ObjectDescribeExt.of(describe).isSlaveObject() && !isImportAction(actionType)) {
            ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().ifPresent(x -> {
                String fieldApiName = x.getApiName();
                if (data.get(fieldApiName) != null) {
                    info.setMasterId(data.get(fieldApiName).toString());
                }
            });
        }
    }

    @Override
    public void logWithCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage) {
        if (isModifyRecordClosed(user, describe)) {
            return;
        }
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshotWithCustomMessage(describe, data, customMessage);
        LogInfo info = getLogInfo(user, eventType, actionType, describe.getApiName(), describe.getApiName(), snapshot, snapshot.getMessage(), data.getId());
        fillMasterIdIfNecessary(actionType, describe, data, info);
        asyncLogSender.offer(info);
    }

    @Override
    public void logWithInternationalCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage, InternationalItem internationalCustomMessage) {
        if (isModifyRecordClosed(user, describe)) {
            return;
        }
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshotWithInternalMessage(describe, data, customMessage, internationalCustomMessage);
        LogInfo info = getLogInfo(user, eventType, actionType, describe.getApiName(), describe.getApiName(), snapshot, customMessage, data.getId(), internationalCustomMessage);
        fillMasterIdIfNecessary(actionType, describe, data, info);
        asyncLogSender.offer(info);
    }

    @Override
    public void logDataWithInternationalCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage, InternationalItem customMessageInternational) {
        if (isModifyRecordClosed(user, describe)) {
            return;
        }
        LogInfo.ObjectSnapshot snapshot = LogInfo.createDataSnapshotWithInternalMessage(describe, data, customMessage, customMessageInternational);
        LogInfo info = getLogInfo(user, eventType, actionType, describe.getApiName(), describe.getApiName(), snapshot, customMessage, data.getId(), customMessageInternational);
        fillMasterIdIfNecessary(actionType, describe, data, info);
        asyncLogSender.offer(info);
    }

    @Override
    public void logWithCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage, List<TeamMemberInfo.Msg> msgs) {
        if (isModifyRecordClosed(user, describe)) {
            return;
        }
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshotWithCustomMessage(describe, data, customMessage);
        snapshot.setMsgs(msgs);
        LogInfo info = getLogInfo(user, eventType, actionType, describe.getApiName(), describe.getApiName(), snapshot, snapshot.getMessage(), data.getId());
        fillMasterIdIfNecessary(actionType, describe, data, info);
        asyncLogSender.offer(info);
    }

    @Override
    public void logWithCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, List<IObjectData> dataList, String customMessage) {
        if (isModifyRecordClosed(user, describe)) {
            return;
        }
        dataList.forEach(a -> logWithCustomMessage(user, eventType, actionType, describe, a, customMessage));
    }

    @Override
    public void logCustomMessageOnly(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage) {
        if (isModifyRecordClosed(user, describe)) {
            return;
        }
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshotWithOnlyCustomMessage(describe, data, customMessage);
        LogInfo info = getLogInfo(user, eventType, actionType, describe.getApiName(), describe.getApiName(), snapshot, snapshot.getMessage(), data.getId());

        fillMasterIdIfNecessary(actionType, describe, data, info);
        asyncLogSender.offer(info);
    }

    //增加masterLogId、peerName和peerDisplayName参数，分别用来记录主从一起操作的id、来源模块和来源具体信息
    private void logDetail(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String masterId, String masterLogId,
                           String peerName, String peerDisplayName, Map<String, Object> extendsInfo) {
        logDetail(user, eventType, actionType, describe, data, masterId, masterLogId, peerName, peerDisplayName, extendsInfo, null);
    }

    private void logDetail(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String masterId, String masterLogId,
                           String peerName, String peerDisplayName, Map<String, Object> extendsInfo, ConvertSourceContainer convertSourceContainer) {
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(describe, data, extendsInfo);
        if (Objects.nonNull(convertSourceContainer)) {
            snapshot.setConvertEventId(convertSourceContainer.getEventId());
            String sourceDetailApiName = convertSourceContainer.getTargetSourceApiNameMap().get(describe.getApiName());
            IObjectDescribe sourceDetailDescribe = convertSourceContainer.getSourceDetailDescribes().get(sourceDetailApiName);
            List<IObjectData> sourceDetailList = convertSourceContainer.getSourceDetailList().get(sourceDetailApiName);
            if (Objects.nonNull(sourceDetailDescribe) && CollectionUtils.notEmpty(sourceDetailList)) {
                List<IObjectData> sourceDetailData = sourceDetailList.stream()
                        .filter(sourceData -> Objects.equals(sourceData.getId(), convertSourceContainer.getTargetSourceIdMap().get(data.getId())))
                        .collect(Collectors.toList());
                snapshot.setSourceSnapshot(LogInfo.getSourceSnapshot(sourceDetailDescribe, sourceDetailData));
            }
        }
        LogInfo info = getLogInfo(user, eventType, actionType, describe.getApiName(), describe.getApiName(), snapshot, snapshot.getMessage(), data.getId());

        // 补充logInfo
        fillLogInfo(peerName, peerDisplayName, masterLogId, info, masterId);
        asyncLogSender.offer(info);
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, List<IObjectData> data) {
        log(user, eventType, actionType, describe, data, Maps.newHashMap());
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, List<IObjectData> data, Map<String, Object> extendsInfo) {
        if (isModifyRecordClosed(user, describe)) {
            return;
        }
        if (ObjectDescribeExt.of(describe).isSlaveObject() && !isImportAction(actionType)) {
            String fieldApiName = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().map(x -> x.getApiName()).orElse(null);
            data.forEach(x -> {
                String masterId = x.get(fieldApiName) == null ? null : x.get(fieldApiName).toString();
                logDetail(user, eventType, actionType, describe, x, masterId, null, null, null, extendsInfo);
            });
        } else {
            data.forEach(x -> log(user, eventType, actionType, describe, x, extendsInfo));
        }
    }

    private boolean isImportAction(ActionType actionType) {
        return Objects.equals(actionType, ActionType.Import) || Objects.equals(actionType, ActionType.UpdateImport);
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes,
                    List<IObjectData> data) {
        log(user, eventType, actionType, objectDescribes, data, null);
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes,
                    List<IObjectData> data, String masterLogId) {
        log(user, eventType, actionType, objectDescribes, data, masterLogId, null);
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, List<IObjectData> data, String masterLogId, ConvertSourceContainer convertSourceContainer) {
        Map<String, OptionalFeaturesSwitchDTO> optionalFeaturesSwitchMap = Maps.newHashMap();
        if (AppFrameworkConfig.isOptionalFeaturesSupport(user.getTenantId())) {
            optionalFeaturesSwitchMap.putAll(optionalFeaturesService.batchQueryOptionalFeaturesSwitch(user.getTenantId(), Lists.newArrayList(objectDescribes.values())));
        }

        data.forEach(x -> {
            IObjectDescribe describe = objectDescribes.get(x.getDescribeApiName());
            if (CollectionUtils.notEmpty(optionalFeaturesSwitchMap) && Objects.nonNull(optionalFeaturesSwitchMap.get(describe.getApiName()))
                    && !optionalFeaturesSwitchMap.get(describe.getApiName()).getIsModifyRecordEnabled()) {
                return;
            }
            if (ObjectDescribeExt.of(describe).isSlaveObject() && !isImportAction(actionType)) {
                String fieldApiName = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().get().getApiName();
                String masterId = x.get(fieldApiName) == null ? null : x.get(fieldApiName).toString();
                logDetail(user, eventType, actionType, describe, x, masterId, masterLogId, null, null, Maps.newHashMap(), convertSourceContainer);
            } else {
                log(user, eventType, actionType, describe, x);
            }
        });
    }

    @Override
    public void masterDetailLog(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes,
                                List<IObjectData> data) {
        masterDetailLog(user, eventType, actionType, objectDescribes, data, null, null);
    }

    @Override
    public void masterDetailLog(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes,
                                List<IObjectData> data, String peerName, String peerDisplayName) {
        masterDetailLog(user, eventType, actionType, objectDescribes, data, peerName, peerDisplayName, Maps.newHashMap());
    }

    @Override
    public void masterDetailLog(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes,
                                List<IObjectData> data, String peerName, String peerDisplayName, Map<String, Object> extendsInfo) {
        masterDetailLog(user, eventType, actionType, objectDescribes, data, peerName, peerDisplayName, extendsInfo, null);
    }

    @Override
    public void masterDetailLog(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes,
                                List<IObjectData> data, String peerName, String peerDisplayName, Map<String, Object> extendsInfo,
                                ConvertSourceContainer convertSourceContainer) {
        if (CollectionUtils.empty(data)) {
            return;
        }
        Map<String, OptionalFeaturesSwitchDTO> optionalFeaturesSwitchMap = Maps.newHashMap();
        if (AppFrameworkConfig.isOptionalFeaturesSupport(user.getTenantId())) {
            optionalFeaturesSwitchMap.putAll(optionalFeaturesService.batchQueryOptionalFeaturesSwitch(user.getTenantId(), Lists.newArrayList(objectDescribes.values())));
        }
        // 获取从对象信息
        List<LogInfo.DetailInfo> detailInfos = getDetailInfos(objectDescribes, data);
        // 如果从对象不为空，生成masterLogId
        String masterLogId = getMasterLogId(detailInfos);

        List<LogInfo.DetailInfo> sourceDetailInfos = getSourceDetailInfos(convertSourceContainer);

        data.forEach(x -> {
            IObjectDescribe describe = objectDescribes.get(x.getDescribeApiName());
            if (CollectionUtils.notEmpty(optionalFeaturesSwitchMap) && Objects.nonNull(optionalFeaturesSwitchMap.get(describe.getApiName()))
                    && !optionalFeaturesSwitchMap.get(describe.getApiName()).getIsModifyRecordEnabled()) {
                return;
            }
            if (ObjectDescribeExt.of(describe).isSlaveObject() && !isImportAction(actionType)) {
                String fieldApiName = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().get().getApiName();
                String masterId = x.get(fieldApiName) == null ? null : x.get(fieldApiName).toString();
                logDetail(user, eventType, actionType, describe, x, masterId, masterLogId, peerName, peerDisplayName, extendsInfo, convertSourceContainer);
            } else {
                LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(describe, x, extendsInfo);
                fillSourceSnapshot(convertSourceContainer, snapshot, sourceDetailInfos);
                // 补充snapshot
                fillSnapshot(detailInfos, snapshot);
                LogInfo info = getLogInfo(user, eventType, actionType, describe.getApiName(), describe.getApiName(), snapshot, snapshot.getMessage(), x.getId());
                fillMasterIdIfNecessary(actionType, describe, x, info);
                // 补充logInfo
                fillLogInfo(peerName, peerDisplayName, masterLogId, info, null);
                asyncLogSender.offer(info);
            }
        });
    }

    private static void fillSourceSnapshot(ConvertSourceContainer convertSourceContainer, LogInfo.ObjectSnapshot snapshot, List<LogInfo.DetailInfo> sourceDetailInfos) {
        if (Objects.nonNull(convertSourceContainer)) {
            snapshot.setConvertEventId(convertSourceContainer.getEventId());
            if (CollectionUtils.notEmpty(sourceDetailInfos)) {
                snapshot.setSourceDetailInfo(sourceDetailInfos);
                snapshot.setSourceSnapshot(LogInfo.getSourceSnapshot(convertSourceContainer.getSourceObjectDescribe(), convertSourceContainer.getSourceObjectDataList()));
            }
        }
    }

    private List<LogInfo.DetailInfo> getSourceDetailInfos(ConvertSourceContainer convertSourceContainer) {
        List<LogInfo.DetailInfo> sourceDetailInfos = Lists.newArrayList();
        if (Objects.nonNull(convertSourceContainer)) {
            sourceDetailInfos.addAll(getSourceDetailInfos(convertSourceContainer.getSourceDetailDescribes(), convertSourceContainer.getSourceDetailList()));
        }
        return sourceDetailInfos;
    }

    private List<LogInfo.DetailInfo> getSourceDetailInfos(Map<String, IObjectDescribe> sourceDetailDescribeMap, Map<String, List<IObjectData>> sourceDetailList) {
        if (CollectionUtils.empty(sourceDetailList)) {
            return Lists.newArrayList();
        }
        List<LogInfo.DetailInfo> detailInfos = Lists.newArrayList();
        sourceDetailList.keySet().forEach(apiName -> detailInfos.add(LogInfo.DetailInfo.builder()
                .objectApiName(apiName)
                .objectLabel(sourceDetailDescribeMap.get(apiName).getDisplayName())
                .build()));
        return detailInfos;
    }

    private void fillSnapshot(List<LogInfo.DetailInfo> detailInfos, LogInfo.ObjectSnapshot snapshot) {
        if (CollectionUtils.empty(detailInfos)) {
            return;
        }
        snapshot.setDetailInfo(detailInfos);
    }

    private void fillLogInfo(String peerName, String peerDisplayName, String masterLogId, LogInfo info, String masterId) {
        if (!Strings.isNullOrEmpty(masterId)) {
            info.setMasterId(masterId);
        }
        if (StringUtils.isNotEmpty(masterLogId)) {
            info.setMasterLogId(masterLogId);
        }
        if (StringUtils.isNotEmpty(peerName)) {
            info.setPeerName(peerName);
        }
        if (Objects.nonNull(info.getSnapshot()) && StringUtils.isNotEmpty(peerDisplayName)) {
            info.getSnapshot().setPeerDisplayName(peerDisplayName);
        }
    }

    private String getMasterLogId(List<LogInfo.DetailInfo> detailInfos) {
        if (CollectionUtils.empty(detailInfos)) {
            return null;
        }
        return IdGenerator.get();
    }

    @Override
    public void detailModifyLog(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList,
                                Map<String, Map<String, Object>> updatedFieldMap,
                                List<IObjectData> dbDetailDataList, String masterLogId) {
        detailModifyLog(user, ActionType.Modify, objectDescribe, dataList, updatedFieldMap, dbDetailDataList, masterLogId);
    }

    @Override
    public void detailModifyLog(User user, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> dataList,
                                Map<String, Map<String, Object>> updatedFieldMap,
                                List<IObjectData> dbDetailDataList, String masterLogId) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(dbDetailDataList) || CollectionUtils.empty(updatedFieldMap)) {
            return;
        }
        if (isModifyRecordClosed(user, objectDescribe)) {
            return;
        }

        // 补充dataList中的__r信息
        List<IObjectData> modifyDataList = ObjectDataExt.copyList(dataList);
        List<IObjectData> cpDbDetailDataList = ObjectDataExt.copyList(dbDetailDataList);
        asyncFillFieldInfo(user, objectDescribe, modifyDataList, updatedFieldMap, cpDbDetailDataList);

        modifyDataList.forEach(x -> {
            // 重新拷贝一份再remove掉计算和统计字段的修改记录
            Map<String, Object> updateField = Maps.newHashMap(CollectionUtils.nullToEmpty(updatedFieldMap.get(x.getId())));
            ObjectDataExt.of(updateField).removeFieldForLog(objectDescribe);
            if (CollectionUtils.empty(updateField)) {
                return;
            }
            // 获取masterId
            String fieldApiName = ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().get().getApiName();
            String masterId = x.get(fieldApiName) == null ? null : x.get(fieldApiName).toString();
            // 获取此对象下的dbData
            Map<String, IObjectData> dbDataMap = CollectionUtils.nullToEmpty(cpDbDetailDataList)
                    .stream().collect(Collectors.toMap(DBRecord::getId, y -> y));

            LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(objectDescribe, x, updateField, dbDataMap);
            LogInfo info = getLogInfo(user, EventType.MODIFY, actionType, objectDescribe.getApiName(),
                    objectDescribe.getApiName(), snapshot, snapshot.getMessage(), x.getId());
            info.setMasterLogId(masterLogId);
            if (!Strings.isNullOrEmpty(masterId)) {
                info.setMasterId(masterId);
            }
            asyncLogSender.offer(info);
        });
    }

    private void asyncFillFieldInfo(User user, IObjectDescribe objectDescribe, List<IObjectData> modifyDataList,
                                    Map<String, Map<String, Object>> updatedFieldMap, List<IObjectData> dbDetailDataList) {
        try {
            boolean match = updatedFieldMap.values().stream()
                    .anyMatch(x -> x.keySet().stream()
                            .anyMatch(y -> {
                                IFieldDescribe field = ObjectDescribeExt.of(objectDescribe).getFieldDescribe(y);
                                if (Objects.isNull(field)) {
                                    return false;
                                }
                                return LogInfo.needFillFieldInfoList.contains(field.getType());
                            }));
            if (match) {
                // 补充dataList中的__r信息
                fillFieldInfoHandle.asyncFillFieldInfo(objectDescribe, modifyDataList, user);
                fillFieldInfoHandle.asyncFillFieldInfo(objectDescribe, dbDetailDataList, user);
            }
        } catch (Exception e) {
            log.warn("asyncFillFieldInfo error", e);
        }
    }

    @Override
    public String masterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                  Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData, Map<String, Object> detailChangeMap) {
        List<String> changeDetailApiName = getChangeDetailApiNameForLog(objectDescribes, detailChangeMap);
        return masterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName);
    }

    @Override
    public String masterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                  Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData, List<String> changeDetailApiName) {
        MasterLogInfo masterLogInfo = fillMasterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName);
        sendLog(masterLogInfo.getLogList());
        return masterLogInfo.getMasterLogId();
    }

    @Override
    public void sendLog(List<LogInfo> logList) {
        if (CollectionUtils.empty(logList)) {
            return;
        }

        logList.forEach(a -> asyncLogSender.offer(a));
    }

    @Override
    public MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                             Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                             Map<String, Object> detailChangeMap) {
        List<String> changeDetailApiName = getChangeDetailApiNameForLog(objectDescribes, detailChangeMap);
        return fillMasterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName, Maps.newHashMap());
    }

    @Override
    public MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                             Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                             Map<String, Object> detailChangeMap, Map<String, Object> extendsInfo) {

        List<String> changeDetailApiName = getChangeDetailApiNameForLog(objectDescribes, detailChangeMap);
        return fillMasterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName, extendsInfo);
    }

    @Override
    public MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData, Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData, Map<String, Object> detailChangeMap, Map<String, Object> extendsInfo, ConvertSourceContainer convertSourceContainer) {
        List<String> changeDetailApiName = getChangeDetailApiNameForLog(objectDescribes, detailChangeMap);
        return fillMasterModifyLog(user, ActionType.Modify, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName, extendsInfo, convertSourceContainer);
    }

    @Override
    public MasterLogInfo fillMasterModifyLog(User user, ActionType actionType, Map<String, IObjectDescribe> objectDescribes,
                                             IObjectData masterData, Map<String, Map<String, Map<String, Object>>> updatedFieldMap,
                                             IObjectData dbMasterData, Map<String, Object> detailChangeMap, Map<String, Object> extendsInfo) {
        List<String> changeDetailApiName = getChangeDetailApiNameForLog(objectDescribes, detailChangeMap);
        return fillMasterModifyLog(user, ActionType.Modify, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName, extendsInfo);
    }

    @Override
    public MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                             Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                             List<String> changeDetailApiName) {
        return fillMasterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName, Maps.newHashMap());
    }

    @Override
    public MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                             Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                             List<String> changeDetailApiName, Map<String, Object> extendsInfo) {

        return fillMasterModifyLog(user, ActionType.Modify, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName, extendsInfo);
    }

    @Override
    public MasterLogInfo fillMasterModifyLog(User user, ActionType actionType, Map<String, IObjectDescribe> objectDescribes,
                                             IObjectData masterData, Map<String, Map<String, Map<String, Object>>> updatedFieldMap,
                                             IObjectData dbMasterData, List<String> changeDetailApiName, Map<String, Object> extendsInfo) {
        return fillMasterModifyLog(user, actionType, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName, extendsInfo, null);
    }

    @Override
    public MasterLogInfo fillMasterModifyLog(User user, ActionType actionType, Map<String, IObjectDescribe> objectDescribes,
                                             IObjectData masterData, Map<String, Map<String, Map<String, Object>>> updatedFieldMap,
                                             IObjectData dbMasterData, List<String> changeDetailApiName, Map<String, Object> extendsInfo,
                                             ConvertSourceContainer convertSourceContainer) {
        String masterLogId;
        List<LogInfo.DetailInfo> detailInfos;
        // 获取从对象信息
        detailInfos = getDetailInfosForChangeDetailApiName(objectDescribes, changeDetailApiName);
        // 获取masterLogId
        masterLogId = getMasterLogId(detailInfos);
        IObjectData masterModifyData = ObjectDataExt.of(masterData).copy();
        IObjectDescribe objectDescribe = objectDescribes.get(masterModifyData.getDescribeApiName());
        if ((updateFieldIsEmpty(masterData, updatedFieldMap) && CollectionUtils.empty(detailInfos))
                || isModifyRecordClosed(user, objectDescribes.get(masterData.getDescribeApiName()))) { // 主对象关闭修改记录开关时不记录修改记录
            if (Objects.nonNull(convertSourceContainer)) {
                return buildConvertRuleLog(user, actionType, convertSourceContainer, objectDescribe, masterModifyData, masterLogId);
            }
            return MasterLogInfo.builder().masterLogId(masterLogId).build();
        }

        // 补充dataList中的__r信息
        asyncFillFieldInfo(user, objectDescribe, Lists.newArrayList(masterModifyData),
                updatedFieldMap.get(masterModifyData.getDescribeApiName()), Lists.newArrayList(dbMasterData));

        Map<String, Object> updateField = CollectionUtils.nullToEmpty(updatedFieldMap.get(masterModifyData.getDescribeApiName())).get(masterModifyData.getId());
        // 重新拷贝一份再remove掉计算和统计字段的修改记录
        updateField = Maps.newHashMap(updateField);
        ObjectDataExt.of(updateField).removeFieldForLog(objectDescribe);
        if (CollectionUtils.empty(updateField) && CollectionUtils.empty(detailInfos)) {
            if (Objects.nonNull(convertSourceContainer)) {
                return buildConvertRuleLog(user, actionType, convertSourceContainer, objectDescribe, masterModifyData, masterLogId);
            }
            return MasterLogInfo.builder().masterLogId(masterLogId).build();
        }
        Map<String, IObjectData> dbMasterDataMap = Lists.newArrayList(dbMasterData).stream().collect(Collectors.toMap(DBRecord::getId, y -> y));
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(objectDescribe, masterModifyData, updateField, dbMasterDataMap, extendsInfo);
        fillSourceSnapshot(convertSourceContainer, snapshot, getSourceDetailInfos(convertSourceContainer));
        fillSnapshot(detailInfos, snapshot);
        LogInfo info = getLogInfo(user, EventType.MODIFY, actionType, objectDescribe.getApiName(),
                objectDescribe.getApiName(), snapshot, snapshot.getMessage(), masterModifyData.getId());
        if (CollectionUtils.notEmpty(detailInfos)) {
            info.setMasterLogId(masterLogId);
        }

        List<LogInfo> logList = Lists.newArrayList();
        logList.add(info);
        return MasterLogInfo.builder().masterLogId(masterLogId).logList(logList).build();
    }

    private MasterLogInfo buildConvertRuleLog(User user, ActionType actionType, ConvertSourceContainer convertSourceContainer, IObjectDescribe objectDescribe, IObjectData masterModifyData, String masterLogId) {
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(objectDescribe, masterModifyData, Maps.newHashMap(), Maps.newHashMap(), Maps.newHashMap());
        fillSourceSnapshot(convertSourceContainer, snapshot, getSourceDetailInfos(convertSourceContainer));
        LogInfo info = getLogInfo(user, EventType.MODIFY, actionType, objectDescribe.getApiName(),
                objectDescribe.getApiName(), snapshot, snapshot.getMessage(), masterModifyData.getId());
        return MasterLogInfo.builder().masterLogId(masterLogId).logList(Lists.newArrayList(info)).build();
    }

    private List<String> getChangeDetailApiNameForLog(Map<String, IObjectDescribe> describeMap, Map<String, Object> detailChangeMap) {
        if (CollectionUtils.empty(detailChangeMap)) {
            return Lists.newArrayList();
        }
        Set<String> changedDetailApiNames = Sets.newLinkedHashSet();
        detailChangeMap.forEach((detailApiName, changeInfo) -> {
            Map<String, Object> changeMap = (Map<String, Object>) changeInfo;
            if (CollectionUtils.empty(changeMap)) {
                return;
            }
            //有两种以上变更，肯定需要记修改记录
            if (changeMap.size() > 1) {
                changedDetailApiNames.add(detailApiName);
                return;
            }
            //只有更新操作，只有包含需要记修改记录的字段才认为修改了
            if (changeMap.containsKey(ObjectAction.UPDATE.getActionCode())) {
                Map<String, Map<String, Object>> id2UpdateFieldMap = (Map<String, Map<String, Object>>) changeMap.get(ObjectAction.UPDATE.getActionCode());
                if (CollectionUtils.empty(id2UpdateFieldMap)) {
                    return;
                }
                IObjectDescribe detailDescribe = describeMap.get(detailApiName);
                if (id2UpdateFieldMap.keySet().stream().anyMatch(id -> {
                    Map<String, Object> updateFieldsForLog = Maps.newHashMap(id2UpdateFieldMap.get(id));
                    ObjectDataExt.of(updateFieldsForLog).removeFieldForLog(detailDescribe);
                    return CollectionUtils.notEmpty(updateFieldsForLog);
                })) {
                    changedDetailApiNames.add(detailApiName);
                }
            } else {
                changedDetailApiNames.add(detailApiName);
            }
        });
        return Lists.newArrayList(changedDetailApiNames);
    }

    private boolean updateFieldIsEmpty(IObjectData masterData, Map<String, Map<String, Map<String, Object>>> updatedFieldMap) {
        return CollectionUtils.empty(updatedFieldMap) || CollectionUtils.empty(updatedFieldMap.get(masterData.getDescribeApiName()))
                || CollectionUtils.empty(updatedFieldMap.get(masterData.getDescribeApiName()).get(masterData.getId()));
    }

    private List<LogInfo.DetailInfo> getDetailInfosForChangeDetailApiName(Map<String, IObjectDescribe> objectDescribes, List<String> changeDetailApiName) {
        if (CollectionUtils.empty(objectDescribes) || CollectionUtils.empty(changeDetailApiName)) {
            return Lists.newArrayList();
        }
        return objectDescribes.values().stream()
                .filter(x -> ObjectDescribeExt.of(x).isSlaveObject())
                .filter(x -> changeDetailApiName.contains(x.getApiName()))
                .map(x -> LogInfo.DetailInfo.builder()
                        .objectApiName(x.getApiName())
                        .objectLabel(x.getDisplayName())
                        .build())
                .collect(Collectors.toList());
    }

    private List<LogInfo.DetailInfo> getDetailInfos(Map<String, IObjectDescribe> objectDescribes, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        Set<LogInfo.DetailInfo> detailInfos = Sets.newHashSet();
        dataList.stream().filter(x -> ObjectDescribeExt.of(objectDescribes.get(x.getDescribeApiName())).isSlaveObject())
                .forEach(x -> detailInfos.add(LogInfo.DetailInfo.builder()
                        .objectApiName(x.getDescribeApiName())
                        .objectLabel(objectDescribes.get(x.getDescribeApiName()).getDisplayName())
                        .build()));
        return Lists.newArrayList(detailInfos);
    }

    @Override
    public void bulkRecordEditLog(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> dataList, List<IObjectData> dbDataList) {
        Map<String, IObjectData> dbDataMap = dbDataList.stream().collect(Collectors.toMap(DBRecord::getId, x -> x, (x, y) -> x));
        updateImportLog(user, eventType, actionType, objectDescribe, dataList, dbDataMap);
    }

    @Override
    public void updateImportLog(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> datas,
                                Map<String, IObjectData> dbDataMap) {
        updateImportLog(user, eventType, actionType, objectDescribe, datas, dbDataMap, Maps.newHashMap());
    }

    public void updateImportLog(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> datas,
                                Map<String, IObjectData> dbDataMap, Map<String, Object> extendsInfo) {
        LogExtendInfo logExtendInfo = LogExtendInfo.builder().extendsInfo(extendsInfo).build();
        bulkRecordEditLog(user, eventType, actionType, objectDescribe, datas, Lists.newArrayList(dbDataMap.values()), Lists.newArrayList(), logExtendInfo);
    }

    public void bulkRecordEditLog(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe,
                                  List<IObjectData> dataList, List<IObjectData> dbDataList,
                                  List<String> updateFields, LogExtendInfo logExtendInfo) {
        if (isModifyRecordClosed(user, objectDescribe)) {
            return;
        }
        List<IObjectData> newDataList = ObjectDataExt.copyList(dataList);
        List<IObjectData> oldDataList = ObjectDataExt.copyList(dbDataList);
        Map<String, IObjectData> dbDataMap = oldDataList.stream().collect(Collectors.toMap(DBRecord::getId, x -> x, (x, y) -> x));
        //数据id,字段apiName,字段值
        Map<String, Map<String, Object>> updateMap = newDataList.stream()
                .collect(Collectors.toMap(DBRecord::getId, x -> ObjectDataExt.of(dbDataMap.get(x.getId())).diff(x, objectDescribe)));

        Map<String, Map<String, Object>> updatedFieldMap = updateMap.entrySet().stream()
                .peek(it -> ObjectDataExt.of(it.getValue()).removeFieldForLog(objectDescribe, updateFields))
                .filter(it -> CollectionUtils.notEmpty(it.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (CollectionUtils.empty(updatedFieldMap)) {
            return;
        }
        asyncFillFieldInfo(user, objectDescribe, newDataList, updatedFieldMap, Lists.newArrayList(dbDataMap.values()));
        newDataList.forEach(data -> {
            if (CollectionUtils.empty(updatedFieldMap.get(data.getId()))) {
                return;
            }
            LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(objectDescribe, data, updatedFieldMap.get(data.getId()), dbDataMap);
            LogInfo info = getLogInfo(user, eventType, actionType, objectDescribe.getApiName(), objectDescribe.getApiName(), snapshot, snapshot.getMessage(), data.getId());
            logExtendInfo.fillLogInfo(info);
            asyncLogSender.offer(info);
        });
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData data,
                    Map<String, Object> updatedFieldMap, IObjectData dbData) {
        log(user, eventType, actionType, objectDescribe, data, updatedFieldMap, dbData, null, null, null);
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData data,
                    Map<String, Object> updatedFieldMap, IObjectData dbData, String bizId) {
        log(user, eventType, actionType, objectDescribe, data, updatedFieldMap, dbData, null, null, bizId);
    }

    @Override
    public void logWithCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> datas,
                                     Map<String, Map<String, Object>> updatedFieldMap, List<IObjectData> dbDataList,
                                     String peerName, String peerDisplayName, String message) {
        if (CollectionUtils.empty(updatedFieldMap) || CollectionUtils.empty(datas)) {
            return;
        }
        if (isModifyRecordClosed(user, objectDescribe)) {
            return;
        }
        Map<String, IObjectData> dbDataMap = CollectionUtils.nullToEmpty(dbDataList).stream().collect(Collectors.toMap(DBRecord::getId, x -> x));
        List<IObjectData> dataList = ObjectDataExt.copyList(datas);
        // 补充dataList中的__r信息
        asyncFillFieldInfo(user, objectDescribe, dataList, updatedFieldMap, dbDataList);
        dataList.forEach(x -> {
            // 重新拷贝一份再remove掉计算和统计字段的修改记录
            Map<String, Object> updateFields = Maps.newHashMap(CollectionUtils.nullToEmpty(updatedFieldMap.get(x.getId())));
            ObjectDataExt.of(updateFields).removeFieldForLog(objectDescribe);
            if (CollectionUtils.empty(updateFields)) {
                return;
            }
            LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(objectDescribe, x, updateFields, dbDataMap);

            // 记录自定义信息
            setCustomMessage(objectDescribe, message, x, snapshot);

            LogInfo info = getLogInfo(user, eventType, actionType, objectDescribe.getApiName(), objectDescribe.getApiName(), snapshot, snapshot.getMessage(), x.getId());
            // 补充info
            fillLogInfo(peerName, peerDisplayName, null, info);
            asyncLogSender.offer(info);
        });
    }

    private void fillLogInfo(String peerName, String peerDisplayName, InternationalItem internationalPeerDisplayName, LogInfo info) {
        if (StringUtils.isNotEmpty(peerName)) {
            info.setPeerName(peerName);
        }
        if (StringUtils.isNotEmpty(peerDisplayName)) {
            info.getSnapshot().setPeerDisplayName(peerDisplayName);
        }
        if (Objects.nonNull(internationalPeerDisplayName)) {
            info.getSnapshot().setInternationalPeerDisplayName(internationalPeerDisplayName);
        }
    }

    private void setCustomMessage(IObjectDescribe objectDescribe, String message, IObjectData data, LogInfo.ObjectSnapshot snapshot) {
        if (StringUtils.isBlank(message)) {
            return;
        }
        LogInfo.LintMessage txtMessage = new LogInfo.LintMessage(objectDescribe.getDisplayName(), "", "");
        LogInfo.LintMessage lintMessage = new LogInfo.LintMessage(data.getName(), data.getId(), objectDescribe.getApiName());
        LogInfo.LintMessage customMessage = new LogInfo.LintMessage(message, "", "");
        snapshot.setTextMsg(Lists.newArrayList(txtMessage, lintMessage, customMessage));
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData modifyData,
                    Map<String, Object> updatedFieldMap, IObjectData dbData, String peerName, String peerDisplayName, String bizId) {
        log(user, eventType, actionType, objectDescribe, modifyData, updatedFieldMap, dbData, peerName, peerDisplayName, bizId, Maps.newHashMap());
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData modifyData,
                    Map<String, Object> updatedFieldMap, IObjectData dbData, String peerName, String peerDisplayName,
                    String bizId, Map<String, Object> extendsInfo) {
        log(user, eventType, actionType, objectDescribe, modifyData, updatedFieldMap, dbData, peerName, peerDisplayName, null, bizId, extendsInfo);
    }

    @Override
    public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe,
                    IObjectData modifyData, Map<String, Object> updatedFieldMap, IObjectData dbData, String peerName,
                    String peerDisplayName, InternationalItem internationalPeerDisplayName, String bizId, Map<String, Object> extendsInfo) {
        if (CollectionUtils.empty(updatedFieldMap)) {
            return;
        }
        if (isModifyRecordClosed(user, objectDescribe)) {
            return;
        }
        IObjectData data = ObjectDataExt.of(modifyData).copy();
        fillFieldInfoHandle.asyncFillFieldInfo(objectDescribe, Lists.newArrayList(data), user);
        fillFieldInfoHandle.asyncFillFieldInfo(objectDescribe, Lists.newArrayList(dbData), user);
        Map<String, IObjectData> dbDataMap = Maps.newHashMap();
        if (Objects.nonNull(dbData)) {
            dbDataMap.put(dbData.getId(), dbData);
        }
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(objectDescribe, data, updatedFieldMap, dbDataMap, extendsInfo);
        LogInfo info = getLogInfo(user, eventType, actionType, objectDescribe.getApiName(), objectDescribe.getApiName(), snapshot, snapshot.getMessage(), data.getId(), bizId);
        fillLogInfo(peerName, peerDisplayName, internationalPeerDisplayName, info);
        asyncLogSender.offer(info);
    }

    @Override
    public void logByActionType(User user, EventType eventType, ActionType actionType, List<IObjectData> oldDataList, List<IObjectData> dataList, IObjectDescribe objectDescribe) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        if (isModifyRecordClosed(user, objectDescribe)) {
            return;
        }
        if (actionType == ActionType.AddEmployee
                || actionType == ActionType.AddSale
                || actionType == ActionType.RemoveEmployee
                || actionType == ActionType.RemoveSale
                || actionType == ActionType.ModifySale
                || actionType == ActionType.ChangeOwner
                || actionType == ActionType.CHANGE_PARTNER_OWNER) {
            logTeamMember(user, eventType, actionType, oldDataList, dataList, objectDescribe);
        }
    }

    @Override
    public void logWithUpdateLayout(User user, EventType eventType, ActionType actionType, ILayout before, ILayout after, String objectApiName, String textMessage, InternationalItem internationalTextMessage) {
        if (before == null && after == null) {
            return;
        }
        Map<String, Object> afterLayoutMap = LayoutExt.of(after).toMap();
        List<LogInfo.DiffData> layoutDiffData = null;
        if (before == null) {
            layoutDiffData = afterLayoutMap.entrySet().stream()
                    .map(entry -> LogInfo.DiffData.builder().field(entry.getKey()).after(entry.getValue()).build())
                    .collect(Collectors.toList());
        } else {
            Map<String, Object> beforeLayoutMap = LayoutExt.of(before).toMap();
            MapDifference<String, Object> difference = Maps.difference(beforeLayoutMap, afterLayoutMap);

            List<LogInfo.DiffData> finalLayoutDiffData = new ArrayList<>();
            CollectionUtils.nullToEmpty(difference.entriesOnlyOnLeft()).forEach((field, removeValue) -> {
                finalLayoutDiffData.add(LogInfo.DiffData.builder().field(field).before(removeValue).build());
            });
            CollectionUtils.nullToEmpty(difference.entriesOnlyOnRight()).forEach((field, createValue) -> {
                finalLayoutDiffData.add(LogInfo.DiffData.builder().field(field).after(createValue).build());
            });
            CollectionUtils.nullToEmpty(difference.entriesDiffering()).forEach((field, updateValue) -> {
                finalLayoutDiffData.add(LogInfo
                        .DiffData.builder()
                        .field(field)
                        .before(updateValue.leftValue())
                        .after(updateValue.rightValue())
                        .build());
            });
            layoutDiffData = finalLayoutDiffData;
        }

        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, objectApiName, internationalTextMessage)))
                .diffData(layoutDiffData)
                .build();
        LogInfo info = getLogInfo(user, eventType, actionType, objectApiName, getModule(), snapshot, textMessage, null, internationalTextMessage);
        asyncLogSender.offer(info);
    }

    @Override
    public void logWithUpdateLayout(User user, EventType eventType, ActionType actionType, ILayout before, ILayout after, String objectApiName, String textMessage) {
        if (before == null && after == null) {
            return;
        }
        Map<String, Object> afterLayoutMap = LayoutExt.of(after).toMap();
        List<LogInfo.DiffData> layoutDiffData = null;
        if (before == null) {
            layoutDiffData = afterLayoutMap.entrySet().stream()
                    .map(entry -> LogInfo.DiffData.builder().field(entry.getKey()).after(entry.getValue()).build())
                    .collect(Collectors.toList());
        } else {
            Map<String, Object> beforeLayoutMap = LayoutExt.of(before).toMap();
            MapDifference<String, Object> difference = Maps.difference(beforeLayoutMap, afterLayoutMap);

            List<LogInfo.DiffData> finalLayoutDiffData = new ArrayList<>();
            CollectionUtils.nullToEmpty(difference.entriesOnlyOnLeft()).forEach((field, removeValue) -> {
                finalLayoutDiffData.add(LogInfo.DiffData.builder().field(field).before(removeValue).build());
            });
            CollectionUtils.nullToEmpty(difference.entriesOnlyOnRight()).forEach((field, createValue) -> {
                finalLayoutDiffData.add(LogInfo.DiffData.builder().field(field).after(createValue).build());
            });
            CollectionUtils.nullToEmpty(difference.entriesDiffering()).forEach((field, updateValue) -> {
                finalLayoutDiffData.add(LogInfo
                        .DiffData.builder()
                        .field(field)
                        .before(updateValue.leftValue())
                        .after(updateValue.rightValue())
                        .build());
            });
            layoutDiffData = finalLayoutDiffData;
        }

        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, objectApiName)))
                .diffData(layoutDiffData)
                .build();
        LogInfo info = getLogInfo(user, eventType, actionType, objectApiName, getModule(), snapshot, textMessage, null);

        asyncLogSender.offer(info);

    }

    @Override
    public MobSearchResult mobSearchModifyRecord(String apiName, String dataId, int limit, long operationTime, User user) {
        return mobSearchModifyRecord(apiName, dataId, null, limit, operationTime, user, null, null);
    }

    @Override
    public MobSearchResult mobSearchModifyRecord(String apiName, String dataId, String operationType, int limit, long operationTime, User user, List<String> bizId, List<String> otherBizIds) {
        SearchCondition searchCondition = buildSearchConditionForMob(apiName, limit, operationTime, user, operationType);
        searchCondition.setObjectId(dataId);
        searchCondition.setBizId(bizId);
        searchCondition.setOtherBizId(otherBizIds);
        if (CollectionUtils.notEmpty(bizId)) {
            searchCondition.removeHidden();
        } else if (CollectionUtils.notEmpty(otherBizIds)) {
            searchCondition.setHidden(true);
        }
        return getMobSearchResult(searchCondition, operationType);
    }

    @Override
    public MobSearchResult mobSearchModifyRecord(User user, LogCondition condition) {
        SearchCondition searchCondition = buildCHSearchCondition(user, condition);
        searchCondition.setObjectId(condition.getObjectId());
        // 只有在用户没有指定 hidden 值时，才根据 bizIds 和 otherBizIds 来设置 hidden 值
        if (condition.getHidden() == null) {
            if (CollectionUtils.notEmpty(condition.getBizIds())) {
                searchCondition.removeHidden();
            } else if (CollectionUtils.notEmpty(condition.getOtherBizIds())) {
                searchCondition.setHidden(true);
            }
        }
        return getMobSearchResult(searchCondition, condition.getOperationalType());
    }

    @Override
    public MobSearchResult mobSearchModifyRecordForMaster(String masterId, String detailApiName, int limit, long operationTime, User user) {
        return mobSearchModifyRecordForMaster(masterId, detailApiName, (String) null, limit, operationTime, user);
    }

    @Override
    public MobSearchResult mobSearchModifyRecordForMaster(String masterId, String detailApiName, String operationType, int limit, long operationTime, User user) {
        SearchCondition searchCondition = buildSearchConditionForMob(detailApiName, limit, operationTime, user, operationType);
        searchCondition.setMasterId(masterId);
        return getMobSearchResult(searchCondition, operationType);
    }

    @Override
    public MobSearchResult mobSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationType, User user, List<String> bizId, List<String> otherBizIds) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setMasterId(masterId);
        searchCondition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        searchCondition.setModule(detailApiName);
        searchCondition.setMasterLogId(masterLogId);
        searchCondition.setCorpId(user.getTenantId());
        searchCondition.setOperationType(operationType);
        searchCondition.setBizId(bizId);
        searchCondition.setOtherBizId(otherBizIds);
        return getMobSearchResultForMasterLogId(searchCondition, operationType);
    }

    @Override
    public MobSearchResult mobSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationType, User user, List<String> bizId, List<String> otherBizIds, int limit, long operationTime) {
        SearchCondition searchCondition = buildSearchConditionForMob(detailApiName, limit, operationTime, user, operationType);
        searchCondition.setMasterId(masterId);
        searchCondition.setMasterLogId(masterLogId);
        searchCondition.setBizId(bizId);
        searchCondition.setOtherBizId(otherBizIds);
        return getMobSearchResult(searchCondition, operationType);
    }

    @Override
    public MobSearchResult mobSearchModifyRecordForMaster(User user, LogCondition condition) {
        SearchCondition searchCondition = buildCHSearchCondition(user, condition);
        searchCondition.setMasterId(condition.getMasterId());
        searchCondition.setMasterLogId(condition.getMasterLogId());
        return getMobSearchResult(searchCondition, condition.getOperationalType());
    }

    private SearchCondition getSearchConditionForObjectId(String apiName, String dataId, int limit, long operationTime, User user) {
        SearchCondition searchCondition = buildSearchConditionForMob(apiName, limit, operationTime, user, null);
        searchCondition.setObjectId(dataId);
        return searchCondition;
    }

    private SearchCondition buildSearchConditionForMob(String apiName, int limit, long operationTime, User user, String operationType) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        searchCondition.setModule(apiName);
        searchCondition.setLastOperationTime(operationTime);
        searchCondition.setPageSize(limit);
        searchCondition.setCorpId(user.getTenantId());
        searchCondition.setOperationType(operationType);
        searchCondition.setHidden(false);
        return searchCondition;
    }

    @Override
    public MobSearchRichResult mobSearchModifyRecordForMaster(String masterId, String masterApiName, List<String> detailApiNameList, int limit, long operationTime, User user) {

        //定义变量
        MobSearchRichResult mobSearchRichResult = new MobSearchRichResult();
        Map<String, Integer> totalMap = Maps.newHashMap();
        Map<String, List<ModifyRecord>> allModifyRecordListMap = Maps.newHashMap();

        //找主对象的修改记录
        SearchCondition searchCondition = getSearchConditionForObjectId(masterApiName, masterId, limit, operationTime, user);
        MobSearchResult searchResultMaster = getMobSearchResult(searchCondition);
        //写入主对象的修改记录结果
        totalMap.put(masterApiName, searchResultMaster.getTotalCount());
        allModifyRecordListMap.put(masterApiName, searchResultMaster.getModifyRecordList());

        for (String detailApiName : detailApiNameList) {
            //找所有的从对象的修改记录
            SearchCondition searchConditionDetail = new SearchCondition();
            searchConditionDetail.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
            searchConditionDetail.setLastOperationTime(operationTime);
            searchConditionDetail.setPageSize(limit);
            searchConditionDetail.setCorpId(user.getTenantId());
            searchConditionDetail.setMasterId(masterId);
            searchConditionDetail.setModule(detailApiName);
            searchConditionDetail.setHidden(false);

            MobSearchResult searchResultDetail = getMobSearchResult(searchConditionDetail);
            //写入从对象的修改记录结果
            totalMap.put(detailApiName, searchResultDetail.getTotalCount());
            allModifyRecordListMap.put(detailApiName, searchResultDetail.getModifyRecordList());
        }


        mobSearchRichResult.setTotalCountMap(totalMap);
        mobSearchRichResult.setAllModifyRecordListMap(allModifyRecordListMap);

        return mobSearchRichResult;
    }

    private MobSearchResult getMobSearchResult(SearchCondition searchCondition) {
        return getMobSearchResult(searchCondition, null);
    }

    private MobSearchResult getMobSearchResult(SearchCondition searchCondition, String operationType) {
        SearchModel.Arg arg = new SearchModel.Arg();
        MobSearchResult searchResultMaster = new MobSearchResult();
        arg.setCondition(searchCondition);
        try {
            Map<String, String> header = getHeader((String) searchCondition.get("corpId"));
            SearchModel.Result resultMaster = logServiceProxy.mobSearch(header, arg);
            searchResultMaster.setTotalCount(resultMaster.getTotalCount());
            searchResultMaster.setHasMore(BooleanUtils.isTrue(resultMaster.getHasMore()));
            searchResultMaster.setModifyRecordList(logInfosToModifyRecords(resultMaster.getResults(), operationType));
        } catch (Exception e) {
            log.error("getMobSearchResult mobSearch arg:{}", arg, e);
            searchResultMaster.setModifyRecordList(Lists.newArrayList());
        }
        return searchResultMaster;
    }

    private Map<String, String> getHeader(String tenantId) {
        Map<String, String> header = Maps.newHashMap();
        header.put("x-fs-ei", tenantId);
        header.put("FSR-GRAY_VALUE", tenantId);
        return header;
    }

    private MobSearchResult getMobSearchResultForMasterLogId(SearchCondition searchCondition, String operationType) {
        MobSearchResult searchResult = new MobSearchResult();
        SearchModel.Arg arg = new SearchModel.Arg();
        arg.setCondition(searchCondition);
        Map<String, String> header = getHeader((String) searchCondition.get("corpId"));
        SearchModel.Result result = logServiceProxy.searchDetail(header, arg);
        searchResult.setHasMore(BooleanUtils.isTrue(result.getHasMore()));
        searchResult.setModifyRecordList(logInfosToModifyRecords(result.getResults(), operationType));
        return searchResult;
    }

    @Override
    public WebSearchResult webSearchModifyRecord(String apiName, String dataId, int limit, int pageNumber, User user) {
        return webSearchModifyRecord(apiName, dataId, null, limit, pageNumber, user, null, null);
    }

    @Override
    public WebSearchResult webSearchModifyRecord(String apiName, String dataId, String operationalType, int limit, int pageNumber, User user,
                                                 List<String> bizId, List<String> otherBizIds) {
        SearchCondition searchCondition = buildSearchConditionForWeb(apiName, operationalType, limit, pageNumber, user);
        searchCondition.setObjectId(dataId);
        searchCondition.setBizId(bizId);
        searchCondition.setOtherBizId(otherBizIds);
        if (CollectionUtils.notEmpty(bizId)) {
            searchCondition.removeHidden();
        } else if (CollectionUtils.notEmpty(otherBizIds)) {
            searchCondition.setHidden(true);
        }
        return getSearchResultForObjectId(searchCondition, operationalType);
    }

    @Override
    public WebSearchResult webSearchModifyRecord(User user, LogCondition condition) {
        SearchCondition searchCondition = buildCHSearchCondition(user, condition);
        searchCondition.setObjectId(condition.getObjectId());
        // 只有在用户没有指定 hidden 值时，才根据 bizIds 和 otherBizIds 来设置 hidden 值
        if (condition.getHidden() == null) {
            if (CollectionUtils.notEmpty(condition.getBizIds())) {
                searchCondition.removeHidden();
            } else if (CollectionUtils.notEmpty(condition.getOtherBizIds())) {
                searchCondition.setHidden(true);
            }
        }
        return getSearchResultForObjectId(searchCondition, condition.getOperationalType());
    }

    private SearchCondition buildSearchConditionForWeb(String detailApiName, String operationalType, int limit, int pageNumber, User user) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        searchCondition.setModule(detailApiName);
        searchCondition.setPageNum(pageNumber);
        searchCondition.setPageSize(limit);
        searchCondition.setCorpId(user.getTenantId());
        searchCondition.setOperationType(operationalType);
        searchCondition.setHidden(false);
        return searchCondition;
    }

    private SearchCondition buildCHSearchCondition(User user, LogCondition condition) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setModule(condition.getModule());
        searchCondition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        searchCondition.setCorpId(user.getTenantId());
        searchCondition.setOperationType(condition.getOperationalType());
        searchCondition.setBizId(condition.getBizIds());
        searchCondition.setOtherBizId(condition.getOtherBizIds());
        // 如果用户指定了 hidden 参数，则使用用户指定的值，否则使用默认值 false
        searchCondition.setHidden(condition.getHidden() != null ? condition.getHidden() : false);
        searchCondition.setPageNum(condition.getPageNumber());
        searchCondition.setPageSize(condition.getPageSize());
        searchCondition.setFromOperationTime(condition.getOperationTimeFrom());
        searchCondition.setToOperationTime(condition.getOperationTimeTo());
        searchCondition.setLastOperationTime(condition.getOperationTime());
        searchCondition.setNeedReturnCount(BooleanUtils.isNotFalse(condition.getNeedReturnCount()));
        return searchCondition;
    }


    private WebSearchResult getSearchResultForObjectId(SearchCondition searchCondition, String operationalType) {
        WebSearchResult searchResult = new WebSearchResult();
        SearchModel.Arg arg = new SearchModel.Arg();
        arg.setCondition(searchCondition);
        Map<String, String> header = getHeader((String) searchCondition.get("corpId"));
        SearchModel.Result result = logServiceProxy.modifyLog(header, arg);
        searchResult.setPageInfo(toPageInfo(result));
        searchResult.setHasMore(BooleanUtils.isTrue(result.getHasMore()));
        searchResult.setModifyRecordList(logInfosToModifyRecords(result.getResults(), operationalType));
        return searchResult;
    }

    private WebSearchResult getSearchResultForMasterLogId(SearchCondition searchCondition, String operationalType) {
        WebSearchResult searchResult = new WebSearchResult();
        SearchModel.Arg arg = new SearchModel.Arg();
        arg.setCondition(searchCondition);
        Map<String, String> header = getHeader((String) searchCondition.get("corpId"));
        SearchModel.Result result = logServiceProxy.searchDetail(header, arg);
        searchResult.setPageInfo(toPageInfo(result));
        searchResult.setHasMore(BooleanUtils.isTrue(result.getHasMore()));
        searchResult.setModifyRecordList(logInfosToModifyRecords(result.getResults(), operationalType));
        return searchResult;
    }

    private WebSearchResult getSearchResultForMasterId(SearchCondition searchCondition, String operationalType) {
        WebSearchResult searchResult = new WebSearchResult();
        SearchModel.Arg arg = new SearchModel.Arg();
        arg.setCondition(searchCondition);
        Map<String, String> header = getHeader((String) searchCondition.get("corpId"));
        SearchModel.Result result = logServiceProxy.webSearch(header, arg);
        searchResult.setPageInfo(toPageInfo(result));
        searchResult.setHasMore(BooleanUtils.isTrue(result.getHasMore()));
        searchResult.setModifyRecordList(logInfosToModifyRecords(result.getResults(), operationalType));
        return searchResult;
    }

    @Override
    public WebSearchResult webSearchModifyRecordForMaster(String masterId, String detailApiName, int limit, int pageNumber, User user) {
        return webSearchModifyRecordForMaster(masterId, detailApiName, null, limit, pageNumber, user);
    }

    @Override
    public WebSearchResult webSearchModifyRecordForMaster(String masterId, String detailApiName, String operationalType, int limit, int pageNumber, User user) {
        SearchCondition searchCondition = buildSearchConditionForWeb(detailApiName, operationalType, limit, pageNumber, user);
        searchCondition.setMasterId(masterId);
        return getSearchResultForMasterId(searchCondition, operationalType);
    }

    @Override
    public WebSearchResult webSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationalType, User user,
                                                          List<String> bizId, List<String> otherBizIds) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        searchCondition.setModule(detailApiName);
        searchCondition.setCorpId(user.getTenantId());
        searchCondition.setOperationType(operationalType);
        searchCondition.setMasterId(masterId);
        searchCondition.setMasterLogId(masterLogId);
        searchCondition.setOtherBizId(otherBizIds);
        searchCondition.setBizId(bizId);
        return getSearchResultForMasterLogId(searchCondition, operationalType);
    }

    @Override
    public WebSearchResult webSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationalType,
                                                          User user, List<String> bizId, List<String> otherBizIds, int limit, int pageNumber) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        searchCondition.setModule(detailApiName);
        searchCondition.setCorpId(user.getTenantId());
        searchCondition.setOperationType(operationalType);
        searchCondition.setMasterId(masterId);
        searchCondition.setMasterLogId(masterLogId);
        searchCondition.setOtherBizId(otherBizIds);
        searchCondition.setBizId(bizId);
        searchCondition.setPageNum(pageNumber);
        searchCondition.setPageSize(limit);
        return getSearchResultForMasterLogId(searchCondition, operationalType);
    }

    @Override
    public WebSearchResult webSearchModifyRecordForMaster(User user, LogCondition condition) {
        SearchCondition searchCondition = buildCHSearchCondition(user, condition);
        searchCondition.setMasterId(condition.getMasterId());
        searchCondition.setMasterLogId(condition.getMasterLogId());
        return getSearchResultForMasterLogId(searchCondition, condition.getOperationalType());
    }

    @Override
    public LogInfo getLogById(String apiName, String logId, User user) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        searchCondition.setLogId(logId);
        searchCondition.setPageNum(1);
        searchCondition.setPageSize(1);
        searchCondition.setModule(apiName);
        searchCondition.setCorpId(user.getTenantId());

        SearchModel.Arg arg = new SearchModel.Arg();
        arg.setCondition(searchCondition);
        Map<String, String> header = getHeader(user.getTenantId());
        List<LogInfo> logInfos = logServiceProxy.modifyLog(header, arg).getResults();
        if (logInfos == null || logInfos.isEmpty()) {
            throw new ValidateException("current dataId is error");
        }
        return logInfos.get(0);
    }

    @Override
    public GetLogByModuleResult getLogByModule(String module, int pageSize, int pageNumber, User user) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        searchCondition.setModule(module);
        searchCondition.setPageNum(pageNumber);
        searchCondition.setPageSize(pageSize);
        searchCondition.setCorpId(user.getTenantId());
        searchCondition.setHidden(false);
        SearchModel.Arg arg = new SearchModel.Arg();
        arg.setCondition(searchCondition);
        Map<String, String> header = getHeader(user.getTenantId());
        SearchModel.Result result = logServiceProxy.webSearch(header, arg);
        GetLogByModuleResult controllerResult = new GetLogByModuleResult();
        controllerResult.setPageInfo(toPageInfo(result));
        controllerResult.setLogInfos(logInfosToModifyRecords(result.getResults(), null));
        return controllerResult;
    }

    @Override
    public GetManageLogListResult getManageLogList(User user, String module, int pageSize, int pageNumber, List<Filter> filters, String sortField, int sortType) {
        GetManageLogListResult result = new GetManageLogListResult();
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setCorpId(user.getTenantId());
        searchCondition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        searchCondition.setPageSize(pageSize);
        searchCondition.setPageNum(pageNumber);
        searchCondition.setModule(module);
        searchCondition.setHidden(false);
        if (!StringUtils.isEmpty(sortField)) {
            boolean asc = sortType == 1;
            searchCondition.addSortInfo(sortField, asc);
        }
        if (filters != null) {
            for (Filter filter : filters) {
                if ("operationTime".equals(filter.getFieldName())) {
                    if (filter.getComparison() == 4) { //大于等于
                        searchCondition.setFromOperationTime(Long.parseLong(filter.getFilterValue()));
                    } else if (filter.getComparison() == 6) {//小于等于
                        searchCondition.setToOperationTime(Long.parseLong(filter.getFilterValue()));
                    }
                    continue;
                }
                searchCondition.put(filter.getFieldName(),
                        filter.getFilterValue());
            }
        }
        SearchModel.Arg arg = new SearchModel.Arg();
        arg.setCondition(searchCondition);
        Map<String, String> header = getHeader(user.getTenantId());
        SearchModel.Result searchResult;
        try {
            searchResult = logServiceProxy.webSearch(header, arg);
            result.setPageInfo(toPageInfo(searchResult));
            result.setMsgs(searchResult.getResults());
        } catch (RestProxyInvokeException e) {
            throw new NotElementPresentException(e.getMessage(), e);
        }
        return result;
    }

    @Override
    public SearchFunctionModel.Result getFunctionLog(User user, String logId, String functionApiName, Date operationTimeFrom, Boolean success, Date operationTimeTo, Map<String, Boolean> sorts, String traceId, String name, int pageNumber, int pageSize) {
        SearchFunctionModel.Arg arg = new SearchFunctionModel.Arg();
        arg.setFunctionApiName(functionApiName);
        arg.setTenantId(user.getTenantId());
        arg.setOperationTimeTo(operationTimeTo);
        arg.setOperationTimeFrom(operationTimeFrom);
        arg.setSuccess(success);
        arg.setSorts(sorts);
        arg.setPage(pageNumber);
        arg.setPageSize(pageSize);
        arg.setTraceId(traceId);
        arg.setName(name);
        Map<String, String> header = getHeader(user.getTenantId());
        SearchFunctionModel.Result result = logServiceProxy.searchFunctionLog(header, arg);
        return result;
    }

    @Override
    public SearchFunctionLogV2.Result getFunctionLogV2(User user, String logId, String functionApiName, String traceId, Date operationTimeFrom, Boolean success, Date operationTimeTo, Map<String, Boolean> sorts, String name, int pageSize, boolean nextPage) {
        SearchFunctionLogV2.Arg arg = new SearchFunctionLogV2.Arg();
        arg.setTenantId(user.getTenantId());
        arg.setLogId(logId);
        arg.setFunctionApiName(functionApiName);
        arg.setTraceId(traceId);
        arg.setOperationTimeFrom(operationTimeFrom);
        arg.setOperationTimeTo(operationTimeTo);
        arg.setSuccess(success);
        arg.setSorts(sorts);
        arg.setName(name);
        arg.setPageSize(pageSize);
        arg.setNextPage(nextPage);
        Map<String, String> header = getHeader(user.getTenantId());
        return logServiceProxy.searchFunctionLogV2(header, arg);
    }

    @Override
    public SearchFunctionDetailModel.Result getFunctionLogDetail(User user, String traceId, String logId) {
        SearchFunctionDetailModel.Arg arg = new SearchFunctionDetailModel.Arg();
        arg.setLogId(logId);
        arg.setTenantId(user.getTenantId());
        Map<String, String> header = getHeader(user.getTenantId());
        SearchFunctionDetailModel.Result result = logServiceProxy.searchFunctionDetailLog(header, arg);

        return result;
    }

    @Override
    public SearchFunctionDetailModel.Result getNewFunctionLogDetail(User user, String traceId, String logId) {
        SearchFunctionDetailModel.Arg arg = new SearchFunctionDetailModel.Arg();
        arg.setLogId(logId);
        arg.setTenantId(user.getTenantId());
        Map<String, String> header = getHeader(user.getTenantId());
        SearchFunctionDetailModel.Result result = logServiceProxy.getFunctionEsLogDetail(header, arg);
        return result;
    }

    @Override
    public SearchFunctionEsLogDetail.Result searchFunctionEsLogDetail(User user, SearchFunctionEsLogDetail.Arg arg) {
        Map<String, String> header = getHeader(user.getTenantId());
        arg.setTenantId(user.getTenantIdInt());
        SearchFunctionEsLogDetail.Result result;
        try {
            result = logServiceProxy.searchFunctionEsLogDetail(header, arg);
        } catch (RestProxyInvokeException e) {
            throw new NotElementPresentException(e.getMessage(), e);
        }
        List<FunctionEsLogDetail> functionEsLogDetailList = result.getLogs();
        if (CollectionUtils.empty(functionEsLogDetailList)) {
            return result;
        }
        List<String> objApiNames = functionEsLogDetailList.stream()
                .map(FunctionEsLogDetail::getBindingApiName)
                .collect(Collectors.toList());
        try {
            List<IObjectDescribe> objectDescribes = objectDescribeService.findDescribeListByApiNames(user.getTenantId(), objApiNames);
            Map<String, String> apiNames = objectDescribes.stream()
                    .collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName, (t1, t2) -> t2));
            functionEsLogDetailList.forEach(detail -> {
                String displayName = apiNames.get(detail.getBindingApiName());
                if (!Strings.isNullOrEmpty(displayName)) {
                    detail.setBindingObjName(displayName);
                }
            });
        } catch (MetadataServiceException e) {
            log.error("findDescribeListByApiNames tenantId:{},objectApiName:{}", user.getTenantId(), objApiNames, e);
            return result;
        }
        return result;
    }

    @Override
    public SearchFunctionEsLog.Result searchFunctionEsLog(User user, SearchFunctionEsLog.Arg arg) {
        Map<String, String> header = getHeader(user.getTenantId());
        arg.setTenantId(user.getTenantIdInt());
        SearchFunctionEsLog.Result result = logServiceProxy.searchFunctionEsLog(header, arg);
        List<FunctionLogInfo> logs = result.getLogs();
        if (org.springframework.util.CollectionUtils.isEmpty(logs)) {
            return result;
        }
        List<String> objApiNames = logs.stream().map(log -> log.getBindingApiName()).collect(Collectors.toList());
        try {
            List<IObjectDescribe> objectDescribes = objectDescribeService.findDescribeListByApiNames(user.getTenantId(), objApiNames);
            Map<String, String> apiNames = objectDescribes.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName, (t1, t2) -> t2));
            logs.forEach(log -> {
                String displayName = apiNames.get(log.getBindingApiName());
                if (!Strings.isNullOrEmpty(displayName)) {
                    log.setBindingObjName(displayName);
                }
            });
        } catch (MetadataServiceException e) {
            log.error("findDescribeListByApiNames tenantId:{},objectApiName:{}", user.getTenantId(), objApiNames, e);
            return result;
        }
        return result;
    }

    @Override   // 记录管理后台的审计日志
    public void logUdefFunction(User user, EventType eventType, ActionType actionType, String objectApiName, String apiName, IUdefFunction function) {
        User newUser = user;
        String displayName = "";
        if (Strings.isNullOrEmpty(user.getUserName())) {
            newUser = orgService.getUser(user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser());
        }
        if (function == null) {
            function = udefFunctionService.findFunctionByApiName(user.getTenantId(), apiName, objectApiName);
        }
        if (Objects.isNull(function)) {
            log.warn("findFunctionByApiName tenantId:{},apiName:{},objectApiName:{}", user.getTenantId(), apiName, objectApiName);
            return;
        }
        try {
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), objectApiName);
            displayName = describe == null ? "" : describe.getDisplayName();
        } catch (MetadataServiceException e) {
            log.error("findByTenantIdAndDescribeApiName tenantId:{},objectApiName:{}", user.getTenantId(), objectApiName, e);
        }
        String textMessage = I18N.text(I18NKey.HANDLE_CUSTOM_FUNCTION, function.getFunctionName(), displayName);
        LogInfo info = LogInfo.builder()
                .operationTime(System.currentTimeMillis())
                .operation(eventType.getId())
                .bizOperationName(actionType.getId())
                .corpId(newUser.getTenantId())
                .userId(newUser.getUserId())
                .userName(newUser.getUserName())
                .appId("CRM")
                .module("UserDefineFunc")
                .objectName(objectApiName)
                .textMessage(textMessage)
                .snapshot(LogInfo.ObjectSnapshot.builder().textMsg(
                        Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, objectApiName))).build())
                .build();
        asyncLogSender.offer(info);
    }

    @Override
    public void logTemporaryRights(User user, EventType eventType, ActionType actionType, String objectApiName, String jsonStr) {
        String textMessage = I18N.text(I18NKey.TEMPORARY_PRIVILEGE_MANAGER, findDisplayName(user, objectApiName));
        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, objectApiName)))
                .snapshot(JSON.parseObject(jsonStr))
                .build();
        LogInfo logInfo = getLogInfo(user, eventType, actionType, objectApiName, DATA_PRIVILEGE_MANAGEMENT.getCode(), snapshot
                , textMessage, null);
        asyncLogSender.offer(logInfo);
    }

    @Override
    public void logDataPermission(User user, EventType eventType, ActionType actionType, List<LogInfo.ObjectSnapshot> snapshots) {
        snapshots.forEach(snapshot -> {
            LogInfo logInfo = getLogInfo(user, eventType, actionType, snapshot.getObjectApiNameByTextMsg(),
                    DATA_PRIVILEGE_MANAGEMENT.getCode(), snapshot, snapshot.getMessage(), null);
            asyncLogSender.offer(logInfo);
        });
    }

    @Override
    public void logRelatedTeamDataPermission(User user, IObjectDescribe objectDescribe, Map<String, Set<String>> addPermissionRecords, Map<String, Set<String>> removePermissionRecords) {
        StringBuilder addPermissionLogText = new StringBuilder();
        for (Map.Entry<String, Set<String>> addPermissionRecord : addPermissionRecords.entrySet()) {
            String roleType = addPermissionRecord.getKey();
            Set<String> addPermissionRecordValue = addPermissionRecord.getValue();
            if (CollectionUtils.notEmpty(addPermissionRecordValue)) {
                TeamMember.Role role = TeamMember.Role.of(roleType);
                StringBuilder content = new StringBuilder();
                for (String text : addPermissionRecords.get(roleType)) {
                    content.append(",").append(text);
                }
                content = new StringBuilder(content.toString().replaceFirst(",", ""));
                addPermissionLogText.append(I18N.text(I18NKey.RELATED_TEAM_DATA_PERMISSION_UPDATE_LOG_DETAIL, role.getLabel(), content.toString()));
            }
        }

        StringBuilder removePermissionLogText = new StringBuilder();
        for (Map.Entry<String, Set<String>> removePermissionRecord : removePermissionRecords.entrySet()) {
            String roleType = removePermissionRecord.getKey();
            Set<String> removePermissionRecordValue = removePermissionRecord.getValue();
            if (CollectionUtils.notEmpty(removePermissionRecordValue)) {
                TeamMember.Role role = TeamMember.Role.of(roleType);
                StringBuilder content = new StringBuilder();
                for (String text : removePermissionRecords.get(roleType)) {
                    content.append(",").append(text);
                }
                content = new StringBuilder(content.toString().replaceFirst(",", ""));
                removePermissionLogText.append(I18N.text(I18NKey.RELATED_TEAM_DATA_PERMISSION_REMOVE_LOG_DETAIL, role.getLabel(), content.toString()));
            }
        }

        if (StringUtils.isNotEmpty(addPermissionLogText.toString()) || StringUtils.isNotEmpty(removePermissionLogText.toString())) {
            String logContent = I18N.text(I18NKey.RELATED_TEAM_DATA_PERMISSION_LOG, objectDescribe.getDisplayName(), addPermissionLogText + removePermissionLogText.toString());
            log(user, EventType.MODIFY, ActionType.UPDATE_RELATED_TEAM_DATA_PERMISSION, objectDescribe.getApiName(), logContent);
        }
    }

    @Override
    public void logRuleSetting(User user, EventType eventType, ActionType actionType, String objectApiName, String textMessage) {
        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder().textMsg(
                Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, objectApiName))).build();
        LogInfo info = getLogInfo(user, eventType, actionType, objectApiName, "35", snapshot, textMessage, null);

        asyncLogSender.offer(info);
    }

    private String findDisplayName(User user, String objectApiName) {
        try {
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), objectApiName);
            if (describe == null) {
                return objectApiName;
            }
            return describe.getDisplayName();
        } catch (MetadataServiceException e) {
            log.error("findByTenantIdAndDescribeApiName tenantId:{},objectApiName:{}", user.getTenantId(), objectApiName, e);
        }
        return objectApiName;
    }

    private void logTeamMember(User user, EventType eventType, ActionType actionType, List<IObjectData> oldDataList, List<IObjectData> newdataList, IObjectDescribe objectDescribe) {
        //获取所有的团队成员的id
        Map<String, List<String>> memberIds = Maps.newHashMap();
        getTeamMemberIds(oldDataList, memberIds);
        getTeamMemberIds(newdataList, memberIds);

        if (CollectionUtils.empty(memberIds) && CollectionUtils.empty(memberIds.values())) {
            return;
        }

        // 获取企业名称{人员ID:企业信息}
        Map<String, TenantInfo> userTenantMap = getUserTenantMap(oldDataList, newdataList, user);

        Map<String, List<TeamMemberInfo.Member>> memberInfos = Maps.newHashMap();

        for (String type : memberIds.keySet()) {
            if (CollectionUtils.empty(memberIds.get(type))) {
                continue;
            }
            //获取相关团队成员id，去重
            List<String> uniqueMemberIds = memberIds.get(type).stream().distinct().collect(Collectors.toList());
            if (TeamMember.MemberType.EMPLOYEE.equals(TeamMember.MemberType.of(type))) {
                List<UserInfo> userInfos = orgService.getUserNameByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds);
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                userInfos.forEach(userInfo -> member.add(TeamMemberInfo.Member.builder().id(userInfo.getId()).name(userInfo.getName()).build()));
                memberInfos.put(TeamMember.MemberType.EMPLOYEE.getValue(), member);
            }
            if (TeamMember.MemberType.GROUP.getValue().equals(type)) {
                List<QueryGroupByIds.UserGroupInfo> groupInfos = orgService.getGroupInfoByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds);
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                groupInfos.forEach(groupInfo -> member.add(TeamMemberInfo.Member.builder().id(groupInfo.getId()).name(groupInfo.getName()).build()));
                memberInfos.put(TeamMember.MemberType.GROUP.getValue(), member);
            }
            if (TeamMember.MemberType.DEPARTMENT.getValue().equals(type)) {
                List<QueryDeptInfoByDeptIds.DeptInfo> deptInfoIds = orgService.getDeptInfoNameByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds);
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                deptInfoIds.forEach(userInfo -> member.add(TeamMemberInfo.Member.builder().id(userInfo.getDeptId()).name(userInfo.getDeptName()).build()));
                memberInfos.put(TeamMember.MemberType.DEPARTMENT.getValue(), member);
            }
            if (TeamMember.MemberType.OUT_TENANT_GROUP.getValue().equals(type)) {
                List<QueryTenantGroupByIds.TenantGroupInfo> tenantGroupInfos = orgService.getTenantGroupInfoByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds);
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                tenantGroupInfos.forEach(tenantGroupInfo -> member.add(TeamMemberInfo.Member.builder().id(tenantGroupInfo.getId()).name(tenantGroupInfo.getName()).build()));
                memberInfos.put(TeamMember.MemberType.OUT_TENANT_GROUP.getValue(), member);
            }
            if (TeamMember.MemberType.ROLE.getValue().equals(type)) {
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                List<QueryUserRoleInfo.RoleInfo> roleInfos = userRoleInformationService.queryRoleInfoByRoleCode(user, uniqueMemberIds);
                roleInfos.forEach(roleInfo -> member.add(TeamMemberInfo.Member.builder().id(roleInfo.getRoleCode()).name(roleInfo.getRoleName()).build()));
                memberInfos.put(TeamMember.MemberType.ROLE.getValue(), member);
            }
        }

        diffRelatedTeamAndRecord(user, eventType, actionType, oldDataList, newdataList, objectDescribe, userTenantMap, memberInfos);
    }


    private void getTeamMemberIds(List<IObjectData> dataList, Map<String, List<String>> teamMemberIds) {
        dataList.forEach(data -> {
            List<TeamMemberInfoPoJo> teamMemberInfoPoJos = ObjectDataExt.of(data).getRelevantTeamFromObjectData();
            teamMemberInfoPoJos.forEach(teamMemberInfoPoJo -> {
                List<String> teamMemberEmployee = teamMemberInfoPoJo.getTeamMemberEmployee();
                String teamMemberType = TeamMember.MemberType.of(teamMemberInfoPoJo.getTeamMemberType()).getValue();
                List<String> oldTeamMemberIds = teamMemberIds.getOrDefault(teamMemberType, Lists.newArrayList());
                oldTeamMemberIds.addAll(teamMemberEmployee);
                teamMemberIds.put(teamMemberType, oldTeamMemberIds);
            });
        });
    }

    private void diffRelatedTeamAndRecord(User user, EventType eventType, ActionType actionType, List<IObjectData> oldDatas, List<IObjectData> newdatas, IObjectDescribe objectDescribe, Map<String, TenantInfo> userTenantMap, Map<String, List<TeamMemberInfo.Member>> memberInfos) {
        List<TeamRoleInfo> teamRoleInfos = Lists.newArrayList();
        if (TeamMember.isTeamRoleGray(user.getTenantId())) {
            teamRoleInfos.addAll(teamMemberRoleService.queryTeamRoleInfo(user.getTenantId(), objectDescribe.getApiName()));
        }
        for (IObjectData data : newdatas) {
            Optional<IObjectData> oldDataOp = oldDatas.stream()
                    .filter(f -> Objects.equals(f.getId(), data.getId()))
                    .findFirst();
            if (!oldDataOp.isPresent()) {
                continue;
            }
            IObjectData oldData = oldDataOp.get();

            List<TeamMemberInfo.Msg> msgs = Lists.newArrayList();
            List<LogInfo.LintMessage> textMsg = Lists.newArrayList();
            if (actionType == ActionType.ModifySale) {
                modifyRecordInitManager.getProvider(ActionType.AddEmployee.getId()).logTeamMember(oldData, data, objectDescribe, memberInfos, msgs, textMsg, userTenantMap, teamRoleInfos);
                modifyRecordInitManager.getProvider(ActionType.ModifySale.getId()).logTeamMember(oldData, data, objectDescribe, memberInfos, msgs, textMsg, userTenantMap, teamRoleInfos);
                modifyRecordInitManager.getProvider(ActionType.RemoveEmployee.getId()).logTeamMember(oldData, data, objectDescribe, memberInfos, msgs, textMsg, userTenantMap, teamRoleInfos);
                msgs.forEach(x -> {
                    if (ActionType.ModifySale.getId().equals(x.getOperationType())) {
                        x.setOperationType(ActionType.MODIFY.getId());
                    }
                    if (ActionType.RemoveEmployee.getId().equals(x.getOperationType()) || ActionType.RemoveSale.getId().equals(x.getOperationType())) {
                        x.setOperationType(ActionType.REMOVE.getId());
                    }
                    if (ActionType.AddEmployee.getId().equals(x.getOperationType()) || ActionType.AddSale.getId().equals(x.getOperationType())) {
                        x.setOperationType(ActionType.INCREASE.getId());
                    }
                });
                // 过滤掉只修改读写权限而为添加记录的数据
                List<Map<String, Object>> oldRelevantTeam = (List<Map<String, Object>>) oldData.get(ObjectDataExt.RELEVANT_TEAM);
                List<String> teamMemberEmployee = CollectionUtils.nullToEmpty(oldRelevantTeam).stream()
                        .filter(t -> Objects.nonNull(t.get("teamMemberEmployee")))
                        .map(team -> ((List<String>) (team.get("teamMemberEmployee"))).get(0))
                        .filter(t -> StringUtils.isNotEmpty(t))
                        .collect(Collectors.toList());
                msgs = msgs.stream().filter(msg -> {
                    if (StringUtils.equals(ActionType.INCREASE.getId(), msg.getOperationType())) {
                        return !teamMemberEmployee.contains(msg.getMember().getId());
                    }
                    return true;
                }).collect(Collectors.toList());
            } else {
                modifyRecordInitManager.getProvider(actionType.getId()).logTeamMember(oldData, data, objectDescribe, memberInfos, msgs, textMsg, userTenantMap, teamRoleInfos);
            }
            if (CollectionUtils.notEmpty(textMsg) || CollectionUtils.notEmpty(msgs)) {
                logByCustomized(user, eventType, actionType, objectDescribe.getApiName(), data.getId(), textMsg, msgs);
            }
        }
    }

    private Map<String, TenantInfo> getUserTenantMap(List<IObjectData> oldDataList, List<IObjectData> dataList, User user) {
        Map<String, TenantInfo> result = Maps.newHashMap();
        @SuppressWarnings("unchecked")
        Map<String, String> userTenantMap = (Map<String, String>) dataList.stream()
                .filter(data -> Objects.nonNull(data.get(ObjectDataExt.RELEVANT_TEAM)))
                .flatMap(data -> ((List) data.get(ObjectDataExt.RELEVANT_TEAM)).stream())
                .filter(teamMember -> Objects.nonNull(((Map) teamMember).get(TeamMember.TEAM_MEMBER_OUT_EMPLOYEE_EI)))
                .filter(teamMember -> Objects.nonNull(((Map) teamMember).get(TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME)))
                .filter(teamMember -> Objects.nonNull(((Map) teamMember).get(TeamMember.TEAM_MEMBER_OUT_EMPLOYEE_EI)))
                .collect(Collectors.toMap(
                        t -> {
                            Object userId = ((Map) t).get(TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME);
                            if (userId instanceof List) {
                                return ((List) userId).get(0).toString();
                            }
                            return userId.toString();
                        },
                        t -> {
                            Object outTenantId = ((Map) t).get(TeamMember.TEAM_MEMBER_OUT_EMPLOYEE_EI);
                            if (outTenantId instanceof List) {
                                return ((List) outTenantId).get(0).toString();
                            }
                            return outTenantId.toString();
                        },
                        (k1, k2) -> k1
                ));

        @SuppressWarnings("unchecked")
        Map<String, String> oldUserTenantMap = (Map<String, String>) oldDataList.stream()
                .filter(data -> Objects.nonNull(data.get(ObjectDataExt.RELEVANT_TEAM)))
                .flatMap(data -> ((List) data.get(ObjectDataExt.RELEVANT_TEAM)).stream())
                .filter(teamMember -> Objects.nonNull(((Map) teamMember).get(TeamMember.TEAM_MEMBER_OUT_EMPLOYEE_EI)))
                .filter(teamMember -> Objects.nonNull(((Map) teamMember).get(TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME)))
                .filter(teamMember -> Objects.nonNull(((Map) teamMember).get(TeamMember.TEAM_MEMBER_OUT_EMPLOYEE_EI)))
                .collect(Collectors.toMap(
                        t -> {
                            Object userId = ((Map) t).get(TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME);
                            if (userId instanceof List) {
                                return ((List) userId).get(0).toString();
                            }
                            return userId.toString();
                        },
                        t -> {
                            Object outTenantId = ((Map) t).get(TeamMember.TEAM_MEMBER_OUT_EMPLOYEE_EI);
                            if (outTenantId instanceof List) {
                                return ((List) outTenantId).get(0).toString();
                            }
                            return outTenantId.toString();
                        },
                        (k1, k2) -> k1
                ));

        userTenantMap.putAll(oldUserTenantMap);

        Set<String> tenantIds = Sets.newHashSet(userTenantMap.values());
        // 根据企业ID获取企业信息
        Set<TenantInfo> tenantInfos = getTenantInfos(tenantIds, user);
        for (TenantInfo tenantInfo : tenantInfos) {
            for (Map.Entry<String, String> entry : userTenantMap.entrySet()) {
                if (entry.getValue().equals(tenantInfo.getId())) {
                    result.put(entry.getKey(), tenantInfo);
                }
            }
        }
        return result;
    }

    @Override
    public Set<TenantInfo> getTenantInfos(Set<String> tenantIds, User user) {
        Set<TenantInfo> tenantInfos = Sets.newHashSet();
        if (CollectionUtils.empty(tenantIds)) {
            return tenantInfos;
        }
        BatchGetEnterpriseCards.Arg arg = BatchGetEnterpriseCards.Arg.builder()
                .upstreamTenantId(user.getTenantIdInt())
                .outerTenantIds(tenantIds.stream().map(Integer::parseInt).collect(Collectors.toList()))
                .build();
        BatchGetEnterpriseCards.Result result = connectionServiceProxy.batchGetEnterpriseCards(user.getTenantId(), "x_app_framework", arg);
        if (!result.isSuccess()) {
            return tenantInfos;
        }
        for (BatchGetEnterpriseCards.Card card : result.getData()) {
            tenantInfos.add(TenantInfo.builder()
                    .id(String.valueOf(card.getOuterTenantId()))
                    .name(card.getEnterpriseName()).build());
        }
        return tenantInfos;
    }

    @Override
    public QueryFunctionVerificationLog.Result queryFunctionValidationLog(User user, String sessionId) {
        QueryFunctionVerificationLog.Arg arg = new QueryFunctionVerificationLog.Arg();
        arg.setSessionId(sessionId);
        arg.setTenantId(user.getTenantId());
        QueryFunctionVerificationLog.Result result = logServiceProxy.queryFunctionVerificationLog(getHeader(user.getTenantId()), arg);
        return result;
    }

    @Override
    public InsertFunctionVerificationLog.Result insertFunctionValidationLog(User user, List<FunctionValidationLog> logs) {
        InsertFunctionVerificationLog.Arg arg = new InsertFunctionVerificationLog.Arg(logs);
        InsertFunctionVerificationLog.Result result = logServiceProxy.insertFunctionVerificationLog(getHeader(user.getTenantId()), arg);
        return result;
    }

    public void logByCustomized(User user, EventType eventType, ActionType actionType, String apiName, String dataID,
                                List<LogInfo.LintMessage> textMsg, List<TeamMemberInfo.Msg> msgs) {
        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder().textMsg(textMsg).msgs(msgs).build();
        LogInfo info = getLogInfo(user, eventType, actionType, apiName, apiName, snapshot, snapshot.getMessage(), dataID);
        asyncLogSender.offer(info);
    }


    private PageInfo toPageInfo(SearchModel.Result result) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(result.getTotalCount());
        pageInfo.setPageCount(result.getTotalPage());
        pageInfo.setPageNumber(result.getPage());
        pageInfo.setPageSize(result.getPageSize());
        return pageInfo;
    }

    private List<ModifyRecord> logInfosToModifyRecords(List<LogInfo> logInfos, String operationalType) {
        if (logInfos != null) {
            List<ModifyRecord> records = new ArrayList<>(logInfos.size());
            for (LogInfo logInfo : logInfos) {
                records.add(logInfoToModifyRecord(logInfo, operationalType));
            }
            return records;
        }
        return Lists.newArrayList();
    }

    private ModifyRecord logInfoToModifyRecord(LogInfo logInfo, String operationalType) {
        if (Objects.isNull(logInfo.getPeerName()) || Objects.isNull(operationalType) || !newAuditLogListForActionType.contains(logInfo.getBizOperationName())) {
            return modifyRecordInitManager.getProvider("OldDataModel").getModifyRecord(logInfo);
        }
        return modifyRecordInitManager.getProvider(logInfo.getBizOperationName()).getModifyRecord(logInfo);
    }

    private String getModule() {
        return "UserDefineObj";
    }

    @Override
    public void deleteAuditLog(String tenantId, String describeApiName, String dataId, String otherBizId) {
        DeleteAuditLog.Parameter parameter = DeleteAuditLog.Parameter.builder()
                .tenantId(tenantId)
                .objectAPIName(describeApiName)
                .objectId(dataId)
                .otherBizId(otherBizId)
                .build();
        DeleteAuditLog.Arg arg = new DeleteAuditLog.Arg(Lists.newArrayList(parameter));
        try {
            Map<String, String> header = getHeader(tenantId);
            DeleteAuditLog.Result result = logServiceProxy.deleteAuditLog(header, arg);
            if (!result.isSuccess()) {
                log.warn("delete auditLog failed,arg:{},result:{}", arg, result);
            }
        } catch (Exception e) {
            log.error("delete auditLog failed,arg:{}", arg, e);
        }
    }

    public void deleteLog(User user, DeleteLog.Arg arg) {
        SearchCondition condition = new SearchCondition();
        condition.setCorpId(user.getTenantId());
        condition.setModules(Lists.newArrayList(arg.getModule()));
        condition.setObjectIds(arg.getObjectIds());
        condition.setDeleteOnlyEs(arg.getDeleteOnlyEs());
        condition.setFromOperationTime(arg.getOperationTimeFrom());
        condition.setToOperationTime(arg.getOperationTimeTo());
        try {
            Map<String, String> header = getHeader(user.getTenantId());
            logServiceProxy.deleteLog(header, condition);
        } catch (Exception e) {
            log.error("deleteLog failed,arg:{}", condition, e);
        }
    }

    @Override
    public LoginLog.Result getLoginLog(User user, LoginLog.Arg arg) {
        LoginLog.Result result = LoginLog.Result.builder().build();
        Map<String, String> header = getHeader(user.getTenantId());
        LoginLog.LoginInfoCondition condition;
        if (Objects.isNull(arg.getLoginCondition())) {
            condition = LoginLog.LoginInfoCondition.builder().build();
        } else {
            condition = arg.getLoginCondition();
        }
        condition.setTenantId(user.getTenantId());
        condition.setId(arg.getId());
        condition.setPageSize(arg.getPageSize());
        condition.setIsPrePage(arg.getIsPrePage());
        condition.setIsChangePageSize(arg.getIsChangePageSize());
        try {
            result = logServiceProxy.getLoginLog(header, condition);
        } catch (Exception e) {
            log.error("get login log failed,arg:{}", arg, e);
        }
        return result;
    }

    @Override
    public AuditLog.Result getAuditLog(User user, AuditLog.Arg arg) {
        AuditLog.Result result = AuditLog.Result.builder().build();
        Map<String, String> header = getHeader(user.getTenantId());
        SearchCondition condition = buildSearchCondition(arg.getAuditLogCondition());
        condition.setIsPrePage(arg.getIsPrePage());
        condition.setIsChangePageSize(arg.getIsChangePageSize());
        condition.setCorpId(user.getTenantId());
        condition.setPageSize(arg.getPageSize());
        if (AuditLogConfig.isGrayAuditLogChRead(user.getTenantId())) {
            condition.setTurnLogId(arg.getLogId());
        } else {
            condition.setLogId(arg.getLogId());
        }
        condition.setSearchAfter(arg.getSearchAfter());
        SearchModel.Arg searchArg = new SearchModel.Arg();
        searchArg.setCondition(condition);
        try {
            SearchModel.Result searchAuditLog = logServiceProxy.searchAuditLog(header, searchArg);
            if (Objects.nonNull(searchAuditLog) && CollectionUtils.notEmpty(searchAuditLog.getResults())) {
                result.setResults(searchAuditLog.getResults().stream().map(x -> convertAuditLog(x, arg.getIsExport())).collect(Collectors.toList()));
                result.setTotalCount(searchAuditLog.getTotalCount());
                result.setPageSize(searchAuditLog.getPageSize());
                result.setHasMore(searchAuditLog.getHasMore());
            }
        } catch (Exception e) {
            log.error("get audit log failed,arg:{}", arg, e);
        }
        return result;
    }

    @Override
    public AuditLog.Result getAuditLogCount(User user, AuditLog.Arg arg) {
        AuditLog.Result result = AuditLog.Result.builder().build();
        Map<String, String> header = getHeader(user.getTenantId());
        SearchCondition condition = buildSearchCondition(arg.getAuditLogCondition());
        condition.setIsPrePage(arg.getIsPrePage());
        condition.setIsChangePageSize(arg.getIsChangePageSize());
        condition.setCorpId(user.getTenantId());
        condition.setPageSize(arg.getPageSize());
        if (AuditLogConfig.isGrayAuditLogChRead(user.getTenantId())) {
            condition.setTurnLogId(arg.getLogId());
        } else {
            condition.setLogId(arg.getLogId());
        }
        SearchModel.Arg searchArg = new SearchModel.Arg();
        searchArg.setCondition(condition);
        try {
            SearchModel.Result searchAuditLog = logServiceProxy.getAuditLogCount(header, searchArg);
            if (Objects.nonNull(searchAuditLog)) {
                result.setTotalCount(searchAuditLog.getTotalCount());
            }
        } catch (Exception e) {
            log.error("getAuditLogCount failed,arg:{}", arg, e);
        }
        return result;
    }

    @Override
    public ModifyLog.Result getModifyLog(User user, ModifyLog.Arg arg) {
        ModifyLog.Result result = new ModifyLog.Result();
        List<ModifyLog.ModifyLogInfo> modifyLogInfos = Lists.newArrayList();
        Map<String, String> header = getHeader(user.getTenantId());
        SearchCondition condition = buildSearchCondition(arg.getCondition());
        if (AuditLogConfig.isGrayAuditLogChRead(user.getTenantId())) {
            condition.setTurnLogId(arg.getCondition().getLogId());
        } else {
            condition.setSearchAfter(arg.getCondition().getSearchAfter());
        }
        condition.setCorpId(user.getTenantId());
        condition.setPageSize(arg.getPageSize());
        SearchModel.Arg searchArg = new SearchModel.Arg();
        searchArg.setCondition(condition);
        try {
            SearchModel.Result searchAuditLog = logServiceProxy.searchAuditLog(header, searchArg);
            if (Objects.nonNull(searchAuditLog) && CollectionUtils.notEmpty(searchAuditLog.getResults())) {
                List<LogInfo> results = searchAuditLog.getResults();
                for (LogInfo logInfo : results) {
                    ModifyRecord modifyRecord = logInfoToModifyRecord(logInfo, "");
                    ModifyLog.ModifyLogInfo modifyLogInfo = new ModifyLog.ModifyLogInfo();
                    modifyLogInfo.setObjectData(modifyRecord.getObjectData());
                    modifyLogInfo.setLogId(logInfo.getLogId());
                    modifyLogInfo.setDataId(logInfo.getObjectId());
                    modifyLogInfo.setObjectApiName(logInfo.getObjectName());
                    modifyLogInfo.setBizOperationName(logInfo.getBizOperationName());
                    modifyLogInfo.setSearchAfter(logInfo.getSearchAfter());
                    modifyLogInfo.setOperationTime(logInfo.getOperationTime());
                    modifyLogInfos.add(modifyLogInfo);
                }
                result.setModifyLogInfos(modifyLogInfos);
                result.setPageSize(arg.getPageSize());
                result.setTotalCount(searchAuditLog.getTotalCount());
                result.setHasMore(searchAuditLog.getHasMore());
            }
        } catch (Exception e) {
            log.error("getModifyLog failed,arg:{}", arg, e);
            return result;
        }
        return result;
    }

    public SearchCondition buildSearchCondition(AuditLogInfo logInfo) {
        SearchCondition condition = new SearchCondition();
        condition.setLogId(logInfo.getLogId());
        condition.setCorpId(logInfo.getCorpId());
        condition.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        condition.setFromOperationTime(logInfo.getOperationTimeFrom());
        condition.setToOperationTime(logInfo.getOperationTimeTo());
        condition.setModule(logInfo.getModule());
        condition.setObjectId(logInfo.getObjectId());
        condition.setUserIds(logInfo.getUserIds());
        condition.setUserName(logInfo.getUserName());
        condition.setLevel(logInfo.getLevel());
        condition.setTextMessage(logInfo.getTextMessage());
        condition.setObjectApiName(logInfo.getObjectName());
        condition.setBizOperationNames(logInfo.getBizOperationNames());
        condition.setBizOperationName(logInfo.getBizOperationName());
        condition.setOperationObject(logInfo.getOperationObject());
        return condition;
    }

    public AuditLogInfo convertAuditLog(LogInfo logInfo, Boolean isExport) {
        AuditLogInfo auditLogInfo = AuditLogInfo.builder().build();
        auditLogInfo.setLogId(logInfo.getLogId());
        auditLogInfo.setAppId(logInfo.getAppId());
        auditLogInfo.setHidden(logInfo.isHidden());
        auditLogInfo.setCorpId(logInfo.getCorpId());
        auditLogInfo.setMasterLogId(logInfo.getMasterLogId());
        auditLogInfo.setBizId(logInfo.getBizId());
        auditLogInfo.setBizOperationName(logInfo.getBizOperationName());
        auditLogInfo.setLevel(logInfo.getLevel());
        auditLogInfo.setModule(logInfo.getModule());
        auditLogInfo.setObjectId(logInfo.getObjectId());
        if (SPECIFIC_OBJECT_NAME.contains(logInfo.getObjectName())) {
            auditLogInfo.setObjectName("");
        } else {
            auditLogInfo.setObjectName(logInfo.getObjectName());
        }
        auditLogInfo.setOperation(logInfo.getOperation());
        auditLogInfo.setOtherBizId(logInfo.getOtherBizId());
        auditLogInfo.setOperationTime(logInfo.getOperationTime());
        auditLogInfo.setOutTenantId(logInfo.getOutTenantId());
        auditLogInfo.setUserId(logInfo.getUserId());
        if (User.SUPPER_ADMIN_USER_ID.equals(logInfo.getUserId())) {
            auditLogInfo.setUserName(I18NExt.getOrDefault(I18NKey.SYSTEM, "系统")); // ignoreI18n
        } else {
            auditLogInfo.setUserName(logInfo.getUserName());
        }
        auditLogInfo.setTextMessage(logInfo.convertToI18nTextMessage());
        auditLogInfo.setPeerName(logInfo.getPeerName());
        auditLogInfo.setOperationObject(logInfo.getOperationObject());
        auditLogInfo.setMasterId(logInfo.getMasterId());
        LogInfo.ObjectSnapshot snapshot = logInfo.getSnapshot();
        if (Objects.nonNull(snapshot)) {
            auditLogInfo.setMessageList(snapshot.getLinkMessage());
            if (BooleanUtils.isTrue(isExport)) {
                auditLogInfo.setSnapshot(snapshot);
                auditLogInfo.setModifyDataList(snapshot.getObjectDataList());
            }
        }
        if (CollectionUtils.notEmpty(logInfo.getSearchAfter())) {
            auditLogInfo.setSearchAfter(logInfo.getSearchAfter());
        }
        return auditLogInfo;
    }
}
