package com.facishare.paas.appframework.core.predef.handler.flowstartcallback;

import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.facade.AuditLogServiceFacade;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2023/10/12.
 */
@Component
@HandlerProvider(name = "defaultFlowStartCallbackAfterRecordLogHandler")
public class DefaultFlowStartCallbackAfterRecordLogHandler implements FlowStartCallbackHandler {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private AuditLogServiceFacade auditLogServiceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        ApprovalFlowTriggerType triggerType = arg.getInterfaceArg().triggerType();
        switch (triggerType) {
            case UPDATE:
                recordEditLog(context, arg);
                break;
            case INVALID:
                recordInvalidLog(context, arg);
                break;
            default:
                break;
        }
        return new Result();
    }

    private void recordInvalidLog(HandlerContext context, Arg arg) {
        auditLogServiceFacade.recordInvalidLog(context.getUser(), Lists.newArrayList(arg.data()), arg.detailDataMap(),
                arg.getDescribeMap());
    }

    private void recordEditLog(HandlerContext context, Arg arg) {
        serviceFacade.log(context.getUser(), EventType.MODIFY, ActionType.Modify, arg.getObjectDescribe(), arg.data(),
                arg.getUpdateFieldMap(), arg.dbData());
    }
}
