package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.metadata.dto.scene.ITenantScene;
import com.google.common.collect.Lists;

import java.util.List;

public class StandardAddOuterSceneAction extends BaseOuterSceneAction {
    @Override
    protected Result doAct(Arg arg) {
        ITenantScene tenantScene = infraServiceFacade.createTenantScene(scene, arg.getDescribeApiName(), arg.getExtendAttribute(), actionContext.getUser());
        if (Boolean.TRUE.equals(arg.getScene().getClearCustomConfig())) {
            api2Display2TypeForDefault = Lists.newArrayList();
        }
        extendSceneConfig();
        return new Result(tenantScene);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }
}
