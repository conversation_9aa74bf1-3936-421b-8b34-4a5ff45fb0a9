package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.domain.DomainProvider;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouwr on 2022/7/6.
 */
@Component
@DomainProvider(name = "_remote_IncrementUpdate")
public class RemoteIncrementUpdateActionDomainPlugin implements IncrementUpdateActionDomainPlugin {

    @Autowired
    private IncrementUpdateActionDomainPluginProxy proxy;

    @Override
    public IncrementUpdateActionDomainPlugin.Result before(ActionContext context, IncrementUpdateActionDomainPlugin.Arg arg) {
        return doPost(context, arg, BEFORE);
    }

    @Override
    public Result preAct(ActionContext context, Arg arg) {
        return doPost(context, arg, PRE_ACT);
    }

    @Override
    public Result postAct(ActionContext context, Arg arg) {
        return doPost(context, arg, POST_ACT);
    }

    @Override
    public IncrementUpdateActionDomainPlugin.Result after(ActionContext context, IncrementUpdateActionDomainPlugin.Arg arg) {
        return doPost(context, arg, AFTER);
    }

    @Override
    public IncrementUpdateActionDomainPlugin.Result finallyDo(ActionContext context, IncrementUpdateActionDomainPlugin.Arg arg) {
        return doPost(context, arg, FINALLY_DO);
    }

    private IncrementUpdateActionDomainPlugin.Result doPost(ActionContext context, IncrementUpdateActionDomainPlugin.Arg arg, String method) {
        Map<String, String> header = RestUtils.buildHeaders(context.getUser(), arg.getPluginDescribe().supportIdempotent(method));
        String url = arg.getPluginDescribe().getRestApiUrl(method);
        if (Strings.isNullOrEmpty(url)) {
            return new IncrementUpdateActionDomainPlugin.Result();
        }
        return proxy.post(url, arg, header).getData();
    }


}
