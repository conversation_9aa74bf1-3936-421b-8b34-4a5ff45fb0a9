package com.facishare.paas.appframework.core.predef.service.dto.option.dependence;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.options.bo.OptionDependence;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/8/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FieldDependenceDTO {
    private String describeApiName;
    private String fieldApiName;
    private String childFieldName;
    private List<OptionDependenceDTO> dependence;

    public static FieldDependenceDTO from(SelectFieldDependence selectFieldDependence) {
        return FieldDependenceDTO.builder()
                .describeApiName(selectFieldDependence.getDescribeApiName())
                .fieldApiName(selectFieldDependence.getFieldApiName())
                .childFieldName(selectFieldDependence.getChildFieldName())
                .dependence(OptionDependenceDTO.fromList(selectFieldDependence.getDependence()))
                .build();
    }

    public static List<FieldDependenceDTO> fromList(List<SelectFieldDependence> selectFieldDependenceList) {
        if (CollectionUtils.empty(selectFieldDependenceList)) {
            return Collections.emptyList();
        }
        return selectFieldDependenceList.stream()
                .map(FieldDependenceDTO::from)
                .collect(Collectors.toList());
    }

    public SelectFieldDependence convert() {
        List<OptionDependence> dependenceList = CollectionUtils.nullToEmpty(dependence).stream()
                .map(OptionDependenceDTO::convert)
                .collect(Collectors.toList());
        return new SelectFieldDependence(describeApiName, fieldApiName, childFieldName, dependenceList);
    }
}
