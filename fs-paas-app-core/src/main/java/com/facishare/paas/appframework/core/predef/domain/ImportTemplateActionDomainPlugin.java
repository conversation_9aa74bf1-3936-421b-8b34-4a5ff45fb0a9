package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * Created by zhouwr on 2022/1/18.
 */
public interface ImportTemplateActionDomainPlugin extends ActionDomainPlugin<ImportTemplateActionDomainPlugin.Arg, ImportTemplateActionDomainPlugin.Result> {

    String CUSTOM_HEADER = "customHeader";

    Result customHeader(ActionContext context, Arg arg);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg extends DomainPlugin.Arg {
        private List<String> headerFieldList;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends DomainPlugin.Result {
        private List<String> headerFieldListToRemove;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class RestResult extends BaseAPIResult {
        private Result data;
    }

}
