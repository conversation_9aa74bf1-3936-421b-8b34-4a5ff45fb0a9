package com.facishare.paas.appframework.core.predef.service.dto.button;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.handler.HandlerInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface UpdateButton {

    @Data
    class Arg {
        @JSONField(name = "M1")
        private String button;

        @JSONField(name = "M2")
        private List<ActionPojo> post_actions;

        @JSONField(name = "M3")
        private List<String> roles;

        @JsonProperty("sourceInfo")
        @JSONField(name = "sourceInfo")
        String sourceInfo;

        @JSONField(name = "handler_list")
        @JsonProperty("handler_list")
        private List<HandlerInfo> handlerList;
    }

    @Data
    class Result {
        @JSONField(name = "M2")
        private boolean success;
    }
}
