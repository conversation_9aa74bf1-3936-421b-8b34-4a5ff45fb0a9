package com.facishare.paas.appframework.core.predef.handler.edit;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.core.predef.handler.AbstractValidateArgSaveBeforeHandler;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectReferenceWrapper;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/1/13.
 */
@Component
@HandlerProvider(name = "defaultValidateArgEditBeforeHandler")
public class DefaultValidateArgEditBeforeHandler extends AbstractValidateArgSaveBeforeHandler<EditActionHandler.Arg, EditActionHandler.Result> {

    @Autowired
    private SaveActionServiceFacade saveActionServiceFacade;

    @Override
    protected EditActionHandler.Result buildResult(HandlerContext context, EditActionHandler.Arg arg) {
        return new EditActionHandler.Result();
    }

    @Override
    protected void validateDataList(HandlerContext context, EditActionHandler.Arg arg) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BATCH_VALIDATE_EDIT_DATA_CONSTRAINT_BY_FIELDS, context.getTenantId())) {
            saveActionServiceFacade.batchValidateEditDataConstraintByFields(context.getUser(), arg);
        }
    }

    @Override
    protected void validateDataStatus(HandlerContext context, EditActionHandler.Arg arg) {
        super.validateDataStatus(context, arg);
        validateMasterDataStatus(context, arg);
    }

    private void validateMasterDataStatus(HandlerContext context, EditActionHandler.Arg arg) {
        //审批例外人不校验锁定状态和生命状态
        if (arg.isAssigneesExceptional()) {
            return;
        }
        IObjectData objectData = arg.objectData();
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        //根据objectData的生命状态和锁定状态来判断数据是否有权限进行该操作
        serviceFacade.checkActionByLockStatusAndLifeStatus(objectData, ObjectAction.UPDATE, context.getUser(),
                objectDescribe.getApiName(), false);

        validateDataLifeStatus(objectData);
    }

    private void validateDataLifeStatus(IObjectData objectData) {
        if (ObjectDataExt.of(objectData).isInChange()) {
            String name = StringUtils.trimToEmpty(objectData.getName());
            throw new ValidateException(I18N.text(I18NKey.ONGOING_APPROVAL_FLOW_AND_NO_CURRENT_OPERATION_POSSIBLE, name));
        }
    }

    @Override
    protected void validateDetail(HandlerContext context, EditActionHandler.Arg arg, IObjectDescribe detailDescribe, List<IObjectData> detailDataList) {
        super.validateDetail(context, arg, detailDescribe, detailDataList);
        validateDetailDataStatus(context, arg, detailDescribe);
    }

    private void validateDetailDataStatus(HandlerContext context, EditActionHandler.Arg arg, IObjectDescribe detailDescribe) {
        //审批例外人不校验锁定状态和生命状态
        if (arg.isAssigneesExceptional()) {
            return;
        }

        String detailApiName = detailDescribe.getApiName();
        List<IObjectData> dataToUpdate = arg.detailsToUpdate().stream().filter(x -> detailApiName.equals(x.getDescribeApiName())).collect(Collectors.toList());
        List<IObjectData> dataToDelete = arg.detailsToDelete().stream().filter(x -> detailApiName.equals(x.getDescribeApiName())).collect(Collectors.toList());

        if (CollectionUtils.empty(dataToUpdate) && CollectionUtils.empty(dataToDelete)) {
            return;
        }

        //根据objectData的生命状态和锁定状态来判断数据是否有权限进行该操作
        if (CollectionUtils.notEmpty(dataToDelete)) {
            serviceFacade.checkActionByLockStatusAndLifeStatus(dataToDelete, ObjectAction.INVALID,
                    context.getUser(), detailApiName, false);
        }

        if (CollectionUtils.notEmpty(dataToUpdate)) {
            serviceFacade.checkActionByLockStatusAndLifeStatus(dataToUpdate, ObjectAction.UPDATE,
                    context.getUser(), detailApiName, false);
        }
    }

    @Override
    protected void validateLookupData(HandlerContext context, EditActionHandler.Arg arg, IObjectData objectData, IObjectDescribe describe) {
        //灰度是否只校验值有变化的lookup字段
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.VALIDATE_LOOKUP_FIELD_ONLY_CHANGED_GRAY_EI, context.getTenantId())) {
            //获取数据有变化的字段
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            List<ObjectReferenceWrapper> fields = describeExt.getActiveReferenceFieldDescribes();
            Map<String, Object> updateFields = ObjectDataExt.of(arg.getDbMasterData()).diff(objectData, describe);
            List<ObjectReferenceWrapper> changedFields = fields.stream().filter(a -> updateFields.containsKey(a.getApiName())).collect(Collectors.toList());
            super.doValidateLookupData(context, objectData, describe, changedFields);
        } else {
            super.validateLookupData(context, arg, objectData, describe);
        }
    }
}
