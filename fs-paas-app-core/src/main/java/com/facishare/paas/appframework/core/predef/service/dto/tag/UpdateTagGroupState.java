package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface UpdateTagGroupState {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JsonProperty("group_api_name")
        @JSONField(name = "group_api_name")
        String groupApiName;

        @JsonProperty("is_enable")
        @JSONField(name = "is_enable")
        Boolean enable;
    }
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        Boolean success;
    }
}
