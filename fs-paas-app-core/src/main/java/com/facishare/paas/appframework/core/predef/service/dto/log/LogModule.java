package com.facishare.paas.appframework.core.predef.service.dto.log;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * Created by rensx on 2017/10/31.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties({"default"})
public class LogModule {

    @JSONField(name = "M1")
    private String name;

    @JSO<PERSON>ield(name = "M2")
    private String id;

    @JSONField(name = "M3")
    @JsonProperty("isDefault")
    private boolean isDefault;

    private Set<String> idList;

    public LogModule(String name, String id, boolean isDefault) {
        this.name = name;
        this.id = id;
        this.isDefault = isDefault;
    }
}
