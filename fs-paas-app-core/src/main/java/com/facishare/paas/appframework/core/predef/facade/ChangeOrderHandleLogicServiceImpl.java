package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardEffectiveAction;
import com.facishare.paas.appframework.core.predef.facade.dto.CreateChangeOrder;
import com.facishare.paas.appframework.core.predef.facade.dto.DiffDetailData;
import com.facishare.paas.appframework.core.predef.facade.dto.EffectiveAndUpdateData;
import com.facishare.paas.appframework.core.predef.facade.dto.MergeChangeOrderData;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.appframework.metadata.changeorder.*;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.dto.UpdateMasterAndDetailData;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.RuleCalculateResult;
import com.facishare.paas.appframework.metadata.relation.EditCalculateParam;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2023/4/6
 */
@Service("changeOrderHandlerLogicService")
@Slf4j
public class ChangeOrderHandleLogicServiceImpl implements ChangeOrderHandlerLogicService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ChangeOrderLogicService changeOrderLogicService;
    @Autowired
    private ChangeOrderRuleLogicService changeOrderRuleLogicService;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private ObjectMappingService objectMappingService;

    @Override
    public CreateChangeOrder.Result doCreateChangeOrder(User user, CreateChangeOrder.Arg arg) {
        IObjectDescribe originalDescribe = arg.getDescribe();
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), originalDescribe.getApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.UNSUPPORTED_CREATE_CHANGE_ORDER));
        }
        IObjectDescribe changeDescribe = changeOrderLogicService.findChangeOrderDescribeByOriginalApiName(user, originalDescribe.getApiName())
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.CHANGE_ORDER_OBJECT_NOT_EXIST_OR_DELETED)));

        // 校验变更状态
        IObjectData dbMasterObjectData = serviceFacade.findObjectData(user.getTenantId(), arg.getMasterData().getId(), originalDescribe);
        validateByChangeStatus(user, originalDescribe, dbMasterObjectData);

        MtChangeOrderRule changeOrderRule = findChangeRule(user, originalDescribe.getApiName());

        OriginalAndChangeDescribes describes = changeOrderLogicService.findDescribesByOriginalApiName(user, originalDescribe.getApiName());
        BaseObjectSaveAction.Arg actionArg = buildAddActionArg(user, arg, changeOrderRule, describes);
        ActionContext actionContext = new ActionContext(RequestContextManager.getContext(), changeDescribe.getApiName(), StandardAction.Add.toString());
        BaseObjectSaveAction.Result actionResult = serviceFacade.triggerRemoteAction(actionContext, actionArg, BaseObjectSaveAction.Result.class);

        IObjectData data = actionResult.getObjectData().toObjectData();
        // 修改原单变更状态
        updateOriginalDataChangeStatus(user, dbMasterObjectData);
        return CreateChangeOrder.Result.buildSuccess(changeDescribe.getApiName(), data.getId());
    }

    @Override
    public CreateChangeOrder.Result doModifyChangeOrder(User user, CreateChangeOrder.Arg arg) {
        IObjectDescribe originalDescribe = arg.getDescribe();
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), originalDescribe.getApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.UNSUPPORTED_CREATE_CHANGE_ORDER));
        }
        IObjectData masterData = arg.getMasterData();
        String changedDataId = masterData.get(ObjectDataExt.CHANGED_DATA_ID, String.class);
        if (Strings.isNullOrEmpty(changedDataId)) {
            throw new ValidateException(I18NExt.text(I18NKey.CHANGE_ORDER_MASTER_DATA_NOT_EXIST_OR_DELETED));
        }
        IObjectDescribe changeDescribe = changeOrderLogicService.findChangeOrderDescribeByOriginalApiName(user, originalDescribe.getApiName())
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.CHANGE_ORDER_OBJECT_NOT_EXIST_OR_DELETED)));

        // 校验变更状态
        IObjectData dbMasterObjectData = serviceFacade.findObjectData(user.getTenantId(), arg.getMasterData().getId(), originalDescribe);
        validateByChangeStatus(user, originalDescribe, dbMasterObjectData, changedDataId, true);

        MtChangeOrderRule changeOrderRule = findChangeRule(user, originalDescribe.getApiName());
        OriginalAndChangeDescribes describes = changeOrderLogicService.findDescribesByOriginalApiName(user, originalDescribe.getApiName());
        BaseObjectSaveAction.Arg actionArg = buildEditActionArg(user, arg, changeOrderRule, describes);
        ActionContext actionContext = new ActionContext(RequestContextManager.getContext(), changeDescribe.getApiName(), StandardAction.Edit.toString());
        BaseObjectSaveAction.Result actionResult = serviceFacade.triggerRemoteAction(actionContext, actionArg, BaseObjectSaveAction.Result.class);
//        BaseObjectSaveAction.Result actionResult = serviceFacade.triggerAction(actionContext, actionArg, BaseObjectSaveAction.Result.class);

        IObjectData data = actionResult.getObjectData().toObjectData();
        // 修改原单变更状态
        updateOriginalDataChangeStatus(user, dbMasterObjectData);
        return CreateChangeOrder.Result.buildSuccess(changeDescribe.getApiName(), data.getId());
    }

    private void updateOriginalDataChangeStatus(User user, IObjectData objectData) {
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put(ObjectDataExt.CHANGED_STATUS, ChangeOrderChangedStatus.IN_CHANGE.getCode());
        serviceFacade.updateWithMap(user, objectData, fieldMap);
        // TODO: 2023/4/16 修改记录
    }

    @Override
    @Transactional
    public void saveChangeOrderOriginalData(User user, SaveMasterAndDetailData.Arg saveArg, IObjectData objectData, Map<String, List<IObjectData>> detailData) {
        IObjectData masterObjectData = saveArg.getMasterObjectData();
        Map<String, IObjectDescribe> describeMap = saveArg.getObjectDescribes();
        IObjectDescribe describe = describeMap.get(masterObjectData.getDescribeApiName());

        if (Objects.nonNull(objectData)) {
            objectData.setId(masterObjectData.getId());
            changeOrderLogicService.saveChangeOrderOriginalData(user, describe.getApiName(), Lists.newArrayList(objectData), false);
        }
        if (CollectionUtils.empty(detailData)) {
            return;
        }
        saveArg.getDetailObjectData().forEach((describeApiName, dataList) -> {
            Map<String, IObjectData> objectDataMap = dataList.stream()
                    .map(ObjectDataExt::of)
                    .filter(it -> !Strings.isNullOrEmpty(it.getOriginalDetailDataId()))
                    .collect(Collectors.toMap(ObjectDataExt::getOriginalDetailDataId, ObjectDataExt::getObjectData));
            if (CollectionUtils.empty(objectDataMap)) {
                return;
            }
            List<IObjectData> objectDataList = detailData.get(describeApiName);
            if (CollectionUtils.empty(objectDataList)) {
                return;
            }
            List<IObjectData> result = Lists.newArrayList();
            for (IObjectData data : objectDataList) {
                String originalDetailData = ObjectDataExt.of(data).getOriginalDetailDataId();
                IObjectData changeData = objectDataMap.get(originalDetailData);
                if (Objects.nonNull(changeData)) {
                    data.setId(changeData.getId());
                    result.add(data);
                }
            }
            IObjectDescribe objectDescribe = describeMap.get(describeApiName);
            changeOrderLogicService.saveChangeOrderOriginalData(user, objectDescribe.getApiName(), result, false);
        });
    }

    @Override
    @Transactional
    public void modifyChangeOrderOriginalData(User user, UpdateMasterAndDetailData.Arg updateArg, IObjectData objectData, Map<String, List<IObjectData>> detailData) {
        IObjectData masterObjectData = updateArg.getMasterObjectData();
        if (Objects.nonNull(objectData)) {
            objectData.setId(masterObjectData.getId());
            changeOrderLogicService.saveChangeOrderOriginalData(user, masterObjectData.getDescribeApiName(), Lists.newArrayList(objectData), false);
        }
        saveChangeOrderOriginalDetailData(user, detailData, updateArg.getDetailsToAdd(), false);
        saveChangeOrderOriginalDetailData(user, detailData, updateArg.getDetailsToUpdate(), true);
    }

    private void saveChangeOrderOriginalDetailData(User user, Map<String, List<IObjectData>> originalDetailMap, Map<String, List<IObjectData>> changeDetailMap, boolean isUpdate) {
        if (CollectionUtils.empty(changeDetailMap)) {
            return;
        }
        changeDetailMap.forEach((describeApiName, dataList) -> {
            Map<String, IObjectData> objectDataMap = dataList.stream()
                    .map(ObjectDataExt::of)
                    .filter(it -> !Strings.isNullOrEmpty(it.getOriginalDetailDataId()))
                    .collect(Collectors.toMap(ObjectDataExt::getOriginalDetailDataId, ObjectDataExt::getObjectData));
            if (CollectionUtils.empty(objectDataMap)) {
                return;
            }
            List<IObjectData> objectDataList = originalDetailMap.get(describeApiName);
            if (CollectionUtils.empty(objectDataList)) {
                return;
            }
            List<IObjectData> result = Lists.newArrayList();
            for (IObjectData data : objectDataList) {
                String originalDetailData = ObjectDataExt.of(data).getOriginalDetailDataId();
                IObjectData changeData = objectDataMap.get(originalDetailData);
                if (Objects.nonNull(changeData)) {
                    data.setId(changeData.getId());
                    result.add(data);
                }
            }
            changeOrderLogicService.saveChangeOrderOriginalData(user, describeApiName, result, isUpdate);
        });
    }

    @Override
    public void validateChangeRuleWithChangeData(User user, IObjectDescribe changeDescribe, IObjectData changeObjectData,
                                                 MtChangeOrderRule.CalibrationType type) {
        String originalDescribeApiName = changeDescribe.getOriginalDescribeApiName();
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), originalDescribeApiName)) {
            return;
        }
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), originalDescribeApiName);
        String changeRuleName = changeObjectData.get(ObjectDataExt.CHANGE_ORDER_RULE, String.class);
        MtChangeOrderRule changeRule = findOneChangeRuleAndValidate(user, originalDescribeApiName, changeRuleName, type);
        if (Objects.isNull(changeRule)) {
            return;
        }
        IRule rule = changeRule.toValidateRule();
        if (Objects.isNull(rule)) {
            return;
        }
        // 将字段映射回去
        OriginalAndChangeDescribes describes = OriginalAndChangeDescribes.buildByDescribe(describe, changeDescribe);
        UpdateMasterAndDetailData.Arg mappingObjectData = mappingObjectData(user, changeRule, describes, changeObjectData, Collections.emptyMap(), false);
        // 校验验证规则
        RuleCalculateResult validateResult = expressionCalculateLogicService.validateRules(describe,
                Lists.newArrayList(mappingObjectData.getMasterObjectData()), Lists.newArrayList(rule));
        if (validateResult.isMatch()) {
            throw new ValidateException(rule.getMessage());
        }
    }

    private UpdateMasterAndDetailData.Arg mappingObjectData(User user, MtChangeOrderRule changeRule, OriginalAndChangeDescribes describes,
                                                            IObjectData objectData, Map<String, List<IObjectData>> detailsDataMap, boolean onlyChangeField) {

        ObjectDataMapping.Result result = ObjectDataMapping.builder()
                .user(user)
                .changeOrderLogicService(changeOrderLogicService)
                .objectMappingService(objectMappingService)
                .changeRule(changeRule)
                .describes(describes)
                .onlyChangeField(onlyChangeField)
                .build()
                .mapping2OriginalData(objectData, detailsDataMap);

        Map<String, Object> updateMap = ObjectDataExt.of(result.getMasterObjectData()).toMap();
        return UpdateMasterAndDetailData.Arg.builder()
                .masterObjectData(result.getMasterObjectData())
                .detailsToAdd(result.getDetailsToAdd())
                .detailsToUpdate(result.getDetailsToUpdate())
                .detailsToDelete(result.getDetailsToDelete())
                .incrementUpdate(true)
                .incrementUpdateDetail(true)
                .toUpdateMap(updateMap)
                .build();
    }

    @Override
    public List<MtChangeOrderRule> findByDescribeApiName(User user, String describeApiName) {
        return changeOrderRuleLogicService.findByDescribeApiName(user, describeApiName);    // 前台变更对象
    }

    @Override
    public List<IObjectData> findAndMergeObjectDataWithOriginalData(User user, IObjectDescribe describe, List<IObjectData> dataList) {
        return changeOrderLogicService.findAndMergeObjectDataWithOriginalData(user, describe, dataList);
    }

    @Override
    @Transactional
    public EffectiveAndUpdateData.Result effectiveAndUpdateData(User user, EffectiveAndUpdateData.Arg arg) {
        IObjectDescribe describe = arg.getDescribe();
        String originalDescribeApiName = describe.getOriginalDescribeApiName();
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), originalDescribeApiName)) {
            throw new ValidateException(I18NExt.text(I18NKey.UNSUPPORTED_OPERATION));
        }
        IObjectData objectData = arg.getObjectData();
        OriginalAndChangeDescribes describes = changeOrderLogicService.findDescribesByOriginalApiName(user, originalDescribeApiName);
        String changeOrderRule = ObjectDataExt.of(objectData).getChangeOrderRule();
        MtChangeOrderRule changeRule = findChangeOrderRuleByRuleName(user, changeOrderRule);

        // 将字段映射回去
        UpdateMasterAndDetailData.Arg updateMasterAndDetailDataArg = mappingObjectData(user, changeRule, describes,
                objectData, arg.getDetailDataMap(), true);

        Map<String, List<IObjectData>> detailsToUpdate = updateMasterAndDetailDataArg.getDetailsToUpdate();
        IObjectData masterObjectData = ObjectDataExt.of(updateMasterAndDetailDataArg.getMasterObjectData()).copy();
        IObjectData dbOriginalMasterData = serviceFacade.findObjectData(user, masterObjectData.getId(), masterObjectData.getDescribeApiName());
        Map<String, List<IObjectData>> dbOriginalDetailDataMap = Maps.newHashMap();
        detailsToUpdate.forEach((describeApiName, dataList) -> {
            List<String> ids = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
            List<IObjectData> dbDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), ids, describeApiName);
            dbOriginalDetailDataMap.put(describeApiName, dbDataList);
        });
        Map<String, List<IObjectData>> detailsToAdd = ObjectDataExt.copyMap(updateMasterAndDetailDataArg.getDetailsToAdd());
        serviceFacade.updateMasterAndDetailData(user, updateMasterAndDetailDataArg);

        // 更新变更单对象
        updateChangeOrderData(user, objectData, detailsToAdd, arg.getDetailDataMap(), describes);

        return EffectiveAndUpdateData.Result.builder()
                .originalData(masterObjectData)
                .detailsToAdd(detailsDataMapToList(updateMasterAndDetailDataArg.getDetailsToAdd()))
                .detailsToUpdate(detailsDataMapToList(detailsToUpdate))
                .detailsToDelete(detailsDataMapToList(updateMasterAndDetailDataArg.getDetailsToDelete()))
                .objectDescribeMap(describes.getOriginalDescribes().getObjectDescribes())
                .dbOriginalMasterData(dbOriginalMasterData)
                .dbOriginalDetailDataMap(dbOriginalDetailDataMap)
                .callBackData(getCallBackData(user, describe, objectData))
                .build();
    }

    private Map<String, Object> getCallBackData(User user, IObjectDescribe describe, IObjectData objectData) {
        List<IObjectData> changeOrderSnapshot = changeOrderLogicService.findChangeOrderSnapshot(user, describe, Lists.newArrayList(objectData.getId()));
        if (CollectionUtils.empty(changeOrderSnapshot)) {
            return Maps.newHashMap();
        }
        IObjectData data = changeOrderSnapshot.get(0);
        return (Map<String, Object>) data.get("_CallBackData");
    }

    private void updateChangeOrderData(User user, IObjectData objectData, Map<String, List<IObjectData>> detailsToAdd, Map<String, List<IObjectData>> detailDataMap, OriginalAndChangeDescribes describes) {
        // 主对象更新生效状态
        IObjectData updateData = ObjectDataExt.of(objectData).copy();
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put(ObjectDataExt.EFFECTIVE_STATUS, ChangeOrderEffectiveStatus.EFFECTIVE.getCode());
        if (user.isOutUser()) {
            fieldMap.put(ObjectDataExt.EFFECTIVE_BY, Lists.newArrayList(user.getOutUserId()));
        } else {
            fieldMap.put(ObjectDataExt.EFFECTIVE_BY, Lists.newArrayList(user.getUserId()));
        }
        fieldMap.put(ObjectDataExt.EFFECTIVE_TIME, System.currentTimeMillis());

        Map<String, List<IObjectData>> detailsToUpdate = getDetailsDataWithAddDetails(describes, detailsToAdd, detailDataMap);
        UpdateMasterAndDetailData.Arg updateMasterAndDetailDataArg = UpdateMasterAndDetailData.Arg.builder()
                .masterObjectData(updateData)
                .toUpdateMap(fieldMap)
                .incrementUpdate(true)
                .detailsToUpdate(detailsToUpdate)
                .incrementUpdateDetail(true)
                .build();
        serviceFacade.updateMasterAndDetailData(user, updateMasterAndDetailDataArg);
    }

    private Map<String, List<IObjectData>> getDetailsDataWithAddDetails(OriginalAndChangeDescribes describes, Map<String, List<IObjectData>> detailsToAdd, Map<String, List<IObjectData>> detailDataMap) {
        if (CollectionUtils.empty(detailsToAdd)) {
            return Maps.newHashMap();
        }
        Table<String, String, IObjectData> table = HashBasedTable.create();
        detailsToAdd.forEach((describeApiName, dataList) -> {
            for (IObjectData objectData : dataList) {
                table.put(describeApiName, objectData.get(ObjectDataExt.CHANGED_DATA_ID, String.class), objectData);
            }
        });

        Map<String, List<IObjectData>> result = Maps.newHashMap();
        detailDataMap.forEach((describeApiName, dataList) -> {
            IObjectDescribe changeOrderDescribe = describes.getChangeOrderDescribe(describeApiName);
            String originalDescribeApiName = changeOrderDescribe.getOriginalDescribeApiName();
            for (IObjectData objectData : dataList) {
                Optional.ofNullable(table.get(originalDescribeApiName, objectData.getId())).ifPresent(data -> {
                    ObjectDataExt updateData = ObjectDataExt.of(new ObjectData());
                    updateData.setId(objectData.getId());
                    updateData.setOriginalDetailDataId(data.getId());
                    updateData.setDescribeApiName(describeApiName);
                    updateData.setTenantId(changeOrderDescribe.getTenantId());
                    result.computeIfAbsent(describeApiName, x -> Lists.newArrayList()).add(updateData.getObjectData());
                });
            }
        });

        return result;
    }

    @Override
    public void validateByChangeStatus(User user, IObjectDescribe describe, IObjectData objectData) {
        // 调用重载方法，使用默认参数：不传changeDataId，需要审批流校验
        validateByChangeStatus(user, describe, objectData, null, false);
    }

    @Override
    public void validateByChangeStatus(User user, IObjectDescribe describe, IObjectData objectData, String changeDataId, boolean needApprovalFlowCheck) {
        ChangeOrderChangedStatus changedStatus = ObjectDataExt.of(objectData).getChangeOrderChangedStatus();
        if (ChangeOrderChangedStatus.NORMAL == changedStatus) {
            // 正常状态，直接通过
            return;
        }

        if (ChangeOrderChangedStatus.IN_CHANGE == changedStatus && needApprovalFlowCheck) {
            // 变更中状态，需要额外校验变更单数据
            validateChangeOrderData(user, describe, changeDataId);
            return;
        }

        // 其他状态直接抛出异常
        throw new ValidateException(I18NExt.text(I18NKey.CHANGED_STATUS_IS_IN_CHANGE));
    }

    /**
     * 校验变更单数据：变更单需要有进行中的审批流，且生命状态和变更状态都正确
     */
    private void validateChangeOrderData(User user, IObjectDescribe describe, String changeDataId) {
        // 获取变更单ID
        if (Strings.isNullOrEmpty(changeDataId)) {
            throw new ValidateException(I18NExt.text(I18NKey.CHANGE_ORDER_MASTER_DATA_NOT_EXIST_OR_DELETED));
        }

        // 获取变更单对象描述
        String originalDescribeApiName = describe.getApiName();
        IObjectDescribe changeDescribe = changeOrderLogicService.findChangeOrderDescribeByOriginalApiName(user, originalDescribeApiName)
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.CHANGE_ORDER_OBJECT_NOT_EXIST_OR_DELETED)));

        // 获取变更单数据
        IObjectData changeOrderData = serviceFacade.findObjectData(user, changeDataId, changeDescribe);
        if (Objects.isNull(changeOrderData)) {
            throw new ValidateException(I18NExt.text(I18NKey.CHANGE_ORDER_MASTER_DATA_NOT_EXIST_OR_DELETED));
        }

        // 校验变更单的生命状态（应该是审核中或正常状态）
        ObjectLifeStatus changeOrderLifeStatus = ObjectDataExt.of(changeOrderData).getLifeStatus();
        if (changeOrderLifeStatus != ObjectLifeStatus.UNDER_REVIEW && changeOrderLifeStatus != ObjectLifeStatus.NORMAL) {
            throw new ValidateException(I18NExt.text(I18NKey.CHANGE_ORDER_LIFE_STATUS_INVALID));
        }

        // 校验变更单的变更状态（应该是未生效状态）
        ChangeOrderEffectiveStatus effectiveStatus = ObjectDataExt.of(changeOrderData).getChangeOrderEffectiveStatus();
        if (effectiveStatus != ChangeOrderEffectiveStatus.INEFFECTIVE) {
            throw new ValidateException(I18NExt.text(I18NKey.CHANGE_ORDER_EFFECTIVE_STATUS_INVALID));
        }
    }

    @Override
    public void updateChangeOrderStatusWithFlowReject(User user, IObjectDescribe objectDescribe, IObjectData objectData) {
        Map<String, Object> effectiveStatusMap = Maps.newHashMap();
        effectiveStatusMap.put(ObjectDataExt.EFFECTIVE_STATUS, ChangeOrderEffectiveStatus.REJECTED.getCode());
        serviceFacade.updateWithMap(user, objectData, effectiveStatusMap);

        String originalDescribeApiName = objectDescribe.getOriginalDescribeApiName();
        String originalDataId = ObjectDataExt.of(objectData).getOriginalDataId();
        IObjectDescribe originalDescribe = serviceFacade.findObject(user.getTenantId(), originalDescribeApiName);
        IObjectData originalData = serviceFacade.findObjectData(user, originalDataId, originalDescribe);

        Map<String, Object> changeStatusMap = Maps.newHashMap();
        changeStatusMap.put(ObjectDataExt.CHANGED_STATUS, ChangeOrderChangedStatus.NORMAL.getCode());
        serviceFacade.updateWithMap(user, originalData, changeStatusMap);
    }

    @Override
    public void updateChangeOrderStatusWithFlowPass(User user, IObjectDescribe describe, IObjectData objectData) {
        String ruleName = objectData.get(ObjectDataExt.CHANGE_ORDER_RULE, String.class);
        MtChangeOrderRule changeOrderRule = changeOrderRuleLogicService.findByRuleName(user, ruleName).orElse(null);
        if (Objects.isNull(changeOrderRule)) {
            log.warn("updateChangeOrderStatusWithFlowPass rule not exit, ei:{}, apiName:{}, ruleName:{}",
                    user.getTenantId(), describe.getApiName(), ruleName);
            return;
        }
        if (MtChangeOrderRule.EffectiveType.AUTO_EFFECT_AFTER_APPROVAL != changeOrderRule.toEffectiveType()) {
            return;
        }
        RequestContext requestContext = RequestContextManager.getContext();
        ActionContext context = new ActionContext(requestContext, describe.getApiName(), StandardAction.Effective.toString());
        StandardEffectiveAction.Arg arg = StandardEffectiveAction.Arg.createByFlowCompleted(objectData.getId());
        serviceFacade.triggerRemoteAction(context, arg, StandardEffectiveAction.Result.class);
    }

    @Override
    public DiffDetailData.Result diffDetailData(User user, DiffDetailData.Arg arg) {
        List<IObjectData> detailsToAdd = arg.getDetailsToAdd();
        //给新增的从对象补充id，数据快照需要使用
        ObjectDataExt.fillDataId(detailsToAdd);
        Map<String, List<IObjectData>> detailToAddMap = ObjectDataExt.groupByDescribeApiName(detailsToAdd);
        Map<String, List<IObjectData>> detailToDeleteMap = ObjectDataExt.groupByDescribeApiName(arg.getDetailsToDelete());
        Map<String, List<IObjectData>> detailToUpdateMap = ObjectDataExt.groupByDescribeApiName(arg.getDetailsToUpdate());
        Map<String, Map<String, Object>> detailChangeMap = Maps.newHashMap();

        detailToAddMap.forEach((k, v) -> {
            detailChangeMap.putIfAbsent(k, Maps.newHashMap());
            detailChangeMap.get(k).put(ObjectAction.CREATE.getActionCode(), ObjectDataDocument.ofList(ObjectDataExt.copyList(v)));
        });

        detailToDeleteMap.forEach((k, v) -> {
            detailChangeMap.putIfAbsent(k, Maps.newHashMap());
            detailChangeMap.get(k).put(ObjectAction.DELETE.getActionCode(), v.stream().map(x -> x.getId()).collect(Collectors.toList()));
        });

        AtomicBoolean detailChangeForApproval = new AtomicBoolean(CollectionUtils.notEmpty(detailChangeMap));
        Map<String, IObjectDescribe> objectDescribes = arg.getObjectDescribes();
        Map<String, List<IObjectData>> dbDetailDataMap = arg.getDbDetailDataMap();
        detailToUpdateMap.forEach((k, v) -> {
            IObjectDescribe describe = objectDescribes.get(k);
            Map<String, IObjectData> dbDataMap = dbDetailDataMap.get(k).stream().filter(x -> ObjectDataExt.of(x).hasId())
                    .collect(Collectors.toMap(x -> x.getId(), x -> x));
            Map<String, ObjectDataDocument> updateMap = Maps.newHashMap();
            v.forEach(data -> {
                IObjectData dbData = dbDataMap.get(data.getId());
                Map<String, Object> updateFields = ObjectDataExt.of(dbData).diff(data, describe);
                if (CollectionUtils.notEmpty(updateFields)) {
                    updateMap.put(data.getId(), ObjectDataDocument.of(updateFields));
                }
            });
            if (CollectionUtils.notEmpty(updateMap)) {
                detailChangeMap.putIfAbsent(k, Maps.newHashMap());
                detailChangeMap.get(k).put(ObjectAction.UPDATE.getActionCode(), updateMap);

                if (!detailChangeForApproval.get()) {
                    detailChangeForApproval.compareAndSet(false, updateMap.values().stream().anyMatch(x ->
                            CollectionUtils.notEmpty(ObjectDataExt.of(Maps.newHashMap(x))
                                    .removeInvalidFieldForApproval(describe).toMap())));
                }
            }
        });

        return DiffDetailData.Result.of(detailChangeMap, detailChangeForApproval.get());
    }

    @Override
    public MergeChangeOrderData.Result mergeChangeOrderData(User user, MergeChangeOrderData.Arg arg) {
        IObjectDescribe originalDescribe = arg.getOriginalDescribe();
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), originalDescribe.getApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.UNSUPPORTED_OPERATION));
        }
        IObjectData changeOrderData = arg.getChangeOrderData();
        IObjectData originalData = arg.getOriginalData();

        OriginalAndChangeDescribes describes = changeOrderLogicService.findDescribesByOriginalApiName(user, originalDescribe.getApiName());
        String changeOrderRuleName = ObjectDataExt.of(changeOrderData).getChangeOrderRule();
        MtChangeOrderRule changeOrderRule = findChangeOrderRuleByRuleName(user, changeOrderRuleName);

        Map<String, List<IObjectData>> changeOrderDetailsDataMap = findDbDetailData(user, Lists.newArrayList(describes.getChangeOrderDetailDescribes().values()), changeOrderData);
        ObjectDataExt.fillTemporaryId(changeOrderDetailsDataMap);
        // 创建 ObjectDataMapping 实例，设置 onlyChangeField 为 true
        ObjectDataMapping mapping = ObjectDataMapping.builder()
                .user(user)
                .changeRule(changeOrderRule)
                .describes(describes)
                .changeOrderLogicService(changeOrderLogicService)
                .objectMappingService(objectMappingService)
                .onlyChangeField(true)
                .needSnapshot(true)
                .build();
        // 调用 mapping2OriginalData 将变更单数据转换为原单数据
        ObjectDataMapping.Result mappingResult = mapping.mapping2OriginalData(changeOrderData, changeOrderDetailsDataMap);

        // 合并数据
        Map<String, IObjectDescribe> originalDetailDescribes = describes.getOriginalDetailDescribes();
        Map<String, List<IObjectData>> originalDetailsDataMap = findDbDetailData(user, Lists.newArrayList(originalDetailDescribes.values()), originalData);

        ChangeOrderDataMerge.MergeResult mergeResult;
        List<IObjectData> noIdDataTuple = null;
        try {
            noIdDataTuple = ObjectDataExt.fillTemporaryId(originalDetailsDataMap);
            ChangeOrderDataMerge changeOrderDataMerge = ChangeOrderDataMerge.builder()
                    .originalDescribe(originalDescribe)
                    .originalDetailDescribeMap(originalDetailDescribes)
                    .originalMasterData(originalData)
                    .originalDetailsDataMap(originalDetailsDataMap)
                    .mappingResult(mappingResult)
                    .build();
            mergeResult = changeOrderDataMerge.merge();

            // 调用计算接口进行数据计算
            EditCalculateParam calculateParam = EditCalculateParam.builder()
                    .masterData(mergeResult.getMasterData())
                    .oldMasterData(originalData)
                    .detailDataMap(mergeResult.getDetailDataMap())
                    .oldDetailDataMap(originalDetailsDataMap)
                    .masterModifyData(ObjectDataExt.of(mergeResult.getMasterWithOnlyChangedFields()).toMap())
                    .detailAddDataMap(mergeResult.getDetailAddDataMap())
                    .detailDeleteDataMap(mergeResult.getDetailDeleteDataMap())
                    .detailModifyDataMap(mergeResult.getDetailModifyDataMap())
                    .masterDescribe(originalDescribe)
                    .detailDescribeMap(originalDetailDescribes)
                    .excludeDefaultValue(true)
                    .includeQuoteField(true)
                    .excludeLookupRelateField(false)
                    .build()
                    .initWithUIEvent();
            serviceFacade.calculateForEditData(user, calculateParam);
        } finally {
            // 删除临时id
            ObjectDataExt.removeTemporaryId(noIdDataTuple);
        }

        return MergeChangeOrderData.Result.builder()
                .masterObjectData(mergeResult.getMasterData())
                .detailsData(mergeResult.getDetailDataMap())
                .build();
    }

    private Map<String, List<IObjectData>> findDbDetailData(User user, List<IObjectDescribe> detailDescribes, IObjectData objectData) {
        return serviceFacade.findDetailObjectDataList(detailDescribes, objectData, user);
    }

    @NotNull
    private List<IObjectData> detailsDataMapToList(Map<String, List<IObjectData>> dataMap) {
        return dataMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
    }

    private MtChangeOrderRule findOneChangeRuleAndValidate(User user, String originalDescribeApiName, String changeRuleName, MtChangeOrderRule.CalibrationType type) {
        MtChangeOrderRule changeRule = findChangeOrderRuleByRuleName(user, changeRuleName);
        List<MtChangeOrderRule.CalibrationType> calibrationTypes = changeRule.toCalibrationType();
        if (Objects.isNull(type) || !calibrationTypes.contains(type)) {
            log.warn("not calibration, ei:{}, apiName:{}, type:{}, calibrationTypes:{}", user.getTenantId(), originalDescribeApiName, type, calibrationTypes);
            return null;
        }
        return changeRule;
    }

    private MtChangeOrderRule findChangeRule(User user, String originalDescribeApiName) {
        List<MtChangeOrderRule> rules = changeOrderRuleLogicService.findByDescribeApiName(user, originalDescribeApiName);   // 前台变更后提交
        if (CollectionUtils.empty(rules)) {
            throw new ValidateException(I18NExt.text(I18NKey.NO_CHANGE_RULES_AVAILABLE));
        }
        if (rules.size() > 1) {
            throw new ValidateException(I18NExt.text(I18NKey.UNSUPPORTED_MULTI_CHANGE_RULE));
        }
        return rules.get(0);
    }

    private MtChangeOrderRule findChangeOrderRuleByRuleName(User user, String changeRuleName) {
        Optional<MtChangeOrderRule> changeOrderRule = changeOrderRuleLogicService.findByRuleName(user, changeRuleName);  // 审批发起 && 审批生效时翻译
        return changeOrderRule.orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.CHANGE_RULE_NOT_EXIST_OR_DELETED)));
    }

    private BaseObjectSaveAction.Arg buildAddActionArg(User user, CreateChangeOrder.Arg arg, MtChangeOrderRule changeOrderRule, OriginalAndChangeDescribes describes) {
        ObjectDataMapping.Mapping2ChangeOrderDataResult result = ObjectDataMapping.builder()
                .user(user)
                .changeOrderLogicService(changeOrderLogicService)
                .objectMappingService(objectMappingService)
                .changeRule(changeOrderRule)
                .describes(describes)
                .onlyChangeField(false)
                .isEdit(false)
                .build()
                .mapping2ChangeOrderData(arg);

        BaseObjectSaveAction.Arg actionArg = new BaseObjectSaveAction.Arg();
        actionArg.setObjectData(ObjectDataDocument.of(result.getMasterObjectData()));
        actionArg.setOriginalData(ObjectDataDocument.of(result.getOriginalObjectData()));
        actionArg.setDetails(ObjectDataDocument.ofMap(result.getDetails()));
        actionArg.setOriginalDetails(ObjectDataDocument.ofMap(result.getOriginalDataDetails()));
        actionArg.setFromChangeOrder(true);
        return actionArg;
    }

    private BaseObjectSaveAction.Arg buildEditActionArg(User user, CreateChangeOrder.Arg arg, MtChangeOrderRule changeOrderRule, OriginalAndChangeDescribes describes) {
        ObjectDataMapping.Mapping2ChangeOrderDataResult result = ObjectDataMapping.builder()
                .user(user)
                .changeOrderLogicService(changeOrderLogicService)
                .objectMappingService(objectMappingService)
                .changeRule(changeOrderRule)
                .describes(describes)
                .onlyChangeField(false)
                .isEdit(true)
                .build()
                .mapping2ChangeOrderData(arg);

        BaseObjectSaveAction.Arg actionArg = new BaseObjectSaveAction.Arg();
        actionArg.setObjectData(ObjectDataDocument.of(result.getMasterObjectData()));
        actionArg.setOriginalData(ObjectDataDocument.of(result.getOriginalObjectData()));
        actionArg.setDetails(ObjectDataDocument.ofMap(result.getDetails()));
        actionArg.setOriginalDetails(ObjectDataDocument.ofMap(result.getOriginalDataDetails()));
        actionArg.setFromChangeOrder(true);

        actionArg.setBizInfo(arg.getBizInfo());
        actionArg.setUseSnapshot(arg.getUseSnapshot());
        actionArg.setSkipApprovalFlow(arg.getSkipApprovalFlow());
        actionArg.setSkipDataStatusValidate(arg.getSkipDataStatusValidate());
        actionArg.setAcceptNonBlockingResult(arg.getAcceptNonBlockingResult());
        return actionArg;
    }

}
