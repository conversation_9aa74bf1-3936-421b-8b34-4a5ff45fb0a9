package com.facishare.paas.appframework.core.predef.action

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.ObjectLockStatus
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.model.domain.DomainPluginManager
import com.facishare.paas.appframework.core.model.handler.HandlerManager
import com.facishare.paas.appframework.core.model.plugin.PluginManager
import com.facishare.paas.appframework.core.predef.service.PartnerRemindOutUserService
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.ObjectLifeStatus
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraph
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraphBuilder
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceImpl
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.Count
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe
import com.facishare.paas.metadata.impl.describe.FormulaFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Sets
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * create by zhaoju on 2019/12/02
 */
class StandardEditActionGroovyTest extends Specification {

    def tenantId = "123"
    def userId = "321"
    def apiName = "whatever"
    def actionCode = "whatever"

    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    User user
    ActionContext actionContext
    StandardEditAction action

    BaseObjectSaveAction.Arg arg

    @Shared
    String describeJson = '''{"fields":{"returned_goods_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"退货单金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"ReturnedGoodsInvoiceObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_11","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912729,"count_type":"sum","count_field_api_name":"returned_goods_inv_amount","label":"退货单金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"returned_goods_amount","count_field_type":"currency","_id":"5d3abb7e319d19982fcc968b","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"}}}'''

    @Shared
    String dataJson = '''{"lock_rule":null,"discount":"100.0000","account_id__r":"jejej","order_time":*************}'''

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        serviceFacade = Mock(ServiceFacade)
        infraServiceFacade = Mock(InfraServiceFacade)
        user = new User(tenantId, userId)
        actionContext = new ActionContext(RequestContext.builder().user(user).tenantId(tenantId).build(), apiName, actionCode)

        // 创建并配置SpringBeanHolder mock
        def springBeanHolder = Mock(SpringBeanHolder)
        springBeanHolder.getHandlerManager() >> Mock(HandlerManager)
        springBeanHolder.getHandlerLogicService() >> Mock(HandlerLogicService)
        springBeanHolder.getPluginManager() >> Mock(PluginManager)
        springBeanHolder.getDomainPluginManager() >> Mock(DomainPluginManager)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder

        action = new StandardEditAction()
        action.setActionContext(actionContext)
        action.setServiceFacade(serviceFacade)
        action.setInfraServiceFacade(infraServiceFacade)

        serviceFacade.getBean(EnterpriseRelationServiceImpl.class) >> new EnterpriseRelationServiceImpl()
        serviceFacade.getBean(PartnerRemindOutUserService.class) >> new PartnerRemindOutUserService()

        arg = new BaseObjectSaveAction.Arg()
        arg.setObjectData(new ObjectDataDocument(JSON.parseObject(dataJson)))
        action.setArg(arg)
    }

    def "before"() {
        expect:
        1 == 1
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processRemoveFields方法的基本功能，包括系统字段移除
     */
    def "processRemoveFieldsTest_BasicFields"() {
        given:
        def objectDescribe = Mock(IObjectDescribe) {
            getApiName() >> "test_object"
            getFieldDescribes() >> [

            ]
        }
        action.objectDescribes = [
                "test_object": objectDescribe
        ]
        action.objectDescribe = objectDescribe
        action.arg = new BaseObjectSaveAction.Arg()

        when:
        action.processRemoveFields()

        then:
        def removeFields = action.removeFieldMap.get("test_object")
        removeFields.contains(IObjectData.VERSION)
        removeFields.contains(ObjectLockStatus.LOCK_STATUS_API_NAME)
        removeFields.contains(ObjectLifeStatus.LIFE_STATUS_API_NAME)
        removeFields.contains(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME)
        removeFields.contains(IObjectData.OWNER)
        removeFields.contains(IObjectData.DATA_OWN_DEPARTMENT)
        removeFields.contains(ObjectDataExt.RELEVANT_TEAM)
        removeFields.contains(IObjectData.OUT_OWNER)
        removeFields.contains(IObjectData.OUT_TENANT_ID)
        removeFields.contains(IObjectData.CREATE_ENTERPRISE)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试OpenAPI请求时GDPR字段的处理
     */
    def "processRemoveFieldsTest_GdprFields"() {
        given:
        infraServiceFacade.needFilterGdprFields(_, _, _) >> ["gdpr_field1", "gdpr_field2"]
        actionContext = new ActionContext(RequestContext.builder().user(user).tenantId(tenantId).peerName(RequestContext.OPENAPI_PEER_NAME).build(),
                apiName, actionCode)
        action.setActionContext(actionContext)
        RequestContextManager.setContext(actionContext.requestContext)
        def objectDescribe = Mock(IObjectDescribe) {
            getApiName() >> "test_object"
            getFieldDescribes() >> [

            ]
        }
        action.objectDescribes = [
                "test_object": objectDescribe
        ]
        action.objectDescribe = objectDescribe
        action.arg = new BaseObjectSaveAction.Arg()

        when:
        action.processRemoveFields()

        then:
        def removeFields = action.removeFieldMap.get("test_object")
        removeFields.contains("gdpr_field1")
        removeFields.contains("gdpr_field2")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试统计字段的处理
     */
    def "processRemoveFieldsTest_CountFields"() {
        given:
        def countField = Mock(Count) {
            getApiName() >> "count_field"
            getSubObjectDescribeApiName() >> "detail_object"
            getType() >> IFieldType.COUNT
            isActive() >> true
        }
        def objectDescribe = Mock(IObjectDescribe) {
            getApiName() >> "test_object"
            getFieldDescribes() >> [countField]
        }
        action.objectDescribe = objectDescribe
        action.objectDescribes = [
                "test_object": objectDescribe
        ]
        action.arg = new BaseObjectSaveAction.Arg()

        when:
        action.processRemoveFields()

        then:
        def removeFields = action.removeFieldMap.get("test_object")
        removeFields.contains("count_field")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试关联字段的处理
     */
    def "processRemoveFieldsTest_RelatedFields"() {
        given:
        def countField = new CountFieldDescribe([
                "api_name"                   : "count_field",
                "sub_object_describe_apiname": "detail_object",
                "field_api_name"             : "lookup_field",
                "count_type"                 : "count",
                "type"                       : IFieldType.COUNT,
                "is_active"                  : true
        ])
        def relatedField = new FormulaFieldDescribe([
                "api_name"   : "related_field",
                "type"       : IFieldType.FORMULA,
                "is_active"  : true,
                "expression" : "\$count_field\$",
                "return_type": IFieldType.NUMBER
        ])
        def objectDescribe = new ObjectDescribe([
                "api_name": "test_object",
                "fields"  : ["count_field": countField.containerDocument, "relate_field": relatedField.containerDocument]
        ])
        action.objectDescribe = objectDescribe
        action.objectDescribes = [
                "test_object": objectDescribe
        ]
        FieldRelationGraph graph = FieldRelationGraphBuilder.builder()
                .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([objectDescribe])])
                .build()
                .getGraph()
                .transpose()
        action.graph = graph
        action.arg = new BaseObjectSaveAction.Arg()

        when:
        action.processRemoveFields()

        then:
        def removeFields = action.removeFieldMap.get("test_object")
        removeFields.contains("related_field")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试自定义移除字段的处理
     */
    def "processRemoveFieldsTest_CustomFields"() {
        given:
        def action = new StandardEditAction() {
            @Override
            protected Set<String> getFieldsToRemoveBeforeUpdate(String describeApiName) {
                return Sets.newHashSet("custom_field1", "custom_field2")
            }
        }
        action.setActionContext(actionContext)
        action.setInfraServiceFacade(infraServiceFacade)
        def objectDescribe = Mock(IObjectDescribe) {
            getApiName() >> "test_object"
            getFieldDescribes() >> [

            ]
        }
        action.objectDescribes = [
                "test_object": objectDescribe
        ]
        action.objectDescribe = objectDescribe
        action.arg = new BaseObjectSaveAction.Arg()

        when:
        action.processRemoveFields()

        then:
        def removeFields = action.removeFieldMap.get("test_object")
        removeFields.contains("custom_field1")
        removeFields.contains("custom_field2")
    }
}
