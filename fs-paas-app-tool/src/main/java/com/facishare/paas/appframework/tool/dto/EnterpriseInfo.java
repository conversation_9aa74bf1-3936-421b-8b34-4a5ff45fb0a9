package com.facishare.paas.appframework.tool.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class EnterpriseInfo {
    private String ea;
    private String ei;
    private String enterpriseName;
    private String level;
    private String crmVersion;
    private String openTime;
    private String status;
    private String csmOwner; //客户成功负责人
    private String customerOwner;
    private String cloud;
    private String index;
    private String dataSource;
    private boolean schema;//是否是schema隔离企业
}
