package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.dto.LocationFieldMapping;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/05/07
 */
@Slf4j
public abstract class ObjectImportConfig {
    //控制导入支持ID，即放开自定义对象主属性可重复时更新导入
    private static final String OBJECT_IMPORT_CONFIG = "fs-crm-object-import-config";
    private static final String IMPORT_EXPORT_CONFIG_NAME = "import_export_id";
    private static final String SUPPORT_IMPORT_ID = "support_import_id";
    public static final String ALL = "ALL";

    // 支持按id导入导出的企业
    private static List<String> TENANT_ID_LIST = Lists.newArrayList();
    // 支持按id导入的对象
    public static List<String> SUPPORT_IMPORT_ID_API_NAME = Lists.newArrayList();
    // 导出时 优先从 redis 中获取 总条数
    public static List<String> EXPORT_TOTAL_COUNT_FIRST_READ_IN_REDIS = Lists.newArrayList();

    public static List<String> SUPPORT_IMPORT_OWNER_PRESET_OBJECT_API_NAME = Lists.newArrayList();
    public static List<String> SUPPORT_IMPORT_OWNER_BLACK_PRESET_OBJECT = Lists.newArrayList();
    private static Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings();

    public static List<LocationFieldMapping> IMPORT_LOCATION_FIELD_MAPPINGS = Lists.newArrayList();
    private static List<String> EXPORT_EXCEL_FILE_SPLIT_GRAY = Lists.newArrayList();
    private static List<String> LOCATION_IMPORT_PROVIDER = Lists.newArrayList();

    static {
        ConfigFactory.getConfig(OBJECT_IMPORT_CONFIG, config -> {
            log.warn("reload config fs-crm-object-import-config,content:{}", config.getString());

            TENANT_ID_LIST = getListFromConfig(config, IMPORT_EXPORT_CONFIG_NAME);
            SUPPORT_IMPORT_ID_API_NAME = getListFromConfig(config, SUPPORT_IMPORT_ID);
            EXPORT_TOTAL_COUNT_FIRST_READ_IN_REDIS = getListFromConfig(config, "export_total_count_first_read_in_redis");
            SUPPORT_IMPORT_OWNER_PRESET_OBJECT_API_NAME = getListFromConfig(config, "support_import_owner_preset_object_api_name");
            SUPPORT_IMPORT_OWNER_BLACK_PRESET_OBJECT = getListFromConfig(config, "support_import_owner_black_preset_object");
            IMPORT_LOCATION_FIELD_MAPPINGS = getLocationFieldMappings(config, "import_location_field_mappings");
            EXPORT_EXCEL_FILE_SPLIT_GRAY = getListFromConfig(config, "export_excel_file_split_gray");
            LOCATION_IMPORT_PROVIDER = getListFromConfig(config, "location_import_provider");
        });
    }

    private static List<String> getListFromConfig(IConfig config, String key) {
        return Lists.newArrayList(CONFIG_SPLITTER.split(config.get(key, "")));
    }

    private static List<LocationFieldMapping> getLocationFieldMappings(IConfig config, String key) {
        String fieldMapping = config.get(key, "");
        return JacksonUtils.fromJson(fieldMapping, new TypeReference<List<LocationFieldMapping>>() {
        });
    }

    public static boolean isGrayTenant(String tenant) {
        if (TENANT_ID_LIST.contains("All")) {
            return true;
        }
        return TENANT_ID_LIST.contains(tenant);
    }

    public static boolean isGrayExportTotalCount(String tenantId) {
        return EXPORT_TOTAL_COUNT_FIRST_READ_IN_REDIS.contains(ALL) || EXPORT_TOTAL_COUNT_FIRST_READ_IN_REDIS.contains(tenantId);
    }

    public static boolean isSupportUpdateImportOwner(User user, IObjectDescribe objectDescribe) {
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return false;
        }
        return isSupportUpdateImportOwner(user, objectDescribe.getApiName());
    }

    public static boolean isSupportUpdateImportOwner(User user, String apiName) {
        if (Objects.isNull(user) || user.isOutUser()) {
            return false;
        }
        return ObjectDescribeExt.isCustomObject(apiName) || !SUPPORT_IMPORT_OWNER_BLACK_PRESET_OBJECT.contains(apiName);
    }

    public static boolean isSupportCustomOrPresetObject(User user, String apiName, String grayKey, List<String> presetObjectApiNames) {
        if (UdobjGrayConfig.isAllow(grayKey, user.getTenantId())) {
            return ObjectDescribeExt.isCustomObject(apiName) || presetObjectApiNames.contains(apiName);
        }
        return false;
    }

    public static boolean isGrayLocationField(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_LOCATION_FIELD_GRAY, tenantId);
    }

    public static boolean isGrayExportExcelFileSplit(String tenantId) {
        if (EXPORT_EXCEL_FILE_SPLIT_GRAY.contains(ALL) || EXPORT_EXCEL_FILE_SPLIT_GRAY.contains(tenantId)) {
            return true;
        }
        return false;
    }

    public static boolean isGrayLocationImportProvider(String objectApiName) {
        return LOCATION_IMPORT_PROVIDER.contains(objectApiName);
    }
}
