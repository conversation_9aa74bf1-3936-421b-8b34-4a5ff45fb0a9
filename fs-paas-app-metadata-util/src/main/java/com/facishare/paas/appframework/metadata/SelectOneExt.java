package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.SelectOne;
import com.google.common.base.Strings;
import lombok.Getter;
import lombok.experimental.Delegate;

/**
 * Created by zhouwr on 2018/6/25
 */
public class SelectOneExt {

    @Getter
    @Delegate
    private SelectOne selectOne;

    private SelectOneExt(SelectOne selectOne) {
        this.selectOne = selectOne;
    }

    public static SelectOneExt of(SelectOne selectOne) {
        return new SelectOneExt(selectOne);
    }

    public String getLabelByValue(String value) {
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        return selectOne.getOption(value).map(x -> x.getLabel()).orElse("");
    }

    public String getOptionType() {
        return get(SelectOne.OPTION_TYPE, String.class);
    }

    public String getOptionApiName() {
        return get(SelectOne.OPTION_API_NAME, String.class);
    }

    public void setOptionApiName(String optionApiName) {
        set(SelectOne.OPTION_API_NAME, optionApiName);
        set(SelectOne.OPTION_TYPE, SelectOne.GENERAL_OPTION_TYPE);
    }

}
