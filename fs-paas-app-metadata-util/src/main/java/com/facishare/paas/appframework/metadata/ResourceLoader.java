package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.Tuple;
import com.google.common.base.Splitter;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhouwr on 2017/11/6
 */
public abstract class ResourceLoader {

    public static Set<Tuple<String, String>> loadMapping(String filePath) {
        try {
            Stream<String> lines = lines(getResources(filePath));
            if (lines == null) {
                return null;
            }
            return lines.map(x -> {
                List<String> temp = Splitter.on(":").trimResults().splitToList(x);
                return Tuple.of(temp.get(0), temp.get(1));
            }).collect(Collectors.toSet());
        } catch (IOException e) {
            throw new RuntimeException("Load mapping error,filePath:" + filePath, e);
        }
    }

    public static Set<Tuple<String, String>> loadMapping(String packageName, String fileName) {
        return loadMapping(packageName + "/" + fileName);
    }

    private static InputStream getResources(String filePath) {
        return ResourceLoader.class.getResourceAsStream("/" + filePath);
    }

    private static Stream<String> lines(InputStream inputStream) throws IOException {
        if (inputStream == null) {
            return null;
        }
        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        try {
            return br.lines().onClose(asUncheckedRunnable(br));
        } catch (Error | RuntimeException e) {
            try {
                br.close();
            } catch (IOException ex) {
                try {
                    e.addSuppressed(ex);
                } catch (Throwable ignore) {
                }
            }
            throw e;
        }
    }

    private static Runnable asUncheckedRunnable(Closeable c) {
        return () -> {
            try {
                c.close();
            } catch (IOException e) {
                throw new UncheckedIOException(e);
            }
        };
    }

    public static void main(String[] args) {
        System.out.println(loadMapping("object_action_mapping", ObjectAPINameMapping.AccountAtt.getApiName()));
    }
}
