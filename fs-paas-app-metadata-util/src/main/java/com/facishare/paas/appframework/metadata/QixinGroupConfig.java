package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.describe.IObjectCluster;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.Objects;

@Data
public class QixinGroupConfig implements IObjectCluster {

    @Delegate
    private IObjectCluster objectCluster;

    private QixinGroupConfig(IObjectCluster config) {
        this.objectCluster = config;
    }

    public static QixinGroupConfig of(IObjectCluster config) {
        return new QixinGroupConfig(config);
    }

    public boolean isObjectEnabled() {
        return Objects.nonNull(objectCluster) &&
                Objects.equals(objectCluster.getIsActive(), Boolean.TRUE) &&
                !Objects.equals(objectCluster.getIsDeleted(), Boolean.TRUE);
    }

}
