package com.facishare.paas.appframework.metadata.filter;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.impl.search.Operator;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by zhouwr on 2018/7/13
 */
public enum Comparator {
    EQ(Operator.EQ) {
        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return ObjectDataExt.isNotEmptyValueEqual(leftValue, rightValue, fieldType);
        }
    },

    EQL(Operator.EQL) {
        @Override
        public boolean needFilterValues() {
            return false;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            if (!FieldDescribeExt.isDateTypeField(fieldType) || StringUtils.isAllBlank(leftValue.toString(), rightValue.toString())) {
                return false;
            }
            return Objects.equals(leftValue, rightValue);
        }
    },

    GT(Operator.GT) {
        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            if (FieldDescribeExt.isDateTypeField(fieldType)) {
                return ObjectDataExt.compareDateTime(leftValue, rightValue, fieldType) > 0;
            }
            return ObjectDataExt.compareNumber(leftValue, rightValue) > 0;
        }
    },
    LT(Operator.LT) {
        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            if (FieldDescribeExt.isDateTypeField(fieldType)) {
                return ObjectDataExt.compareDateTime(leftValue, rightValue, fieldType) < 0;
            }
            return ObjectDataExt.compareNumber(leftValue, rightValue) < 0;
        }
    },
    GTE(Operator.GTE) {
        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return ObjectDataExt.compareNumber(leftValue, rightValue) >= 0;
        }
    },
    LTE(Operator.LTE) {
        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return ObjectDataExt.compareNumber(leftValue, rightValue) <= 0;
        }
    },
    N(Operator.N) {
        @Override
        protected boolean supportEmptyValue() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            if (ObjectDataExt.isValueEmpty(leftValue, fieldType)) {
                return !ObjectDataExt.isValueEmpty(rightValue, fieldType);
            }
            if (ObjectDataExt.isValueEmpty(rightValue, fieldType)) {
                return true;
            }
            return !EQ.doCompare(leftValue, rightValue, fieldType);
        }
    },
    LIKE(Operator.LIKE) {
        @Override
        protected boolean supportEmptyValue() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            leftValue = Optional.ofNullable(leftValue).orElse("");
            rightValue = Optional.ofNullable(rightValue).orElse("");
            return leftValue.toString().contains(rightValue.toString());
        }
    },
    NLIKE(Operator.NLIKE) {
        @Override
        protected boolean supportEmptyValue() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return !LIKE.doCompare(leftValue, rightValue, fieldType);
        }
    },
    IS(Operator.IS) {
        @Override
        protected boolean supportEmptyValue() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return ObjectDataExt.isValueEmpty(leftValue, fieldType);
        }
    },
    ISN(Operator.ISN) {
        @Override
        protected boolean supportEmptyValue() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return !ObjectDataExt.isValueEmpty(leftValue, fieldType);
        }
    },
    IN(Operator.IN) {
        @Override
        public boolean needFilterValues() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            List<String> values = (List<String>) rightValue;
            if (leftValue instanceof Collection) {
                return values.containsAll((Collection) leftValue);
            }
            return values.contains(leftValue.toString());
        }
    },
    NIN(Operator.NIN) {
        @Override
        public boolean needFilterValues() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return !IN.doCompare(leftValue, rightValue, fieldType);
        }
    },
    BETWEEN(Operator.BETWEEN) {
        @Override
        public boolean needFilterValues() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            List<String> values = (List<String>) rightValue;
            return GTE.doCompare(leftValue, values.get(0), fieldType) && LTE.doCompare(leftValue, values.get(1), fieldType);
        }
    },
    NBETWEEN(Operator.NBETWEEN) {
        @Override
        public boolean needFilterValues() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return !BETWEEN.doCompare(leftValue, rightValue, fieldType);
        }
    },
    STARTWITH(Operator.STARTWITH) {
        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return leftValue.toString().startsWith(rightValue.toString());
        }
    },
    ENDWITH(Operator.ENDWITH) {
        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return leftValue.toString().endsWith(rightValue.toString());
        }
    },
    CONTAINS(Operator.CONTAINS) {
        @Override
        public boolean needFilterValues() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            List values = CollectionUtils.nullToEmpty((List) leftValue);
            List filterValues = CollectionUtils.nullToEmpty((List) rightValue);
            return org.apache.commons.collections4.CollectionUtils.containsAll(values, filterValues);
        }
    },
    NCONTAINS(Operator.NCONTAINS) {
        @Override
        public boolean needFilterValues() {
            return true;
        }

        @Override
        protected boolean supportEmptyValue() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return !CONTAINS.doCompare(leftValue, rightValue, fieldType);
        }
    },
    HASANYOF(Operator.HASANYOF) {
        @Override
        public boolean needFilterValues() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            List filterValues = CollectionUtils.nullToEmpty((List) rightValue);
            if (leftValue instanceof Collection) {
                return org.apache.commons.collections4.CollectionUtils.containsAny((Collection) leftValue, filterValues);
            }
            return filterValues.contains(leftValue.toString());
        }
    },
    NHASANYOF(Operator.NHASANYOF) {
        @Override
        protected boolean supportEmptyValue() {
            return true;
        }

        @Override
        public boolean needFilterValues() {
            return true;
        }

        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            if (ObjectDataExt.isValueEmpty(leftValue) || ObjectDataExt.isValueEmpty(rightValue)) {
                return true;
            }
            return !HASANYOF.compare(leftValue, rightValue, fieldType);
        }
    },
    NEQ(Operator.NEQ) {
        @Override
        protected boolean doCompare(Object leftValue, Object rightValue, String fieldType) {
            return !EQ.compare(leftValue, rightValue, fieldType);
        }
    },
    ;

    private Operator operator;

    Comparator(Operator operator) {
        this.operator = operator;
    }

    public static Comparator of(Operator operator) {
        for (Comparator comparator : values()) {
            if (comparator.operator == operator) {
                return comparator;
            }
        }
        throw new MetaDataBusinessException(I18N.text(I18NKey.UNSUPPORT_COMPARISON_OPERATOR, operator));
    }

    public boolean needFilterValues() {
        return false;
    }

    public boolean compare(Object leftValue, Object rightValue, String fieldType) {
        if (!supportEmptyValue()) {
            if (ObjectDataExt.isValueEmpty(leftValue, fieldType)) {
                return false;
            }
            if (ObjectDataExt.isValueEmpty(rightValue, fieldType)) {
                return false;
            }
        }
        return doCompare(leftValue, rightValue, fieldType);
    }

    protected boolean supportEmptyValue() {
        return false;
    }

    protected abstract boolean doCompare(Object leftValue, Object rightValue, String fieldType);

    public String displayName(String fieldType) {
        String name = operator.name().toLowerCase();
        return I18NExt.text(OPERATOR_DISPLAY_NAME_PRE_KEY + name, name);
    }

    String OPERATOR_DISPLAY_NAME_PRE_KEY = "paas.udobj.operator.";
}
