package com.facishare.paas.appframework.metadata.button;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.common.util.ObjectLockStatus.LOCK_STATUS_API_NAME;
import static com.facishare.paas.appframework.metadata.ButtonExt.generateSystemButton;
import static com.facishare.paas.appframework.metadata.ButtonUsePageType.Create;
import static com.facishare.paas.appframework.metadata.ButtonUsePageType.Detail;
import static com.facishare.paas.appframework.metadata.ButtonUsePageType.Edit;
import static com.facishare.paas.appframework.metadata.ButtonUsePageType.ListBatch;
import static com.facishare.paas.appframework.metadata.ObjectLifeStatus.*;

/**
 * create by zhaoju on 2019/09/20
 */
public enum DefaultButtonAction implements IDefaultButtonAction {
    LOCK(ObjectAction.LOCK, "锁定") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setWheres(buildButtonWheresByFilters(Lists.newArrayList(eq(LOCK_STATUS_API_NAME, ObjectLockStatus.UNLOCK.getStatus()))));
            return button;
        }
    },
    UNLOCK(ObjectAction.UNLOCK, "解锁") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setWheres(buildButtonWheresByFilters(Lists.newArrayList(eq(LOCK_STATUS_API_NAME, ObjectLockStatus.LOCK.getStatus()))));
            return button;
        }
    },
    INVALID(ObjectAction.INVALID, "作废") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setWheres(buildButtonWheresByFilters(Lists.newArrayList(in(LIFE_STATUS_API_NAME,
                    NORMAL.getCode(), UNDER_REVIEW.getCode(), IN_CHANGE.getCode(), INEFFECTIVE.getCode()))));
            return button;

        }
    },
    CLONE(ObjectAction.CLONE, "复制") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setUsePages(Lists.newArrayList(Detail.getId()));
            return button;
        }
    },
    EDIT_TEAM_MEMBER(ObjectAction.EDIT_TEAM_MEMBER, "编辑团队成员") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setUsePages(Lists.newArrayList(Detail.getId()));
            button.setWheres(buildButtonWheresByFilters(Lists.newArrayList(eq(LOCK_STATUS_API_NAME, ObjectLockStatus.UNLOCK.getStatus()))));
            return button;
        }
    },
    ADD_TEAM_MEMBER(ObjectAction.ADD_TEAM_MEMBER, "添加团队成员") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setWheres(buildButtonWheresByFilters(Lists.newArrayList(eq(LOCK_STATUS_API_NAME, ObjectLockStatus.UNLOCK.getStatus()))));
            return button;
        }
    },
    DELETE_TEAM_MEMBER(ObjectAction.DELETE_TEAM_MEMBER, "移除团队成员") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setWheres(buildButtonWheresByFilters(Lists.newArrayList(eq(LOCK_STATUS_API_NAME, ObjectLockStatus.UNLOCK.getStatus()))));
            return button;
        }
    },
    CHANGE_OWNER(ObjectAction.CHANGE_OWNER, "更换负责人") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setWheres(buildButtonWheresByFilters(Lists.newArrayList(eq(LOCK_STATUS_API_NAME, ObjectLockStatus.UNLOCK.getStatus()))));
            return button;
        }
    },
    CREATE(ObjectAction.CREATE, "新建") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setUsePages(Lists.newArrayList(Create.getId()));
            return button;
        }
    },
    UPDATE(ObjectAction.UPDATE, "编辑") {
        @Override
        public IUdefButton getButton() {
            IUdefButton button = generateDefaultButton();
            button.setUsePages(Lists.newArrayList(Edit.getId()));
            return button;
        }
    },
    ;
    @Getter
    private ObjectAction objectAction;
    private String defaultLabel;

    DefaultButtonAction(ObjectAction objectAction, String defaultLabel) {
        this.objectAction = objectAction;
        this.defaultLabel = defaultLabel;
    }

    protected String getButtonLabel() {
        return I18NExt.getOrDefault(objectAction.getI18NKey(), defaultLabel);
    }

    protected IUdefButton generateDefaultButton() {
        IUdefButton button = generateSystemButton();
        button.setLabel(getButtonLabel());
        button.setApiName(getButtonApiName());
        button.setUsePages(Lists.newArrayList(Detail.getId(), ListBatch.getId()));
        ButtonExt.of(button).setActionCode(getObjectAction().getActionCode());
        return button;
    }

    public String getButtonApiName() {
        return objectAction.getButtonApiName();
    }

    private static IFilter eq(String fieldName, String fieldValue) {
        return FilterExt.of(Operator.EQ, fieldName, fieldValue).getFilter();
    }

    private static IFilter in(String fieldName, String... fieldValue) {
        return FilterExt.of(Operator.IN, fieldName, Lists.newArrayList(fieldValue)).getFilter();
    }

    private static List<Wheres> buildButtonWheresByFilters(List<IFilter> filters) {

        Wheres wheres = new Wheres();
        wheres.setFilters(filters);
        return Lists.newArrayList(wheres);
    }

    private static Map<String, IDefaultButtonAction> map;

    static {
        map = Stream.of(values()).collect(Collectors.toMap(DefaultButtonAction::getButtonApiName, x -> x));
    }

    public static IDefaultButtonAction getByButtonApiName(String buttonApiName) {
        return map.getOrDefault(buttonApiName, () -> null);
    }
}
