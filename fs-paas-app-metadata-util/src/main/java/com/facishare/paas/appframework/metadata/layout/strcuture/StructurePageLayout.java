package com.facishare.paas.appframework.metadata.layout.strcuture;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.google.common.collect.Lists;
import org.bson.Document;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/11/14
 */
public class StructurePageLayout extends DocumentBasedBean {
    /**
     * 1:顶导航开启
     */
    public static final String PAGE_MULTI_TYPE = "pageMultiType";
    /**
     * 默认标签页index
     */
    public static final String DEFAULT_LABEL_INDEX = "defaultLabelIndex";

    public static final String LAYOUT_LIST = "layout_list";

    public StructurePageLayout() {
    }

    public StructurePageLayout(Map map) {
        super(map);
    }

    public String getPageMultiType() {
        return get(PAGE_MULTI_TYPE, String.class);
    }

    public int getDefaultLabelIndex() {
        return get(DEFAULT_LABEL_INDEX, Integer.class);
    }

    public void setDefaultLabelIndex(int index) {
        set(DEFAULT_LABEL_INDEX, index);
    }

    public List<StructureLayout> getLayoutList() {
        List<Map> list = (List<Map>) get(LAYOUT_LIST);
        if (CollectionUtils.empty(list)) {
            return Lists.newArrayList();
        }
        return list.stream()
                .map(StructureLayout::new)
                .collect(Collectors.toList());
    }

    public void addStructureLayout(StructureLayout structureLayout) {
        List<Map> layoutList = get(LAYOUT_LIST, List.class);
        if (layoutList == null) {
            layoutList = Lists.newArrayList();
            set(LAYOUT_LIST, layoutList);
        }
        layoutList.add(Document.parse(structureLayout.toJsonString()));
    }

    public Optional<StructureLayout> getDefaultLayout() {
        int index = getDefaultLabelIndex();
        return Optional.ofNullable(getLayoutByIndex(index));
    }

    public StructureLayout getLayoutByIndex(int index) {
        List<StructureLayout> layoutList = getLayoutList();
        if (index < 0 || index >= layoutList.size()) {
            return null;
        }
        return layoutList.get(index);
    }

    public Optional<StructureLayout> getFirstListComponentLayout() {
        return getLayoutList().stream()
                .filter(it -> it.getListLayoutComponent().isPresent())
                .findFirst();
    }

    public boolean needHomePageFilters() {
        return getLayoutList().stream()
                .anyMatch(StructureLayout::needHomePageFilters);
    }
}
