package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.FieldManyMaxConfig;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.dto.Filter;
import com.facishare.paas.appframework.metadata.dto.Wheres;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation.RelateField;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.Date;
import com.facishare.paas.metadata.api.describe.Number;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.common.MetadataConstant;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.util.CopyOnWriteMap;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.NonNull;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ObjectDataExt.EXTEND_OBJ_DATA_ID;
import static java.util.stream.Collectors.toList;

/**
 * Created by liyiguang on 2018/5/9.
 */
@Slf4j
public class FieldDescribeExt {

    //过滤字段
    public static final String WHERES = "wheres";

    public static final String WHERE_TYPE = "where_type";

    public static final String RELATED_WHERE_TYPE = "related_where_type";
    public static final String FUNCTION_WHERE_TYPE = "function";
    public static final String FIELD_WHERE_TYPE = "field";

    public static final String PRODUCT_CATEGORY = "category";
    public static final String RECORD_TYPE = "record_type";

    // 普通成员
    public static final String NORMAL_TEAM_MEMBER_READONLY = "normal_member_readonly";
    public static final String NORMAL_TEAM_MEMBER_READWRITE = "normal_member_readwrite";
    public static final Set<String> CASCADE_CHILD_API_NAME_FIELD_TYPE = Sets.newHashSet(IFieldType.SELECT_ONE, IFieldType.SELECT_MANY);
    public static final Set<String> CASCADE_PARENT_API_NAME_FIELD_TYPE = Sets.newHashSet(IFieldType.SELECT_ONE, IFieldType.RECORD_TYPE);

    //默认的小数格式化方式(四舍五入)
    public static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    public static final String REFERENCE_LABEL_I18N_KEY_TEMPLATE = "%s.field.%s.reference_label";
    public static final String CURRENCY_FIELD = "mc_currency";
    public static final String EXCHANGE_RATE_FIELD = "mc_exchange_rate";
    public static final String EXCHANGE_RATE_VERSION_FIELD = "mc_exchange_rate_version";
    public static final String FUNCTIONAL_CURRENCY_FIELD = "mc_functional_currency";
    // 主属性显示字段
    public static final String DISPLAY_NAME = "display_name";
    public static final String ENABLE_CLONE = "enable_clone";
    public static final List<String> WHAT_GROUP_TYPES = Collections.unmodifiableList(Lists.newArrayList(GroupField.GROUP_TYPE_WHAT, GroupField.GROUP_TYPE_WHAT_LIST));
    public static final List<String> GROUP_TYPE = Collections.unmodifiableList(Lists.newArrayList(IFieldType.GROUP));
    public static final Set<String> FILL_EMPLOYEE_EXT_TYPES = Collections.unmodifiableSet(Sets.newHashSet(IFieldType.EMPLOYEE, IFieldType.OUT_EMPLOYEE, IFieldType.EMPLOYEE_MANY));
    public static final Set<String> FILL_DEPARTMENT_EXT_TYPES = Collections.unmodifiableSet(Sets.newHashSet(IFieldType.DEPARTMENT, IFieldType.OUT_DEPARTMENT, IFieldType.DEPARTMENT_MANY));

    // 类 关联类型 的字段 主从 查找关联单选
    public static final Set<String> REF_LIKES_FIELD_TYPE = Collections.unmodifiableSet(Sets.newHashSet(IFieldType.MASTER_DETAIL, IFieldType.OBJECT_REFERENCE));

    public static final String SUPPORT_LANGUAGES = "support_languages";

    private static final Set<String> UN_SYNC_FIELD = ImmutableSet.of("field_num", "_id", "is_extend", "index_name");

    private static final Set<String> REMOVE_PROPS_IN_COPY4EXT = ImmutableSet.of("_id", "label", "define_type", "description",
            "describe_api_name", "default_is_expression", "is_unique", "where_type", "is_required", "wheres", "index_name",
            "is_index", "is_active", "create_time", "is_encrypted", "target_api_name", "target_related_list_name", "field_num",
            "target_related_list_label", "action_on_target_delete", "is_index_field", "help_text", "status", "default_value",
            "is_need_convert", "return_type", "expression_type", "decimal_places", "config", "cascade_parent_api_name",
            "select_color", "input_mode", "is_show_mask", "remove_mask_roles", "is_ocr_recognition", "identify_type",
            "is_need_cdn", "is_single", "show_detail_button", "is_create_when_master_create", "is_required_when_master_create");

    // 外部负责人
    public static final String RELATION_OUTER_OWNER = "outer_owner";
    // 仅主负责人添加到外部相关团队——只读
    public static final String RELATION_OUTER_EMPLOY_READ = "outer_employ_read";
    // 仅主负责人添加到外部相关团队——读写
    public static final String RELATION_OUTER_EMPLOY_WRITE = "outer_employ_write";
    // 互联企业添加到外部相关团队——只读
    public static final String RELATION_OUTER_TENANT_READ = "outer_tenant_read";
    // 互联企业添加到外部相关团队——读写
    public static final String RELATION_OUTER_TENANT_WRITE = "outer_tenant_write";
    // 不关联
    public static final String RELATION_NOT_RELATED = "not_related";
    /**
     * 详细地址的最大长度
     */
    public static final int AREA_DETAIL_ADDRESS_MAX_LENGTH = 300;
    public static final String MASK_FIELD_ENCRYPT = "mask_field_encrypt";
    public static final String ADD_TO_LAYOUT = "add_to_layout";

    public static final String OTHER_NAME_SUFFIX = "__o";

    public static final String LOOKUP_NAME_SUFFIX = "__r";

    public static final String RELATION_IDS_SUFFIX = "__relation_ids";

    public static final String EMPLOYEE_NAME_SUFFIX = "__l";

    public static final String QUOTE_NAME_SUFFIX = "__v";

    public static final String PHONE_NAME_SUFFIX = "__p";

    /**
     * 应用方（source）类型，对应配置文件
     */
    public static final String REF_TYPE_VAL = "field";

    /**
     * 对象.字段
     */
    public static final String REF_DISPLAY_TYPE_VAL = "o.f";

    public static final String LAST_MODIFIED_TIME = "last_modified_time";
    public static final String MARK = "mark";
    public static final String TEMP_FIELD = "temp_field";
    public static final String HIDDEN_PAGES = "hidden_pages";
    public static final String OUTER_ROLE = "outer_role";
    public static final String ROLE = "role";
    public static final String DATA_ROLES = "data_roles";
    public static final String AI_API_NAME = "ai_api_name";
    public static final String MUTIL_AI_API_NAME = "mutil_ai_api_name";

    @Delegate
    private final IFieldDescribe fieldDescribe;

    public final static Set<String> CANNOT_FILTER_FIELDS = ImmutableSet.of(EXTEND_OBJ_DATA_ID);

    private FieldDescribeExt(IFieldDescribe fieldDescribe) {
        this.fieldDescribe = fieldDescribe;
    }

    public static boolean isCurrencyOrNumberType(String returnType) {
        return IFieldType.CURRENCY.equals(returnType) || IFieldType.NUMBER.equals(returnType);
    }

    public boolean isWhereRelation() {
        return (Objects.nonNull(getRelatedWheres()) || Objects.nonNull(getWheres()));
    }

    public static FieldDescribeExt of(IFieldDescribe fieldDescribe) {
        return new FieldDescribeExt(fieldDescribe);
    }

    public static FieldDescribeExt of(Map<String, Object> map) {
        return of(FieldDescribeFactory.newInstance(map));
    }

    public static String getLookupNameByFieldName(String lookupFieldName) {
        return lookupFieldName + "__r";
    }

    public static String getLocalizationNameByFieldName(String fieldApiName) {
        return fieldApiName + "__geo";
    }


    public static String getFieldMappingFieldName(String fieldApiName) {
        return fieldApiName + "__wrap";
    }

    public static String getEmployeeNameByFieldName(String employeeName) {
        return employeeName + "__l";
    }

    public static String getSelectOther(String selectFieldName) {
        return selectFieldName + "__o";
    }

    public static String getQuotedValueNameByFieldName(String quoteFieldName) {
        return quoteFieldName + "__v";
    }

    public static String getQuotedFunctionVirtualFieldByFieldName(String lookupFieldName) {
        return lookupFieldName + "__q";
    }

    public static String getShowFieldName(String fieldName) {
        return fieldName + "__s";
    }

    public static String getMultiLangExtraFieldName(String fieldName) {
        return fieldName + "__lang";
    }

    public static String getMultiLangTempFieldName(String fieldName, Lang lang) {
        return String.format("%s__lang_%s", fieldName, lang.getValue());
    }

    public static String getMaskEncryptFieldName(String fieldName) {
        return fieldName + "__encrypt";
    }

    public static String getOldFieldName(String fieldName) {
        return fieldName + "__old";
    }

    public static String getChangeFieldName(String fieldName) {
        return "change_" + fieldName;
    }

    public static String getOriginalFieldName(String fieldName) {
        return "original_" + fieldName;
    }


    public static String getTeamMemberFieldApiName(String memberType, String role, String permission) {
        return String.join("_", memberType, role, permission);
    }

    public static boolean isOriginalFieldName(String fieldName) {
        if (ObjectDataExt.ORIGINAL_DATA.equals(fieldName) || ObjectDataExt.ORIGINAL_DETAIL_DATA.equals(fieldName)) {
            return false;
        }
        return StringUtils.startsWith(fieldName, "original_");
    }

    public static String getCurrencyFieldName(String fieldName) {
        return "currency_" + fieldName;
    }

    public static boolean isChangeFieldName(String fieldName) {
        return StringUtils.startsWith(fieldName, "change_");
    }

    public static String getOriginalFieldNameByChangeField(String fieldName) {
        String name = getFieldNameByChangeField(fieldName);
        if (Strings.isNullOrEmpty(name)) {
            return null;
        }
        return getOriginalFieldName(name);
    }

    public static String getFieldNameByChangeField(String fieldName) {
        if (!isChangeFieldName(fieldName)) {
            return null;
        }
        return StringUtils.substringAfter(fieldName, "change_");
    }

    public static boolean isDateTypeField(String fieldType) {
        return AppFrameworkConfig.getDateFieldTypes().contains(fieldType);
    }

    public static boolean isNumberTypeField(String fieldType) {
        return AppFrameworkConfig.getNumberFieldTypes().contains(fieldType);
    }

    public static boolean isFileTypeField(String fieldType) {
        return AppFrameworkConfig.getFileFieldTypes().contains(fieldType);
    }

    public static boolean isSelectField(String fieldType) {
        return IFieldType.SELECT_ONE.equals(fieldType) || IFieldType.SELECT_MANY.equals(fieldType);
    }

    public static boolean isUseRangeField(String fieldType) {
        return IFieldType.UseRange.equals(fieldType);
    }

    public static boolean isSelectOtherField(String fieldName) {
        return StringUtils.endsWith(fieldName, "__o");
    }

    public static boolean isMultiLangExtraField(String fieldName) {
        return StringUtils.endsWith(fieldName, "__lang");
    }

    public static boolean isMaskEncryptField(String fieldName) {
        return StringUtils.endsWith(fieldName, "__encrypt");
    }

    public static boolean isMaskShowFieldName(String fieldName) {
        return StringUtils.endsWith(fieldName, "__s");
    }

    public static String getMultiLangFieldFromExtraField(String fieldName) {
        return StringUtils.substringBeforeLast(fieldName, "__lang");
    }

    public static String getSelectFieldFromOtherField(String otherField) {
        return StringUtils.substringBeforeLast(otherField, "__o");
    }

    public static String getRichExtField(String otherField) {
        return StringUtils.substringBeforeLast(otherField, "__o");
    }

    public static String getFieldNameFromMaskEncryptFieldName(String fieldName) {
        return StringUtils.substringBeforeLast(fieldName, "__encrypt");
    }

    public static List<AutoNumber> getAutoNumberFields(List<IFieldDescribe> fieldDescribes) {
        return fieldDescribes.stream()
                .filter(fieldDescribe -> IFieldType.AUTO_NUMBER.equals(fieldDescribe.getType()))
                .filter(IFieldDescribe::isActive)
                .map(fieldDescribe -> (AutoNumber) fieldDescribe)
                .collect(toList());
    }

    public static String getReferenceLabelI18NKey(String objectApiName, String fieldApiName) {
        return String.format(REFERENCE_LABEL_I18N_KEY_TEMPLATE, objectApiName, fieldApiName);
    }

    public static String generateTargetRelatedListName() {
        return String.format("target_related_list_%s__c", RandomStringUtils.randomAlphanumeric(5));
    }

    public static IFieldDescribe copy(IFieldDescribe fieldDescribe) {
        return fieldDescribe.copy();
    }

    /**
     * 注意 field_num, id, is_extend, index_name 这几个属性不能复制
     *
     * @param fieldDescribe
     * @return
     */
    public static IFieldDescribe copyToSync(IFieldDescribe fieldDescribe) {
//        Map<String, Object> newMap= FieldDescribeExt.of(fieldDescribe).toMap().entrySet().stream()
//                .filter(it -> !UN_SYNC_FIELD.contains(it.getKey()))
//                .collect(Collectors.toMap(Map.Entry::getKey,Map.Entry::getValue));
        Map<String, Object> newMap = Maps.newHashMap();
        FieldDescribeExt.of(fieldDescribe).toMap()
                .forEach((key, value) -> {
                    if (UN_SYNC_FIELD.contains(key)) {
                        return;
                    }
                    newMap.put(key, value);
                });
        return FieldDescribeFactory.newInstance(newMap);
    }

    public Map<String, Object> toMap() {
        return ((DocumentBasedBean) fieldDescribe).getContainerDocument();
    }

    public IFieldDescribe copyOnWrite() {
        FieldDescribeExt cpField = FieldDescribeExt.of(CopyOnWriteMap.copy(toMap()));
        return cpField.getFieldDescribe();
    }

    public IFieldDescribe removeFieldPropsWithCopy4Ext() {
        Sets.newHashSet(toMap().keySet())
                .stream().filter(REMOVE_PROPS_IN_COPY4EXT::contains)
                .forEach(x -> toMap().remove(x));
        return fieldDescribe;
    }

    public String getFieldExtendName() {
        String type = getType();
        if (Strings.isNullOrEmpty(type)) {
            return null;
        }

        switch (type) {
            case IFieldType.OBJECT_REFERENCE_MANY:
            case IFieldType.OBJECT_REFERENCE:
            case IFieldType.MASTER_DETAIL:
            case IFieldType.COUNTRY:
            case IFieldType.PROVINCE:
            case IFieldType.CITY:
            case IFieldType.DISTRICT:
            case IFieldType.TOWN:
            case IFieldType.VILLAGE:
            case IFieldType.DIMENSION:
                return getApiName() + "__r";
            case IFieldType.DEPARTMENT_MANY:
            case IFieldType.EMPLOYEE_MANY:
            case IFieldType.DEPARTMENT:
            case IFieldType.EMPLOYEE:
            case IFieldType.OUT_DEPARTMENT:
            case IFieldType.OUT_EMPLOYEE:
                return getApiName() + "__l";
            case IFieldType.SELECT_MANY:
            case IFieldType.SELECT_ONE:
                return getApiName() + "__o";
            case IFieldType.QUOTE:
                return getApiName() + "__v";
            case IFieldType.PHONE_NUMBER:
                return getApiName() + "__p";
            default:
                return null;
        }
    }

    /**
     * 是否有一个公式的默认值
     *
     * @return
     */
    public boolean hasFormulaDefaultValue() {
        Boolean isFormula = fieldDescribe.getDefaultIsExpression();
        return (isFormula != null) && isFormula;
    }

    public boolean isFormula() {
        return fieldDescribe instanceof Formula;
    }

    public boolean hasCalculateValue() {
        return Boolean.TRUE.equals(getIsNeedCalculate());
    }

    public <T extends IFieldDescribe> T getFieldDescribe() {
        return (T) fieldDescribe;
    }

    public IFieldDescribe getExtData() {
        return fieldDescribe;
    }

    public boolean isCalculateField() {
        return hasFormulaDefaultValue() || isFormula() || hasCalculateValue();
    }

    public boolean isCountField() {
        return fieldDescribe instanceof Count;
    }

    public boolean isQuoteField() {
        return fieldDescribe instanceof Quote;
    }

    public boolean isLookupField() {
        return fieldDescribe instanceof IObjectReferenceField || fieldDescribe instanceof IObjectReferenceMany;
    }

    public boolean isNumberField() {
        return fieldDescribe instanceof Number;
    }

    public boolean isObjectReferenceField() {
        return fieldDescribe instanceof IObjectReferenceField;
    }

    public boolean isObjectReferenceManyField() {
        return fieldDescribe instanceof IObjectReferenceMany;
    }


    public boolean isTreeViewSelfAssociatedField() {
        return !Objects.isNull(fieldDescribe) && StringUtils.equals(fieldDescribe.getType(), IFieldType.OBJECT_REFERENCE)
                && StringUtils.equals(fieldDescribe.getDescribeApiName(), ((IObjectReferenceField) fieldDescribe).getTargetApiName())
                && StringUtils.isNotEmpty(((IObjectReferenceField) fieldDescribe).getTreeViewField());
    }

    public boolean isMasterDetailField() {
        return fieldDescribe instanceof MasterDetail;
    }

    public boolean isSelectOne() {
        return !Objects.isNull(fieldDescribe) && StringUtils.equals(fieldDescribe.getType(), IFieldType.SELECT_ONE);
    }

    public boolean isSelectMany() {
        return !Objects.isNull(fieldDescribe) && StringUtils.equals(fieldDescribe.getType(), IFieldType.SELECT_MANY);
    }

    public boolean isSelectField() {
        return isSelectOne() || isSelectMany();
    }

    public boolean isDataVisibilityRange() {
        return fieldDescribe instanceof DataVisibilityRange;
    }

    /**
     * @return 字段级联关系中的子选项类型
     */
    public boolean isCascadeChildField() {
        return CASCADE_CHILD_API_NAME_FIELD_TYPE.contains(fieldDescribe.getType());
    }

    /**
     * @return 字段级联关系中的父选项类型
     */
    public boolean isCascadeParentField() {
        return CASCADE_PARENT_API_NAME_FIELD_TYPE.contains(fieldDescribe.getType());
    }

    public boolean isRelatedDetailFieldAndFollowLookDataRights(String apiName) {
        if (fieldDescribe instanceof IObjectReferenceField) {
            return StringUtils.equals(apiName, ((IObjectReferenceField) fieldDescribe).getTargetApiName())
                    && Objects.equals(((IObjectReferenceField) fieldDescribe).isFollowLookupDataRights(), Boolean.TRUE);
        }

        if (fieldDescribe instanceof IObjectReferenceMany) {
            return StringUtils.equals(apiName, ((IObjectReferenceMany) fieldDescribe).getTargetApiName())
                    && Objects.equals(((IObjectReferenceMany) fieldDescribe).isFollowLookupDataRights(), Boolean.TRUE);
        }

        return false;
    }

    public boolean isRelatedDetailAndLookupRoles(String teamMemberRole, String apiName) {
        if (fieldDescribe instanceof IObjectReferenceField) {
            return CollectionUtils.notEmpty(((IObjectReferenceField) fieldDescribe).getLookupRoles())
                    && StringUtils.equals(apiName, ((IObjectReferenceField) fieldDescribe).getTargetApiName())
                    && ((IObjectReferenceField) fieldDescribe).getLookupRoles().contains(teamMemberRole);
        }

        if (fieldDescribe instanceof IObjectReferenceMany) {
            return CollectionUtils.notEmpty(((IObjectReferenceMany) fieldDescribe).getLookupRoles())
                    && StringUtils.equals(apiName, ((IObjectReferenceMany) fieldDescribe).getTargetApiName())
                    && ((IObjectReferenceMany) fieldDescribe).getLookupRoles().contains(teamMemberRole);
        }
        return false;
    }

    public boolean isCustomField() {
        return "custom".equals(getDefineType());
    }

    public boolean isSystemField() {
        return "system".equals(getDefineType());
    }

    public boolean isAbstractField() {
        return Objects.nonNull(fieldDescribe.isAbstract()) && fieldDescribe.isAbstract();
    }

    public boolean isGroupField() {
        return IFieldType.GROUP.equals(getType());
    }

    public boolean isAreaField() {
        return isGroupField() && GroupField.GROUP_TYPE_AREA.equals(((GroupField) fieldDescribe).getGroupType());
    }

    public boolean isDateTimeRangeField() {
        return isGroupField() && GroupField.GROUP_TYPE_DATE_TIME_RANGE.equals(((GroupField) fieldDescribe).getGroupType());
    }

    public boolean isGroupBizField() {
        return isGroupField() && "biz_field".equals(((GroupField) fieldDescribe).getGroupType());
    }

    public boolean isBizField() {
        return !Strings.isNullOrEmpty((String) get("biz_field_api_name"));
    }

    public String getBizFieldApiName() {
        return (String) get("biz_field_api_name");
    }

    public IFieldDescribe getBizField(IObjectDescribe describe) {
        GroupField groupField = (GroupField) fieldDescribe;
        try {
            return groupField.getFieldList(describe).get(0);
        } catch (MetadataServiceException e) {
            log.error("get biz field failed,objectApiName:{},groupFieldName:{}", describe.getApiName(), getApiName(), e);
            return null;
        }
    }

    public boolean isFileAttachFileOrImageField() {
        return IFieldType.FILE_ATTACHMENT.equals(getType()) || IFieldType.IMAGE.equals(getType());
    }

    public boolean isSignatureField() {
        return IFieldType.SIGNATURE.equals(getType());
    }


    public boolean isCountryField() {
        return IFieldType.COUNTRY.equals(getType());
    }

    public boolean isProvinceField() {
        return IFieldType.PROVINCE.equals(getType());
    }

    public boolean isCityField() {
        return IFieldType.CITY.equals(getType());
    }

    public boolean isTownField() {
        return IFieldType.TOWN.equals(getType());
    }

    public boolean isVillageField() {
        return IFieldType.VILLAGE.equals(getType());
    }

    public boolean isDistrictField() {
        return IFieldType.DISTRICT.equals(getType());
    }

    public boolean isRegionField() {
        return isCountryField() 
            || isProvinceField()
            || isCityField()
            || isDistrictField()
            || isTownField()
            || isVillageField();
    }

    public boolean isWhatListData() {
        return !Objects.isNull(fieldDescribe) && IFieldType.WHAT_LIST_DATA.equals(getType());
    }

    public boolean isWhatListField() {
        return !Objects.isNull(fieldDescribe) && isGroupField() && GroupField.GROUP_TYPE_WHAT_LIST.equals(((GroupField) fieldDescribe).getGroupType());
    }

    public boolean isWhatField() {
        return isGroupField() && GroupField.GROUP_TYPE_WHAT.equals(((GroupField) fieldDescribe).getGroupType());
    }

    public boolean isUsedByStage() {
        if (!IFieldType.SELECT_ONE.equals(fieldDescribe.getType())) {
            return false;
        }
        return Objects.isNull(((SelectOne) fieldDescribe).getIsUsedByStage()) ? false : ((SelectOne) fieldDescribe).getIsUsedByStage();
    }

    public boolean isUseRangeField() {
        return fieldDescribe instanceof UseRange;
    }


    public boolean isTrueOrFalseField() {
        return !Objects.isNull(fieldDescribe) && IFieldType.TRUE_OR_FALSE.equals(getType());
    }

    public boolean isRecordType() {
        return fieldDescribe instanceof RecordType;
    }


    public void descriptionNullToEmpty() {
        if (Objects.nonNull(fieldDescribe) && Objects.isNull(getDescription())) {
            setDescription("");
        }
    }

    public List<String> getLookupRoles() {
        if (!(fieldDescribe instanceof ObjectReferenceFieldDescribe)) {
            return null;
        }
        List<String> lookupRoles = ((ObjectReferenceFieldDescribe) fieldDescribe).getLookupRoles();
        return CollectionUtils.empty(lookupRoles) ? Lists.newArrayList() : lookupRoles;
    }

    public void setReadOnly(boolean readOnly) {
        set(IFormField.IS_READ_ONLY, readOnly);
    }

    public boolean isReadOnly() {
        return BooleanUtils.isTrue(get(IFormField.IS_READ_ONLY, Boolean.class));
    }

    public void setHide(boolean isHide) {
        set("is_hide", isHide);
    }

    public boolean isHide() {
        return Boolean.TRUE.equals(get("is_hide"));
    }

    public void setPattern(String pattern) {
        set("pattern", pattern);
    }

    public boolean isAutoNumber() {
        return IFieldType.AUTO_NUMBER.equals(getType());
    }

    public void fillDefaultShowDetailButton() {
        if (!isMasterDetailField()) {
            return;
        }
        MasterDetail masterDetail = getFieldDescribe();
        if (Objects.isNull(masterDetail.getShowDetailButton())) {
            masterDetail.setShowDetailButton(true);
        }
    }

    public CalculateRelation getCalculateRelation() {
        Map<String, Object> map = get(CalculateRelation.CALCULATE_RELATION, Map.class);
        if (map == null) {
            return new CalculateRelation();
        }
        return new CalculateRelation(map);
    }

    public void setCalculateRelation(CalculateRelation calculateRelation) {
        set(CalculateRelation.CALCULATE_RELATION, calculateRelation.toMap());
    }

    public Map<String, Set<RelateField>> getRelateFields() {
        return getCalculateRelation().getRelateFields();
    }

    public List<String> getSelfRelateFields() {
        Map<String, Set<RelateField>> relateFields = getRelateFields();
        if (CollectionUtils.empty(relateFields)) {
            return Lists.newArrayList();
        }
        return relateFields.getOrDefault(getDescribeApiName(), Sets.newHashSet()).stream()
                .sorted(Comparator.comparingInt(RelateField::getOrder))
                .map(RelateField::getFieldName)
                .collect(Collectors.toList());
    }

    public List<String> getCascadeParentApiNames() {
        Object value = get(ObjectDescribeExt.CASCADE_PARENT_API_NAME);
        if (ObjectDataExt.isValueEmpty(value)) {
            return Lists.newArrayList();
        }
        if (value instanceof String) {
            return Lists.newArrayList((String) value);
        } else if (value instanceof Collection) {
            return Lists.newArrayList((Collection) value);
        }
        return Lists.newArrayList();
    }

    public void setCascadeParentApiName(String parentApiName) {
        set(ObjectDescribeExt.CASCADE_PARENT_API_NAME, parentApiName);
    }

    public boolean isParentApiName(String apiName) {
        List<String> parentApiNames = getCascadeParentApiNames();
        if (CollectionUtils.empty(parentApiNames)) {
            return false;
        }
        return parentApiNames.contains(apiName);
    }

    public boolean isShowMask() {
        return Boolean.TRUE.equals(getIsShowMask());
    }

    public boolean isRemoveMaskForOwner() {
        Map removeMaskRoles = getRemoveMaskRoles();
        if (CollectionUtils.empty(removeMaskRoles)) {
            return false;
        }
        return CollectionUtils.nullToEmpty((List<String>) removeMaskRoles.get(DATA_ROLES)).contains("owner");
    }

    public boolean isRemoveMaskForOutOwner() {
        Map removeMaskRoles = getRemoveMaskRoles();
        if (CollectionUtils.empty(removeMaskRoles)) {
            return false;
        }
        return CollectionUtils.nullToEmpty((List<String>) removeMaskRoles.get(DATA_ROLES)).contains("out_owner");
    }

    public boolean isRemoveMaskForDataOwnerMainDeptLeader() {
        Map removeMaskRoles = getRemoveMaskRoles();
        if (CollectionUtils.empty(removeMaskRoles)) {
            return false;
        }
        return CollectionUtils.nullToEmpty((List<String>) removeMaskRoles.get(DATA_ROLES)).contains("data_owner_main_dept_leader");
    }

    public boolean existMaskFieldRules(String type) {
        if (!isShowMask()) {
            return false;
        }
        Map removeMaskRoles = getRemoveMaskRoles();
        if (CollectionUtils.empty(removeMaskRoles)) {
            return false;
        }
        List<String> removeMaskRoleList = (List<String>) removeMaskRoles.get(type);
        boolean isRoleType = StringUtils.equals(type, ROLE);
        // role类型时 removeMaskRoleList为null时表示存量数据
        if (isRoleType && Objects.isNull(removeMaskRoleList)
                || (!isRoleType && CollectionUtils.empty(removeMaskRoleList))) {
            return false;
        }
        return true;
    }

    public List<String> getMaskRolesConfig(User user) {
        String roleType = user.isOutUser() ? OUTER_ROLE : ROLE;
        if (existMaskFieldRules(roleType)) {
            return getRemoveMaskRoles().get(roleType);
        }
        return Lists.newArrayList();
    }

    public boolean haveRolesConfig() {
        return existMaskFieldRules(ROLE);
    }

    public boolean haveRolesConfig(User user) {
        if (user.isOutUser()) {
            return existMaskFieldRules(OUTER_ROLE);
        }
        return haveRolesConfig();
    }

    public void addDefaultOtherOption() {
        if (!isSelectOne() && !isSelectMany()) {
            return;
        }
        // 引用选项集的单选，不需要补充其他选项
        if (FieldDescribeExt.of(fieldDescribe).isGeneralOptions()) {
            return;
        }
        SelectOne selectOne = (SelectOne) fieldDescribe;
        if (!selectOne.isOptionExist(SelectOne.OPTION_OTHER_VALUE)) {
            selectOne.addSelectOption(buildDefaultOtherOption());
        }
    }

    private ISelectOption buildDefaultOtherOption() {
        ISelectOption option = new SelectOption();
        option.setValue(SelectOne.OPTION_OTHER_VALUE);
        option.setLabel(I18N.text(I18NKey.OTHER));
        option.setNotUsable(true);
        return option;
    }

    public void removeRelatedObjectFilter(User user) {
        if (!isLookupField() && !isEmployee() && !isDepartmentField()) {
            return;
        }
        List<Wheres> wheres = getWheres();
        Wheres.removeRelatedObjectFilter(user, wheres);
        setWheres(wheres);
    }

    public List<Wheres> getWheres() {
        List wheres = fieldDescribe.get(ObjectReferenceFieldDescribe.WHERES, List.class);
        return Wheres.ofList(wheres);
    }

    public List<Wheres> getRelatedWheres() {
        List wheres = fieldDescribe.get(ObjectReferenceFieldDescribe.RELATION_WHERES, List.class);
        return Wheres.ofList(wheres);
    }

    private void setWheres(List<Wheres> wheres) {
        List<LinkedHashMap> wheresMap = wheres.stream()
                .map(it -> {
                    Map map = it.getContainerDocument();
                    if (map instanceof LinkedHashMap) {
                        return (LinkedHashMap) map;
                    }
                    return new LinkedHashMap(map);
                })
                .collect(Collectors.toList());
        fieldDescribe.set(ObjectReferenceFieldDescribe.WHERES, wheresMap);
    }

    public boolean isDateField() {
        return fieldDescribe instanceof Date;
    }

    public boolean isImageField() {
        return fieldDescribe instanceof Image;
    }

    public RoundingMode getRoundingMode() {
        try {
            Integer roundingMode = getRoundMode();
            if (roundingMode == null) {
                return DEFAULT_ROUNDING_MODE;
            }
            return RoundingMode.valueOf(roundingMode);
        } catch (Exception e) {
            log.warn("getRoundingMode failed,field:{}", this.toJsonString(), e);
            return DEFAULT_ROUNDING_MODE;
        }
    }

    public boolean isSupportGeoSearch() {
        return Objects.equals(IFieldType.LOCATION, fieldDescribe.getType())
                && BooleanUtils.isTrue(((Location) fieldDescribe).getIsGeoIndex());
    }

    boolean defaultValueNotEmpty() {
        if (Objects.isNull(getDefaultValue())) {
            return false;
        }
        if (getDefaultValue() instanceof Collection) {
            return CollectionUtils.notEmpty((Collection) getDefaultValue());
        }
        return !Strings.isNullOrEmpty(String.valueOf(getDefaultValue()));
    }

    public boolean isLocation() {
        return fieldDescribe instanceof Location;
    }

    public boolean isDimension() {
        return fieldDescribe instanceof Dimension;
    }

    public boolean isOutEmployee() {
        return fieldDescribe instanceof OutEmployee;
    }

    public boolean isEmployee() {
        return fieldDescribe instanceof Employee;
    }

    public boolean needFillEmployeeExtAttribute() {
        return FILL_EMPLOYEE_EXT_TYPES.contains(getType());
    }

    public boolean needFillDepartmentExtAttribute() {
        return FILL_DEPARTMENT_EXT_TYPES.contains(getType());
    }

    public boolean isEmployeeManyField() {
        return fieldDescribe instanceof EmployeeMany;
    }

    public boolean isWhereRelationField() {
        return fieldDescribe instanceof IObjectReferenceField || fieldDescribe instanceof IObjectReferenceMany
                || fieldDescribe instanceof Employee || fieldDescribe instanceof EmployeeMany
                || fieldDescribe instanceof Department || fieldDescribe instanceof DepartmentMany;
    }

    public boolean isText() {
        return IFieldType.TEXT.equals(getType());
    }

    public boolean isTextType() {
        return IFieldType.TEXT.equals(getType()) || IFieldType.LONG_TEXT.equals(getType());
    }

    public String getTypeOrReturnType() {
        if (isFormula()) {
            return ((Formula) fieldDescribe).getReturnType();
        }
        if (isCountField()) {
            return ((Count) fieldDescribe).getReturnType();
        }
        if (isQuoteField()) {
            return ((Quote) fieldDescribe).getQuoteFieldType();
        }
        return getType();
    }

    public String getFormulaOrCurrencyTypeOrReturnType() {
        if (isFormula()) {
            return ((Formula) fieldDescribe).getReturnType();
        }
        return getType();
    }

    public String getCountOrCurrencyTypeOrReturnType() {
        if (isCountField()) {
            return ((Count) fieldDescribe).getReturnType();
        }
        return getType();
    }

    public boolean realTypeIsDate() {
        return IFieldType.DATE.equals(getTypeOrReturnType());
    }

    public boolean isCurrencyType() {
        if (isCountField()) {
            return CountExt.of((Count) fieldDescribe).isCurrencyType();
        }
        return IFieldType.CURRENCY.equals(getTypeOrReturnType());
    }

    public boolean formulaOrCurrencyRealTypeIsCurrency() {
        return IFieldType.CURRENCY.equals(getFormulaOrCurrencyTypeOrReturnType());
    }

    public boolean countOrCurrencyRealTypeIsCurrency() {
        return IFieldType.CURRENCY.equals(getCountOrCurrencyTypeOrReturnType());
    }

    public boolean isMultiCurrencyCalculateFields() {
        String apiName = getApiName();
        return isMultiCurrencyCalculateFields(apiName);
    }

    public boolean isUIMultiCurrencyFields() {
        String apiName = getApiName();
        return EXCHANGE_RATE_VERSION_FIELD.equals(apiName) || FUNCTIONAL_CURRENCY_FIELD.equals(apiName);
    }

    public static boolean isMultiCurrencyCalculateFields(String apiName) {
        return EXCHANGE_RATE_FIELD.equals(apiName)
                || EXCHANGE_RATE_VERSION_FIELD.equals(apiName)
                || FUNCTIONAL_CURRENCY_FIELD.equals(apiName);
    }

    public boolean isMultiCurrencyFields() {
        String apiName = getApiName();
        return isMultiCurrencyFields(apiName);
    }

    public boolean isOwnerField() {
        return IObjectData.OWNER.equals(getApiName());
    }

    public static boolean isMultiCurrencyFields(String apiName) {
        return CURRENCY_FIELD.equals(apiName) || isMultiCurrencyCalculateFields(apiName);
    }

    public boolean isEmployeeField() {
        return fieldDescribe instanceof Employee;
    }

    public boolean isDepartmentField() {
        return fieldDescribe instanceof Department;
    }

    public boolean isDepartmentManyField() {
        return fieldDescribe instanceof DepartmentMany;
    }

    public boolean isNumberTypeField() {
        return isNumberTypeField(getTypeOrReturnType());
    }

    public void mergeFrom(IFieldDescribe fieldDescribe) {
        Map<String, Object> sourceMap = of(fieldDescribe).toMap();
        toMap().putAll(sourceMap);
    }

    public void mergeFromObjectFieldExtra(IObjectFieldExtra fieldExtra) {
        if (Objects.isNull(fieldExtra) || !Objects.equals(fieldExtra.getFieldApiName(), getApiName())) {
            return;
        }
        Map<String, Object> fieldExtraMap = ((ObjectFieldExtra) fieldExtra).getContainerDocument();

        fieldExtraMap.keySet().stream()
                .filter(it -> !"field_api_name".equals(it))
                .forEach(key -> set(key, fieldExtraMap.get(key)));
    }

    public boolean isCalculateFieldsNeedStoreInDB() {
        if (!isFormula() && !isCountField() && !isQuoteField() && !hasCalculateValue()) {
            return false;
        }
        if (!isActive()) {
            return false;
        }
        if (isAbstractField()) {
            return false;
        }
        if (!hasCalculateValue() && BooleanUtils.isFalse(isIndex())) {
            return false;
        }
        return true;
    }

    public static int getMaxNum(String tenantId, String apiName) {
        return FieldManyMaxConfig.getEmployeeManyMaxLimit(tenantId, apiName);
    }

    public void addMaxNum(Integer num) {
        fieldDescribe.set("max_num", num);
    }

    public void convert2SystemZone(Function<String, IObjectDescribe> findDescribe) {
        convertDateFieldFilter(findDescribe, true);
    }

    public void convert2CustomZone(Function<String, IObjectDescribe> findDescribe) {
        convertDateFieldFilter(findDescribe, false);
    }

    private void convertDateFieldFilter(Function<String, IObjectDescribe> findDescribe, boolean toSystemZone) {
        Objects.requireNonNull(findDescribe);
        if (!isLookupField() && !isEmployee() && !isCountField()) {
            return;
        }
        List<Wheres> wheres = getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }
        String targetApiName = getTargetApiName();
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(findDescribe.apply(targetApiName));
        for (Wheres where : wheres) {
            List<Filter> filters = where.getFilters().stream()
                    .map(Filter::toIFilter)
                    .peek(filter -> FilterExt.convertDateFieldFilter(describeExt, filter, toSystemZone))
                    .map(Filter::of)
                    .collect(Collectors.toList());
            where.filters(filters);
        }
        setWheres(wheres);
    }

    private String getTargetApiName() {
        if (isEmployee()) {
            return Utils.PERSONNEL_OBJ_API_NAME;
        }
        if (isCountField()) {
            return get(CountFieldDescribe.SUB_OBJECT_DESCRIBE_API_NAME, String.class);
        }
        return get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class);
    }

    public void addDisplayNameSwitch(Boolean tureOrFalse) {
        fieldDescribe.set(IObjectDescribe.IS_OPEN_DISPLAY_NAME, tureOrFalse);
    }

    public void addTreeViewSwitch(Boolean tureOrFalse) {
        fieldDescribe.set(IObjectDescribe.IS_SUPPORT_TREE_VIEW, tureOrFalse);
    }

    public void addBigObjectFlag(String bigObjectFlag) {
        fieldDescribe.set(IObjectDescribe.VISIBLE_SCOPE, bigObjectFlag);
    }

    public void addBigTreeFlag(String bigTreeFlag) {
        fieldDescribe.set(IObjectDescribe.TREE_TYPE, bigTreeFlag);
    }

    public String getRefObjApiName() {
        String fieldApiName;
        if (IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
            ObjectReferenceFieldDescribe lookupField = (ObjectReferenceFieldDescribe) fieldDescribe;
            fieldApiName = lookupField.getApiName();
        } else if (IFieldType.OBJECT_REFERENCE_MANY.equals(fieldDescribe.getType())) {
            ObjectReferenceManyFieldDescribe lookupField = (ObjectReferenceManyFieldDescribe) fieldDescribe;
            fieldApiName = lookupField.getApiName();
        } else if (ObjectDescribeExt.isWhatField(fieldDescribe)) {
            WhatFieldDescribe what = (WhatFieldDescribe) fieldDescribe;
            fieldApiName = what.getIdFieldApiName();
        } else {
            MasterDetailFieldDescribe lookupField = (MasterDetailFieldDescribe) fieldDescribe;
            fieldApiName = lookupField.getApiName();
        }
        return fieldApiName;
    }

    public String getRefObjTargetApiName() {
        String fieldTargetApiName;
        if (IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
            ObjectReferenceFieldDescribe lookupField = (ObjectReferenceFieldDescribe) fieldDescribe;
            fieldTargetApiName = lookupField.getTargetApiName();
        } else if (IFieldType.OBJECT_REFERENCE_MANY.equals(fieldDescribe.getType())) {
            ObjectReferenceManyFieldDescribe lookupField = (ObjectReferenceManyFieldDescribe) fieldDescribe;
            fieldTargetApiName = lookupField.getTargetApiName();
        } else {
            MasterDetailFieldDescribe lookupField = (MasterDetailFieldDescribe) fieldDescribe;
            fieldTargetApiName = lookupField.getTargetApiName();
        }
        return fieldTargetApiName;
    }

    public String getRefObjTargetApiNameByType() {
        if (REF_LIKES_FIELD_TYPE.contains(fieldDescribe.getType())) {
            return getRefObjTargetApiName();
        }
        return "";
    }

    public String getRefObjTargetApiNameDirectly() {
        if (Objects.nonNull(fieldDescribe) && isRefObjectField()) {
            return getRefObjTargetApiName();
        }
        return null;
    }

    public boolean defaultIsExpression() {
        return BooleanUtils.isTrue(get(IFieldDescribe.DEFAULT_IS_EXPRESSION, Boolean.class));
    }

    public boolean isRefObjectField() {
        return IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())
                || IFieldType.OBJECT_REFERENCE_MANY.equals(fieldDescribe.getType())
                || IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType());
    }

    public boolean isSupportRelationOuterOwner() {
        if (isObjectReferenceField()) {
            return RELATION_OUTER_OWNER.equals(((IObjectReferenceField) fieldDescribe).getRelationOuterDataPrivilege());
        }
        return false;
    }

    /**
     * 开启了相关团队数据权限的字段
     *
     * @return
     */
    public boolean isSupportRelation() {
        if (isObjectReferenceField()) {
            String dataPrivilege = ((IObjectReferenceField) fieldDescribe).getRelationOuterDataPrivilege();
            return !Strings.isNullOrEmpty(dataPrivilege) && !RELATION_NOT_RELATED.equals(dataPrivilege);
        }
        return false;
    }

    public boolean isSupportRelationNeedUserInfo() {
        if (isObjectReferenceField()) {
            String dataPrivilege = ((IObjectReferenceField) fieldDescribe).getRelationOuterDataPrivilege();
            return RELATION_OUTER_OWNER.equals(dataPrivilege)
                    || RELATION_OUTER_EMPLOY_READ.equals(dataPrivilege)
                    || RELATION_OUTER_EMPLOY_WRITE.equals(dataPrivilege);
        }
        return false;
    }

    public boolean isGeneralOptions() {
        if (isSelectOne() || isSelectMany()) {
            return SelectOne.GENERAL_OPTION_TYPE.equals(SelectOneExt.of(((SelectOne) fieldDescribe)).getOptionType());
        }
        return false;
    }

    public String getFuncApiNameFromWhatListField() {
        if (isWhatListField()) {
            List<LinkedHashMap> wheres = ((WhatList) getFieldDescribe()).getWheres();
            if (CollectionUtils.notEmpty(wheres)) {
                Map<String, String> map = wheres.get(0);
                if (CollectionUtils.notEmpty(map)) {
                    return map.get("func_api_name");
                }
            }
        }
        return null;
    }

    /**
     * 无效的默认值
     * <p>
     * 使字段默认值无效的条件
     * 1. 字段描述设置有where条件
     *
     * @return
     */
    public boolean invalidDefaultValue() {
        return CollectionUtils.notEmpty(getWheres());
    }

    /**
     * 纠正国家省市区组件建中 详细地址的最大长度
     */
    public void correctAreaDetailAddress(@NonNull ObjectDescribeExt describeExt) {
        if (!isAreaField()) {
            return;
        }
        Area area = getFieldDescribe();

        describeExt.getActiveFieldDescribeSilently(area.getAreaDetailAddressFieldApiName())
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isText)
                .map(FieldDescribeExt::<Text>getFieldDescribe)
                .ifPresent(text -> text.setMaxLength(AREA_DETAIL_ADDRESS_MAX_LENGTH));
    }

    public void remove(String key) {
        ((AbstractFieldDescribe) this.getFieldDescribe()).remove(key);
    }

    public boolean isNotIndexFieldForOutUser() {
        return (IFieldType.EMPLOYEE.equals(getType()) ||
                IFieldType.DEPARTMENT.equals(getType()) ||
                IFieldType.EMPLOYEE_MANY.equals(getType()) ||
                IFieldType.DEPARTMENT_MANY.equals(getType()))
                && !IObjectData.OUT_OWNER.equals(getApiName());
    }

    public void setMaskFieldEncrypt(Boolean maskFieldEncrypt) {
        set(MASK_FIELD_ENCRYPT, maskFieldEncrypt);
    }

    public boolean getMaskFieldEncrypt() {
        return BooleanUtils.isTrue(get(MASK_FIELD_ENCRYPT, Boolean.class));
    }

    public int getDecimalPlaces() {
        int decimalPlaces = 2;
        if (fieldDescribe instanceof Number) {
            decimalPlaces = Optional.ofNullable(((Number) fieldDescribe).getDecimalPlaces()).orElse(2);
        } else if (fieldDescribe instanceof Percentile) {
            final PercentileExt percentileExt = PercentileExt.of(((Percentile) fieldDescribe));
            decimalPlaces = percentileExt.isAutoAdaptPlaces() ? percentileExt.getDecimalPlaces() : 4;
        } else if (fieldDescribe instanceof Formula) {
            decimalPlaces = ((Formula) fieldDescribe).getDecimalPlaces();
        } else if (fieldDescribe instanceof Count) {
            if (isDateTypeField(((Count) fieldDescribe).getReturnType())) {
                return 0;
            }
            decimalPlaces = Optional.ofNullable(((Count) fieldDescribe).getDecimalPlaces()).orElse(2);
        }
        return decimalPlaces;
    }

    public boolean isRelatedListAddToLayout() {
        if (!isLookupField()) {
            return true;
        }
        if (ObjectReferenceWrapper.of(fieldDescribe).containsKey(ADD_TO_LAYOUT)) {
            Boolean addToLayout = ObjectReferenceWrapper.of(fieldDescribe).get(ADD_TO_LAYOUT, Boolean.class);
            // 必须要移除此属性
            ObjectReferenceWrapper.of(fieldDescribe).remove(ADD_TO_LAYOUT);
            if (BooleanUtils.isFalse(addToLayout)) {
                return false;
            }
        }
        return true;
    }

    public void setIfAbsent(String key, Object value) {
        toMap().putIfAbsent(key, value);
    }

    public Set<String> getAnyFieldExtendNames() {
        Set<String> extNames = Sets.newHashSet();
        extNames.add(getApiName() + OTHER_NAME_SUFFIX);
        extNames.add(getApiName() + LOOKUP_NAME_SUFFIX);
        extNames.add(getApiName() + RELATION_IDS_SUFFIX);
        extNames.add(getApiName() + EMPLOYEE_NAME_SUFFIX);
        extNames.add(getApiName() + QUOTE_NAME_SUFFIX);
        extNames.add(getApiName() + PHONE_NAME_SUFFIX);
        return extNames;
    }

    public IFieldDescribe generatedChangeOrderOriginalField(String originalFieldName) {
        IFieldDescribe copyToSync = copyToSync(fieldDescribe);
        copyToSync.setApiName(originalFieldName);
        copyToSync.set("is_extend", fieldDescribe.get("is_extend"));
        copyToSync.setLabel(I18NExt.text(I18NKey.ORIGINAL) + fieldDescribe.getLabel());
        return copyToSync;
    }

    public Boolean getEnableCloneActual() {
        return this.get(ENABLE_CLONE, Boolean.class);
    }

    public Tuple<String, String> parseQuoteField() {
        String[] parts = StringUtils.split(getQuoteField(), ".");
        return Tuple.of(StringUtils.substringBeforeLast(parts[0], "__r"), parts[1]);
    }

    public boolean isEnableDataMultiLang() {
        return BooleanUtils.isTrue(getEnableMultiLang());
    }

    public boolean isDisplayName() {
        return StringUtils.equals(DISPLAY_NAME, getApiName());
    }

    boolean isPublicField() {
        return isPublicFieldPrivateData() || isPublicFieldPublicData();
    }

    boolean isPublicFieldPrivateData() {
        return IFieldDescribe.PUBLIC_FIELD_PRIVATE_DATA_BELONG_TENANT.equals(getBelongTenant());
    }

    boolean isPublicFieldPublicData() {
        return Strings.isNullOrEmpty(getBelongTenant());
    }

    public boolean isUsedInGroup() {
        return IFieldDescribe.USED_IN_COMPONENT.equals(fieldDescribe.getUsedIn());
    }

    public boolean isCalculateTypeField() {
        return isFormula() || isCountField() || isQuoteField();
    }

    public boolean isTempField() {
        Map extendInfo = getExtendInfo();
        if (CollectionUtils.empty(extendInfo)) {
            return false;
        }
        Object o = extendInfo.get(MARK);
        return Objects.equals(o, TEMP_FIELD);
    }

    public boolean noOutOwnerMaskConfig(User user, String outOwnerId) {
        return !isRemoveMaskForOutOwner()
                || StringUtils.isBlank(outOwnerId)
                || !StringUtils.equals(user.getOutUserId(), outOwnerId);
    }

    public boolean noOutOwnerMaskConfig(User user, IObjectData data) {
        return noOutOwnerMaskConfig(user, ObjectDataExt.of(data).getOutOwnerId().orElse(null));
    }

    public String getAiApiName() {
        return get(AI_API_NAME, String.class);
    }

    public List<String> getMutilAiApiName() {
        return (List<String>) get(MUTIL_AI_API_NAME, List.class);
    }

    public boolean isPrimaryInheritField() {
        return Objects.equals(MetadataConstant.InheritType.PRIMARY_INHERIT_TYPE, getInheritType());
    }

    public Object handlerMultiLang(String tenantId, IObjectData data) {
        if (Objects.isNull(data) || StringUtils.isEmpty(tenantId)) {
            return null;
        }
        if (!isTextType() || !AppFrameworkConfig.objectMultiLangGray(tenantId, StringUtils.defaultIfEmpty(getDescribeApiName(), ""))
                || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.QUOTE_SUPPORT_MULTI_LANG, tenantId)) {
            return null;
        }
        if (!getEnableMultiLang()) {
            return null;
        }

        String lang = I18N.getContext().getLanguage();
        Object langValue = ObjectDataExt.of(data).getMultiLangFieldValue(getApiName(), lang);
        if (!ObjectDataExt.isValueEmpty(langValue)) {
            return langValue;
        }
        return null;
    }

    public Map getFieldDocument() {
        if(fieldDescribe instanceof AbstractFieldDescribe) {
            return ((AbstractFieldDescribe) this.fieldDescribe).getContainerDocument();
        }
        return Maps.newHashMap();
    }

    public Boolean getIsSingle() {
        return get(EmployeeFieldDescribe.IS_SINGLE, Boolean.class);
    }
}
