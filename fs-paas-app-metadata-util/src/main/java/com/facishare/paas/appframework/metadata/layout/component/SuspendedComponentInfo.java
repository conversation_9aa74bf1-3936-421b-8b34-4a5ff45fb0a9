package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.component.AbstractComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class SuspendedComponentInfo extends AbstractComponent implements ISuspendedComponentInfo {

    public SuspendedComponentInfo() {
    }

    public SuspendedComponentInfo(Map map) {
        super(map);
    }

    @Override
    public String getType() {
        return get(TYPE, String.class);
    }

    @Override
    public void setType(String type) {
        set(TYPE, type);
    }

    @Override
    public List<SuspendedActionInfo> getActions() {
        List<Map> actions = (List<Map>) get(ACTIONS);
        if (Objects.isNull(actions)) {
            return Lists.newArrayList();
        }
        return actions.stream()
                .map(SuspendedActionInfo::new)
                .collect(Collectors.toList());
    }

    @Override
    public void setActions(List<SuspendedActionInfo> actions) {
        if (Objects.isNull(actions)) {
            set(ACTIONS, Lists.newArrayList());
        } else {
            set(ACTIONS, actions.stream()
                    .map(SuspendedActionInfo::getContainerDocument)
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public Map<String, Object> getI18NProps() {
        Map<String, Object> i18nProps = (Map<String, Object>) get(I18N_PROPS);
        if (Objects.isNull(i18nProps)) {
            return Maps.newHashMap();
        }
        return i18nProps;
    }

    @Override
    public void onFieldDelete(IFieldDescribe iFieldDescribe) throws MetadataServiceException {

    }

    @Override
    public void onFieldUpdate(String s, Map map) throws MetadataServiceException {

    }
}
