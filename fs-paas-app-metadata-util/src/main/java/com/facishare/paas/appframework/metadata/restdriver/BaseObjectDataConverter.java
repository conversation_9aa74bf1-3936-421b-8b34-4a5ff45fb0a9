package com.facishare.paas.appframework.metadata.restdriver;

import com.facishare.paas.appframework.common.util.DateTimeUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Formula;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础对象字段转换逻辑
 * <p>
 * Created by liyiguang on 2017/9/19.
 */
@ToString
public class BaseObjectDataConverter implements ObjectDataConverter {
    protected ImmutableBiMap<String, String> fieldMapping;

    public BaseObjectDataConverter(Collection<Tuple<String, String>> entryList) {
        Map<String, String> tmpFieldMapping = entryList.stream()
                .collect(Collectors.toMap(x -> x.getKey(), x -> x.getValue()));
        this.fieldMapping = ImmutableBiMap.copyOf(tmpFieldMapping);
    }

    @Override
    public IObjectData toObjectData(Map<String, Object> srcData, IObjectDescribe describe) {
        if (srcData == null) {
            return null;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        return convert(srcData, describeExt);
    }

    @Override
    public List<IObjectData> toDataObjects(List<Map<String, Object>> srcDataList, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        return srcDataList.stream().map(x -> convert(x, describeExt)).collect(Collectors.toList());
    }

    @Override
    public IObjectData toOldObjectData(Map<String, Object> srcData, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        ObjectData ret = new ObjectData();
        srcData.entrySet().stream()
                .map(x -> Tuple.of(x.getKey(), x.getValue()))
                .map(x -> convertEmployeeFiledReverse(x, describeExt))
                .map(this::convertRefFieldReverse)
                .map(x -> covertToSFAImageField(x, describeExt))
                .map(this::convertFieldNameReverse)
                .forEach(x -> ret.set(x.getKey(), x.getValue()));

        return ret;
    }

    @Override
    public String toNewFieldName(String oldFieldName) {
        if (Strings.isNullOrEmpty(oldFieldName)) {
            return null;
        }
        return convertFieldName(Tuple.of(oldFieldName, null)).getKey();
    }

    @Override
    public String toOldFieldName(String newFieldName) {
        if (Strings.isNullOrEmpty(newFieldName)) {
            return null;
        }
        return convertFieldNameReverse(Tuple.of(newFieldName, null)).getKey();
    }

    @Override
    public Map<String, String> toNewFieldNames(List<String> oldFieldNames) {
        if (CollectionUtils.isEmpty(oldFieldNames)) {
            return Maps.newHashMap();
        }
        Map<String, String> mappings = oldFieldNames.stream()
                .collect(Collectors.toMap(fieldName -> fieldName, fieldName -> toNewFieldName(fieldName)));

        return mappings;
    }

    @Override
    public Map<String, String> toOldFieldNames(List<String> newFieldNames) {
        if (CollectionUtils.isEmpty(newFieldNames)) {
            return Maps.newHashMap();
        }
        Map<String, String> mappings = newFieldNames.stream()
                .collect(Collectors.toMap(fieldName -> fieldName, fieldName -> toOldFieldName(fieldName)));

        return mappings;
    }

    protected IObjectData convert(Map<String, Object> srcData, ObjectDescribeExt describeExt) {
        ObjectData ret = new ObjectData();
        srcData.entrySet().stream()
                .map(x -> Tuple.of(x.getKey(), x.getValue()))
                .map(this::convertFieldName)
                .map(x -> convertEmployeeFiled(x, describeExt))
                .map(x -> convertOutTenantIdFiled(x, describeExt))
                .map(this::convertRefField)
                .map(x -> convertTimeField(x, describeExt))
                .map(x -> convertNumberField(x, describeExt))
                .map(x -> convertImageField(x, describeExt))
                .map(x -> convertFormulaField(x, describeExt))
                .map(x -> convertDepartmentFiled(x, describeExt))
                .filter(x -> filterField(x.getKey(), describeExt))
                .forEach(x -> ret.set(x.getKey(), x.getValue()));

        ret.setTenantId(describeExt.getTenantId());
        ret.setDescribeApiName(describeExt.getApiName());
        ret.setDescribeId(describeExt.getId());

        return ret;
    }

    protected Tuple<String, Object> convertTimeField(Tuple<String, Object> entry, ObjectDescribeExt describeExt) {

        describeExt.getFieldDescribeSilently(entry.getKey()).filter(x ->
                IFieldType.DATE.equals(x.getType()) ||
                        IFieldType.DATE_TIME.equals(x.getType()) ||
                        IFieldType.TIME.equals(x.getType())).ifPresent(x -> {
            Object value = entry.getValue();
            if (Objects.nonNull(value)) {
                if (value instanceof BigDecimal) {
                    entry.setValue(((BigDecimal) value).toBigInteger());
                } else if (value instanceof String) {
                    long timestamp = DateTimeUtils.parseISOLocateDateTime((String) value, x.getType());
                    entry.setValue(timestamp);
                }
            }
        });
        return entry;
    }


    protected Tuple<String, Object> convertNumberField(Tuple<String, Object> entry, ObjectDescribeExt describeExt) {
        //兼容处理
        Object value = entry.getValue();
        if (Objects.nonNull(value) && value instanceof BigDecimal) {
            entry.setValue(((BigDecimal) value).toPlainString());
        }
        return entry;
    }

    protected Tuple<String, Object> covertToSFAImageField(Tuple<String, Object> entry, ObjectDescribeExt describeExt) {
        describeExt.getFieldDescribeSilently(entry.getKey())
                .filter(x -> IFieldType.IMAGE.equals(x.getType())).ifPresent(x -> {
            if (entry.getValue() instanceof List) {
                List<Object> images = (List<Object>) entry.getValue();
                if (images != null) {
                    List<String> newImages = images.stream()
                            .map(y -> toSFAImage((Map<String, Object>) y)).collect(Collectors.toList());
                    entry.setValue(newImages);
                }
            }
        });
        return entry;
    }

    private static String toSFAImage(Map<String, Object> doc) {
        return doc.get("path") + "." + doc.get("ext");
    }


    protected Tuple<String, Object> convertImageField(Tuple<String, Object> entry, ObjectDescribeExt describeExt) {
        describeExt.getFieldDescribeSilently(entry.getKey())
                .filter(x -> IFieldType.IMAGE.equals(x.getType()) && entry.getValue() != null && entry.getValue() instanceof List).ifPresent(x -> {
            List<String> images = (List<String>) entry.getValue();
            List<Map<String, Object>> converted = Lists.newArrayList();
            for (String image : images) {
                int index = image.lastIndexOf('.');
                if (index > 0) {
                    String path = image.substring(0, index);
                    String ext = image.substring(index + 1);
                    Map<String, Object> imageData = new HashMap<>(2);
                    imageData.put("path", path);
                    imageData.put("ext", ext);
                    converted.add(imageData);
                } else {
                    Map<String, Object> imageData = new HashMap<>(2);
                    imageData.put("path", image);
                    converted.add(imageData);
                }
            }
            entry.setValue(converted);
        });
        return entry;
    }

    protected boolean filterField(String key, ObjectDescribeExt describeExt) {
        Optional<IFieldDescribe> optional = describeExt.getFieldDescribeSilently(key);
        if (optional.isPresent()) {
            return optional.get().isActive();
        } else {
            return key.endsWith("__r") || key.endsWith("__ro") || key.endsWith("ServerTime");
        }
    }

    protected Tuple<String, Object> convertRefField(Tuple<String, Object> entry) {
        //TODO:
        return entry;
    }

    protected Tuple<String, Object> convertRefFieldReverse(Tuple<String, Object> entry) {
        //TODO:
        return entry;
    }

    protected Tuple<String, Object> convertFieldName(Tuple<String, Object> entry) {
        String key = entry.getKey();
        if (fieldMapping.containsKey(key)) {
            entry.setKey(fieldMapping.get(key));
        } else if (key.startsWith("UD")) {
            if (key.endsWith("__c__r") || key.endsWith("__c")) {
                return entry;
            }
            if (key.endsWith("__r")) {
                entry.setKey(StringUtils.replace(key, "__r", "__c__r"));
            } else {
                entry.setKey(key + "__c");
            }
        }
        return entry;
    }

    protected Tuple<String, Object> convertFieldNameReverse(Tuple<String, Object> entry) {
        String key = entry.getKey();
        if (fieldMapping.inverse().containsKey(key)) {
            entry.setKey(fieldMapping.inverse().get(key));
        } else if (key.contains("__c")) {
            entry.setKey(StringUtils.replace(key, "__c", ""));
        }
        return entry;
    }

    protected Tuple<String, Object> convertEmployeeFiled(Tuple<String, Object> entry, ObjectDescribeExt describeExt) {
        if (Objects.isNull(entry.getValue())) {
            return entry;
        }
        describeExt.getFieldDescribeSilently(entry.getKey()).filter(x -> IFieldType.EMPLOYEE.equals(x.getType()))
                .ifPresent(x -> {
                    List<String> value = !"0".equals(entry.getValue().toString()) ? Lists.newArrayList(entry.getValue().toString())
                            : Lists.newArrayList();
                    entry.setValue(value);
                });
        return entry;
    }

    protected Tuple<String, Object> convertDepartmentFiled(Tuple<String, Object> entry, ObjectDescribeExt describeExt) {
        if (Objects.isNull(entry.getValue())) {
            return entry;
        }
        describeExt.getFieldDescribeSilently(entry.getKey())
                .filter(x -> IFieldType.DEPARTMENT.equals(x.getType()))
                .ifPresent(x -> {
                    List<String> value = Strings.isNullOrEmpty(entry.getValue().toString()) ? Lists.newArrayList()
                            : Lists.newArrayList(entry.getValue().toString());
                    entry.setValue(value);
                });
        return entry;
    }

    protected Tuple<String, Object> convertEmployeeFiledReverse(Tuple<String, Object> entry, ObjectDescribeExt describeExt) {
        if (Objects.isNull(entry.getValue()) || !(entry.getValue() instanceof List)) {
            return entry;
        }
        describeExt.getFieldDescribeSilently(entry.getKey()).filter(x -> IFieldType.EMPLOYEE.equals(x.getType()))
                .ifPresent(x -> {
                    List value = (List) entry.getValue();
                    if (CollectionUtils.isNotEmpty(value)) {
                        entry.setValue(value.get(0));
                    } else {
                        entry.setValue(null);
                    }
                });
        return entry;
    }

    protected Tuple<String, Object> convertOutTenantIdFiled(Tuple<String, Object> entry, ObjectDescribeExt describeExt) {
        if (Objects.isNull(entry.getValue())) {
            return entry;
        }
        describeExt.getFieldDescribeSilently(entry.getKey()).filter(x -> DBRecord.OUT_TENANT_ID.equals(x.getApiName()))
                .ifPresent(x -> {
                    if (Strings.isNullOrEmpty(entry.getValue().toString()) || Integer.parseInt(entry.getValue().toString()) == 0) {
                        entry.setValue(null);
                    }
                });
        return entry;
    }

    protected Tuple<String, Object> convertFormulaField(Tuple<String, Object> entry, ObjectDescribeExt describeExt) {
        Object sourceValue = entry.getValue();
        if (Objects.isNull(sourceValue) || !(sourceValue instanceof String)) {
            return entry;
        }
        describeExt.getFieldDescribeSilently(entry.getKey())
                .filter(x -> FieldDescribeExt.of(x).isFormula())
                .ifPresent(x -> {
                    String returnType = ((Formula) x).getReturnType();
                    if (Formula.BOOLEAN.equals(returnType)) {
                        Boolean value = Strings.isNullOrEmpty(sourceValue.toString()) ? null : Boolean.valueOf(sourceValue.toString());
                        entry.setValue(value);
                    } else if (FieldDescribeExt.isDateTypeField(returnType)) {
                        Long value = Strings.isNullOrEmpty(sourceValue.toString()) ? null : new BigDecimal(sourceValue.toString()).longValue();
                        entry.setValue(value);
                    }
                });
        return entry;
    }

}
