package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.RelationType;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.component.*;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.UDOBJ;
import static com.facishare.crm.userdefobj.DefObjConstants.briefFieldListFormObjectMap;
import static com.facishare.paas.appframework.metadata.ComponentExt.NAME_COMPONENT;
import static com.facishare.paas.appframework.metadata.ComponentExt.PAYMENT_DESCRIBE_API_NAME;
import static com.facishare.paas.appframework.metadata.layout.LayoutStructure.API_NAME;

/**
 * Created by liwei on 2019/12/6
 */
@Slf4j
public class LayoutComponents {
    public static List<String> SUPPORT_SUMMERY_CARD_OBJECTS = Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.ACCOUNT_API_NAME);
    public final static Set<String> SUPPORT_ATTACH_OBJECTS = Sets.newHashSet(Utils.ACCOUNT_API_NAME, Utils.MARKETING_EVENT_API_NAME, Utils.CONTRACT_API_NAME, Utils.PRODUCT_API_NAME);

    private static final String BUSINESS_TYPE = "business";

    public static IRelatedObjectList buildAttachComponent() {
        IRelatedObjectList component = new RelatedObjectList();
        component.setName(ComponentExt.ATTACH_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.ANNEX));
        ComponentExt.of(component).setNameI18nKey(I18NKey.ANNEX);
        component.setDefineType(ComponentDefineType.BUSINESS.getType());
        component.setRefObjectApiName(ComponentExt.ATTACH_REF_OBJECT_NAME);
        component.setRelatedListName(ComponentExt.ATTACH_REF_OBJECT_NAME);
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static IAccountHierarchyComponent buildAccountHierarchyComponent() {
        IAccountHierarchyComponent component = new AccountHierarchyComponent();
        component.setDefineType(ComponentDefineType.BUSINESS.getType());
        component.setName(ComponentExt.ACCOUNT_HIERARCHY_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.ACCOUNT_HIERARCHY_COMPONENT));
        ComponentExt.of(component).setNameI18nKey(I18NKey.ACCOUNT_HIERARCHY_COMPONENT);
        component.setRefObjectApiName(ObjectAPINameMapping.Account.getApiName());
        component.setFieldApiName("parent_account_id");
        component.setRelatedListName("account_account_list");
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static IContactRelationComponent buildContactRelationComponent() {
        IContactRelationComponent component = new ContactRelationComponent();
        component.setDefineType(ComponentDefineType.BUSINESS.getType());
        component.setName(ComponentExt.CONTACT_RELATION_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.CONTACT_RELATION_COMPONENT));
        ComponentExt.of(component).setNameI18nKey(I18NKey.CONTACT_RELATION_COMPONENT);
        component.setRefObjectApiName(ObjectAPINameMapping.Contact.getApiName());
        component.setFieldApiName("account_contact_atlas");
        component.setRelatedListName("account_contact_relation");
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static UserListComponent buildUserListComponent() {
        UserListComponent component = new UserListComponent();
        // 设置默认Component的基本属性
        component.setName(ComponentExt.TEAM_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.RELATED_TEAM));
        ComponentExt.of(component).setNameI18nKey(I18NKey.RELATED_TEAM);
        component.setIsShowAvatar(Boolean.TRUE);
        component.set(ComponentExt.DEFINE_TYPE, ComponentDefineType.GENERAL.getType());
        component.setIncludeFields(Lists.newArrayList());
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static SimpleComponent buildHeadInfoComponent() {
        SimpleComponent component = new SimpleComponent();
        component.setName(ComponentExt.HEAD_INFO_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.HEAD_INFO));
        ComponentExt.of(component).setNameI18nKey(I18NKey.HEAD_INFO);
        component.set(ComponentExt.DEFINE_TYPE, ComponentDefineType.GENERAL.getType());
        component.setFieldSections(Lists.newArrayList());
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static RelatedObjectList buildPaymentComponent() {
        RelatedObjectList component = new RelatedObjectList();
        component.setDefineType(ComponentDefineType.BUSINESS.getType());
        component.set("relationType", RelationType.ONE_TO_MANY.getType());
        component.setName(ComponentExt.PAYMENT_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.RECEIVABLES_RECORD));
        ComponentExt.of(component).setNameI18nKey(I18NKey.RECEIVABLES_RECORD);
        component.setRefObjectApiName(PAYMENT_DESCRIBE_API_NAME);
        component.setRelatedListName(PAYMENT_DESCRIBE_API_NAME + "_LIST");
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static RelatedObjectList buildBPMRelatedList() {
        RelatedObjectList component = new RelatedObjectList();
        component.setDefineType(ComponentDefineType.GENERAL.getType());
        component.setName(ComponentExt.BPM_RELATED_NAME);
        component.setHeader(I18N.text(I18NKey.PROCESS_LIST));
        ComponentExt.of(component).setNameI18nKey(I18NKey.PROCESS_LIST);
        component.setRefObjectApiName("BPM");
        component.setRelatedListName("");
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static RelatedObjectList buildApprovalRelatedList() {
        RelatedObjectList component = new RelatedObjectList();
        component.setDefineType(ComponentDefineType.GENERAL.getType());
        component.setName("Approval_related_list");
        component.setHeader(I18N.text(I18NKey.APPROVALFLOW));
        ComponentExt.of(component).setNameI18nKey(I18NKey.APPROVALFLOW);
        component.setRefObjectApiName("Approval");
        component.setRelatedListName("");
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static SimpleComponent buildSummaryCardComponent() {
        SimpleComponent component = new SimpleComponent();
        component.setName(ComponentExt.SUMMARY_CARD_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.SUMMARY_CARD));
        ComponentExt.of(component).setNameI18nKey(I18NKey.SUMMARY_CARD);
        component.set(ComponentExt.DEFINE_TYPE, ComponentDefineType.BUSINESS.getType());
        component.setFieldSections(Lists.newArrayList());
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static TabsComponent buildTabsComponent() {
        TabsComponent component = new TabsComponent();
        component.setName(ComponentExt.TABS_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.CONTAINER_TABS));
        component.setDefineType(ComponentDefineType.CONTAINER.getType());
        component.setTabs(Lists.newArrayList());
        component.setComponents(Lists.newArrayList());
        ComponentExt.of(component).setLimit(0);
        return component;
    }

    public static CommonComponent buildProductAttributeComponent() {
        CommonComponent component = new CommonComponent();
        component.setType("product_attribute");
        component.setName(ComponentExt.PRODUCT_ATTRIBUTE_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.PRODUCT_ATTRIBUTE));
        component.set(ComponentExt.FIELD_API_NAME, "product_attribute");
        component.setDefineType(ComponentDefineType.BUSINESS.getType());
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static CommonComponent buildAccountOperationMapComponent() {
        CommonComponent component = new CommonComponent();
        component.setType("account_operation_map");
        component.setName(ComponentExt.ACCOUNT_OPERATION_MAP_NAME);
        component.setHeader(I18N.text(I18NKey.ACCOUNT_OPERATION_MAP));
        component.setDefineType(ComponentDefineType.BUSINESS.getType());
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static IMultiTableComponent buildDetailObjectComponent(IObjectDescribe detailObject) {
        return buildDetailObjectComponent(detailObject, null);
    }

    public static IMultiTableComponent buildDetailObjectComponent(IObjectDescribe detailObject, UnaryOperator<String> getOriginalApiNameFunction) {
        String describeApiName = detailObject.getApiName();
        MasterDetail masterDetail = ObjectDescribeExt.of(detailObject).getMasterDetailField()
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.MASTER_DETAIL_FIELD_NOT_EXIST, detailObject.getDisplayName())));
        MultiTableComponent multiTableComponent = new MultiTableComponent();
        multiTableComponent.setName(ComponentExt.getDetailComponentName(describeApiName));
        multiTableComponent.setHeader(masterDetail.getTargetRelatedListLabel());
        multiTableComponent.setRefObjectApiName(describeApiName);
        multiTableComponent.setRelatedListName(masterDetail.getTargetRelatedListName());
        multiTableComponent.set(ComponentExt.FIELD_API_NAME, masterDetail.getApiName());
        ComponentExt componentExt = ComponentExt.of(multiTableComponent);
        componentExt.setNameI18nKey(FieldDescribeExt.getReferenceLabelI18NKey(describeApiName, masterDetail.getApiName()));
        componentExt.setLimit(1);
        componentExt.setButtonInfo(Lists.newArrayList());
        String originalApiName = getOriginalApiName(getOriginalApiNameFunction, detailObject);
        componentExt.setOriginalName(originalApiName);
        return multiTableComponent;
    }

    private static String getOriginalApiName(UnaryOperator<String> getOriginalApiNameFunction, IObjectDescribe describe) {
        String originalDescribeApiName = describe.getOriginalDescribeApiName();
        if (Strings.isNullOrEmpty(originalDescribeApiName) && Objects.nonNull(getOriginalApiNameFunction)) {
            return getOriginalApiNameFunction.apply(describe.getApiName());
        }
        return originalDescribeApiName;
    }

    public static IComponent buildShortcutComponent() {
        IComponent component = new CommonComponent();
        component.setType("shortcut");
        component.setName("shortcut");
        component.setHeader("shortcut");
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static IComponent buildMarketingEventPathComponent() {
        IComponent component = new CommonComponent();
        component.setType("marketing_event_path");
        component.setName("marketing_event_path");
        component.setHeader(I18N.text(I18NKey.MARKETING_EVENT_PATH));
        component.set("ref_object_api_name", Utils.MARKETING_EVENT_API_NAME);
        component.set("related_list_name", "marketing_event_list");
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static IComponent buildApprovalComponent(boolean unDeletable) {
        IComponent component = new CommonComponent();
        component.setType(ComponentExt.APPROVAL_COMPONENT);
        component.setName(ComponentExt.APPROVAL_COMPONENT);
        component.setHeader(I18NExt.text(I18NKey.APPROVAL_COMPONENT));
        ComponentExt.of(component).setUnDeletable(unDeletable);
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static IComponent buildStageComponent(boolean unDeletable) {
        IComponent component = new CommonComponent();
        component.setType(ComponentExt.STAGE_COMPONENT);
        component.setName(ComponentExt.STAGE_COMPONENT);
        component.setHeader(I18NExt.text(I18NKey.STAGE_COMPONENT));
        ComponentExt.of(component).setUnDeletable(true);
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static IComponent buildBpmComponent(boolean unDeletable) {
        IComponent component = new CommonComponent();
        component.setType(ComponentExt.BPM_COMPONENT);
        component.setName(ComponentExt.BPM_COMPONENT);
        component.setHeader(I18NExt.text(I18NKey.BPM_COMPONENT));
        ComponentExt.of(component).setUnDeletable(true);
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    public static IComponent buildNameComponent(LayoutExt webLayout) {
        CommonComponent component = new CommonComponent();
        component.setType(NAME_COMPONENT);
        component.setName(NAME_COMPONENT);
        component.setHeader(I18NExt.text(I18NKey.NAME_COMPONENT));
        ComponentExt.of(component).setUnDeletable(false);
        ComponentExt.of(component).setLimit(1);

        Optional<IComponent> headInfoComponent = webLayout.getHeadInfoComponent();
        Map cardLayout = headInfoComponent.map(x -> x.get("card_layout", Map.class)).orElse(null);
        if (Objects.nonNull(cardLayout)) {
            ComponentExt.of(component).setCardLayout(cardLayout);
        }
        return component;
    }

    public static List<IComponent> getFlowComponents(boolean unDeletable) {
        List<IComponent> components = Lists.newArrayList();
        components.add(buildApprovalComponent(unDeletable));
        components.add(buildStageComponent(unDeletable));
        components.add(buildBpmComponent(unDeletable));
        return components;
    }

    public static List<String> getFlowComponentApiNames() {
        return Lists.newArrayList(ComponentExt.APPROVAL_COMPONENT, ComponentExt.STAGE_COMPONENT, ComponentExt.BPM_COMPONENT);
    }

    public static void addTopInfoComponentByLayout(LayoutExt layoutExt, List<IComponent> components,
                                                   List<Map> leftStructureList, ObjectDescribeExt describeExt) {
        SimpleComponent simpleComponent = layoutExt.getTopInfoComponentSilently().orElse(null);
        if (simpleComponent == null) {
            simpleComponent = getSimpleComponentWithoutButtons(describeExt);
        }

        TopInfoComponent newTopInfo = new TopInfoComponent();
        ComponentExt.of(newTopInfo).setLimit(1);
        newTopInfo.setHeader(I18N.text(I18NKey.SUMMARY_INFO));
        newTopInfo.setName(ComponentExt.TOP_INFO_COMPONENT_NAME);
        newTopInfo.setDefineType(ComponentDefineType.GENERAL.getType());
        newTopInfo.setButtons(simpleComponent.getButtons());

        List<IFormField> fieldSections = getTopInfoFieldSection(simpleComponent);
        newTopInfo.setFieldSections(fieldSections);
        components.add(newTopInfo);
        leftStructureList.add(LayoutStructure.convertToMap(ComponentExt.TOP_INFO_COMPONENT_NAME));
    }

    public static SimpleComponent getSimpleComponentWithoutButtons(ObjectDescribeExt describeExt) {
        List<IFormField> formFieldList = Lists.newArrayList();

        String objectDescribeApiName = describeExt.getApiName();
        if (!describeExt.isSFAObject() && !Utils.PRODUCT_API_NAME.equals(objectDescribeApiName)) {
            objectDescribeApiName = UDOBJ;
        }

        //如果在摘要信息的配置信息里面,就放入到briefFormField里面。
        Set<String> briefFiledNames = briefFieldListFormObjectMap.get(objectDescribeApiName);
        if (CollectionUtils.notEmpty(briefFiledNames)) {
            briefFiledNames.forEach(x -> {
                describeExt.getFieldDescribeSilently(x).ifPresent(y -> {
                    FormField simpleFormField = new FormField();
                    simpleFormField.setFieldName(y.getApiName());
                    simpleFormField.setReadOnly(false);
                    simpleFormField.setRequired(y.isRequired());
                    simpleFormField.setRenderType(LayoutExt.getRenderType(describeExt.getApiName(), y.getApiName(), y.getType()));
                    formFieldList.add(simpleFormField);
                });
            });
        }

        List<IFieldSection> fieldSectionList = new ArrayList<>();
        IFieldSection section = new FieldSection();
        section.setName("detail");
        section.setFields(formFieldList);
        fieldSectionList.add(section);

        SimpleComponent simpleComponent = new SimpleComponent();
        simpleComponent.setName(ILayout.TOP_INFO);
        simpleComponent.setHeader(I18N.text(I18NKey.TOP_INFO));
        simpleComponent.setFieldSections(fieldSectionList);

        return simpleComponent;
    }

    public static void addSummaryCardComponent(String objectApiName, List<IComponent> components, List<Map> leftStructureList, LayoutExt layoutExt) {
        if (!SUPPORT_SUMMERY_CARD_OBJECTS.contains(objectApiName)) {
            return;
        }
        //设置了隐藏摘要卡片的无需添加该组件
        if (layoutExt.isComponentHidden(ComponentExt.SUMMARY_CARD_COMPONENT_NAME)) {
            return;
        }
        Optional<IComponent> cardSummary = components.stream().filter(x -> ComponentExt.SUMMARY_CARD_COMPONENT_NAME.equals(x.getName())).findFirst();
        if (cardSummary.isPresent()) {
            leftStructureList.add(LayoutStructure.convertToMap(cardSummary.get().getName()));
        } else {
            SimpleComponent cardSummaryComponent = buildSummaryCardComponent();
            components.add(cardSummaryComponent);
            leftStructureList.add(LayoutStructure.convertToMap(cardSummaryComponent.getName()));
        }
    }

    public static void restoreTopInfo(LayoutExt layoutExt) {
        SimpleComponent simpleComponent = convertToSimpleComponent(layoutExt.getNewTopInfoComponent().orElse(null));
        layoutExt.setTopInfo(simpleComponent);
    }

    public static List<Map> getLeftTabsComponentApiNames(List<String> relatedApiNames, List<String> detailApiNames, List<IComponent> finalGroupComponentList,
                                                         List<String> leftTabsApiNames, String defaultComponent, List<String> leftBottomApiNames) {
        leftTabsApiNames.addAll(relatedApiNames);
        leftTabsApiNames.addAll(detailApiNames);
        leftTabsApiNames.remove(ComponentExt.TEAM_COMPONENT_NAME);
        leftTabsApiNames.remove(ComponentExt.SALE_LOG_COMPONENT_NAME);
        leftTabsApiNames.removeAll(leftBottomApiNames);
        Set<String> componentApiNames = finalGroupComponentList.stream().map(x -> x.getName()).collect(Collectors.toSet());

        List<Map> leftTabsComponentApiNames = Lists.newArrayList();
        //没有隐藏且不是相关团队、跟进动态或需要特殊处理到底部的对象才能作为默认组件
        String defaultTab = leftTabsApiNames.contains(defaultComponent) ? defaultComponent : ComponentExt.DETAIL_INFO_COMPONENT_NAME;
        if (componentApiNames.contains(defaultTab)) {
            leftTabsComponentApiNames.add(LayoutStructure.convertToMap(defaultTab));
        }

        finalGroupComponentList.stream()
                .filter(x -> leftTabsApiNames.contains(x.getName()))
                .filter(x -> !StringUtils.equals(x.getName(), defaultTab))
                .forEach(x -> leftTabsComponentApiNames.add(LayoutStructure.convertToMap(x.getName())));
        return leftTabsComponentApiNames;
    }

    public static List<IFormField> getTopInfoFieldSection(SimpleComponent topInfo) {
        if (CollectionUtils.empty(topInfo.getFieldSections())) {
            return Lists.newArrayList();
        }
        return topInfo.getFieldSections().stream().flatMap(x -> x.getFields().stream()).collect(Collectors.toList());
    }

    public static void restoreComponentOrder(LayoutExt layoutExt) {
        final int startIndex = 3;
        if (layoutExt.isV3Layout()) {
            int index = startIndex;
            List<Map> layoutStructure = (List<Map>) layoutExt.getLayoutStructure().get(LayoutStructure.LAYOUT);
            for (Map row : layoutStructure) {
                List<List<String>> componentsList = (List<List<String>>) row.get(LayoutStructure.COMPONENTS);
                for (List<String> components : componentsList) {
                    index = modifyComponentOrder(layoutExt, components, index);
                }
            }
        } else if (layoutExt.isV2Layout()) {
            List<Map> leftStructureList = LayoutStructure.getLeftStructure(layoutExt);
            List<Map> rightStructureList = LayoutStructure.getRightStructure(layoutExt);
            List<String> rightComponents = rightStructureList.stream()
                    .map(x -> (String) x.get(API_NAME)).collect(Collectors.toList());
            List<String> leftComponents = leftStructureList.stream()
                    .map(x -> (String) x.get(API_NAME)).collect(Collectors.toList());
            int index = modifyComponentOrder(layoutExt, leftComponents, startIndex);
            modifyComponentOrder(layoutExt, rightComponents, index);
        }
        resetComponentsOrderInRightLayout(layoutExt, startIndex);
    }

    //对于跟进动态比较关注的对象需要重新调整下跟进动态和相关团队的展示位置
    private static void resetComponentsOrderInRightLayout(LayoutExt layoutExt, int startIndex) {
        if (!layoutExt.isNewLayout()) {
            return;
        }
        if (!ObjectDescribeExt.isCustomObject(layoutExt.getRefObjectApiName())
                && !AppFrameworkConfig.isSaleLogHighOrderObject(layoutExt.getRefObjectApiName())) {
            return;
        }
        //跟进动态放第一位
        layoutExt.getComponentByApiName(ComponentExt.SALE_LOG_COMPONENT_NAME).ifPresent(x -> {
            if (!isComponentInRightLayout(layoutExt, x.getName())) {
                return;
            }
            x.setOrder(startIndex - 2);
        });
        //相关团队放第二位
        layoutExt.getComponentByApiName(ComponentExt.TEAM_COMPONENT_NAME).ifPresent(x -> {
            if (!isComponentInRightLayout(layoutExt, x.getName())) {
                return;
            }
            x.setOrder(startIndex - 1);
        });
    }

    private static boolean isComponentInRightLayout(LayoutExt layoutExt, String componentName) {
        if (layoutExt.isV2Layout()) {
            return LayoutStructure.getRightStructure(layoutExt).stream()
                    .anyMatch(x -> componentName.equals(x.get(API_NAME)));
        } else if (layoutExt.isV3Layout()) {
            List<Map> layoutStructure = (List<Map>) layoutExt.getLayoutStructure().get(LayoutStructure.LAYOUT);
            if (layoutStructure.size() < 2) {
                return false;
            }
            Map secondRow = layoutStructure.get(1);
            List<List<String>> componentsList = (List<List<String>>) secondRow.get(LayoutStructure.COMPONENTS);
            if (componentsList.size() < 2) {
                return false;
            }
            return componentsList.get(1).contains(componentName);
        }
        return false;
    }

    public static void swapDetailComponent(List<IComponent> components) {
        int index = 0;
        for (int i = 0; i < components.size(); i++) {
            if (components.get(i).getName().equals(ComponentExt.DETAIL_INFO_COMPONENT_NAME)) {
                index = i;
                break;
            }
        }
        if (index > 0) {
            Collections.swap(components, 0, index);
        }
    }

    private static int modifyComponentOrder(LayoutExt layoutExt, List<String> components, int index) {
        if (CollectionUtils.empty(components)) {
            return index;
        }
        for (String apiName : components) {
            Optional<IComponent> componentOptional = layoutExt.getComponentByApiName(apiName);
            if (componentOptional.isPresent()) {
                IComponent component = componentOptional.get();
                component.setOrder(index++);
                //处理容器里的组件
                List<String> children = getChildren(component, layoutExt);
                index = modifyComponentOrder(layoutExt, children, index);
            }
        }
        return index;
    }

    private static List<String> getChildren(IComponent component, LayoutExt layoutExt) {
        List<String> children = Lists.newArrayList();
        if (ComponentExt.of(component).isTabs()) {
            TabsComponent tabs = (TabsComponent) component;
            if (CollectionUtils.notEmpty(tabs.getComponents())) {
                tabs.getComponents().forEach(x -> children.addAll(CollectionUtils.nullToEmpty(x)));
            } else if (layoutExt.isV2Layout()) {
                LayoutStructure.getLeftStructure(layoutExt).stream()
                        .filter(x -> component.getName().equals(x.get(API_NAME)))
                        .findFirst()
                        .filter(x -> Objects.nonNull(x.get(LayoutStructure.CHILDREN)))
                        .ifPresent(x -> children.addAll(((List<Map>) x.get(LayoutStructure.CHILDREN)).stream()
                                .map(y -> (String) y.get(API_NAME)).collect(Collectors.toList())));
            }
        } else if (ComponentExt.of(component).isTabComponent()) {
            ITabComponent tabComponent = (ITabComponent) component;
            children.addAll(tabComponent.getComponents());
        } else if (ComponentExt.of(component).isNavigation()) {
            INavigationComponent navigation = (INavigationComponent) component;
            children.addAll(navigation.getComponents());
        } else if (ComponentExt.of(component).isGrid()) {
            IGridComponent grid = (IGridComponent) component;
            GridComponentExt.of(grid).getComponents().forEach(x -> children.addAll(x));
        }
        return children;
    }

    public static IFrameComponent buildFrameComponent() {
        IFrameComponent component = new FrameComponent();
        Map<String, Object> defaultStyle = Maps.newHashMap();
        defaultStyle.put("height", "500px");

        component.setName(ComponentExt.FRAME_COMPONENT_NAME);
        component.setHeader(I18N.text(I18NKey.FRAME_COMPONENT));
        component.setDefineType(ComponentDefineType.GENERAL.getType());
        component.setStyle(defaultStyle);
        ComponentExt.of(component).setLimit(0);
        return component;
    }

    public static SimpleComponent convertToSimpleComponent(ITopInfoComponent topInfoComponent) {
        SimpleComponent simpleComponent = new SimpleComponent();
        simpleComponent.setName(ILayout.TOP_INFO);
        simpleComponent.setHeader(I18N.text(I18NKey.TOP_INFO));
        if (topInfoComponent == null) {
            simpleComponent.setIsHidden(true);
            simpleComponent.setFieldSections(Lists.newArrayList());
        } else {
            List<IFormField> formFieldList = topInfoComponent.getFieldSections();
            List<IFieldSection> fieldSectionList = Lists.newArrayList();
            IFieldSection section = new FieldSection();
            section.setName("detail");
            section.setFields(formFieldList);
            fieldSectionList.add(section);

            simpleComponent.setOrder(0);
            simpleComponent.setIsHidden(false);
            simpleComponent.setFieldSections(fieldSectionList);
        }
        return simpleComponent;
    }

    public static void replaceOldAttachComponent(LayoutExt layoutExt, List<IComponent> components) {
        replaceOldAttachComponent(layoutExt.getRefObjectApiName(), layoutExt, components);
    }

    public static void replaceOldAttachComponent(String objectApiNAme, LayoutExt layoutExt, List<IComponent> components) {
        if (!SUPPORT_ATTACH_OBJECTS.contains(objectApiNAme)) {
            return;
        }
        Optional<IComponent> oldAttach = components.stream().filter(x -> ComponentExt.of(x).isOldAttachComponent()).findFirst();
        Optional<IComponent> newAttach = components.stream().filter(x -> ComponentExt.of(x).isAttachComponent()).findFirst();
        boolean needFillNewAttach = !newAttach.isPresent() && !layoutExt.isComponentHidden(ComponentExt.ATTACH_COMPONENT_NAME);
        if (oldAttach.isPresent()) {
            components.removeIf(x -> oldAttach.get().getName().equals(x.getName()));
            boolean isOldAttachHidden = layoutExt.isComponentHidden(oldAttach.get().getName());
            if (isOldAttachHidden && newAttach.isPresent()) {
                components.removeIf(x -> newAttach.get().getName().equals(x.getName()));
            }
            needFillNewAttach &= !isOldAttachHidden;
        }
        if (needFillNewAttach) {
            components.add(buildAttachComponent());
        }
    }

    public static void addOtherComponents(List<Map> leftStructureList, List<IComponent> components, List<Map> relatedList) {
        List<Object> componentNames = leftStructureList.stream()
                .map(x -> x.get(API_NAME))
                .map(String::valueOf)
                .collect(Collectors.toList());
        componentNames.addAll(relatedList.stream().map(x -> x.get(API_NAME)).collect(Collectors.toList()));
        List<IComponent> iComponents = components.stream()
                .filter(x -> !componentNames.contains(x.getName()))
                .filter(x -> AppFrameworkConfig.isOldLayoutConvertV3AddComponents(x.getName()))
                .collect(Collectors.toList());
        iComponents.forEach(x -> leftStructureList.add(LayoutStructure.convertToMap(x.getName())));
    }
}
