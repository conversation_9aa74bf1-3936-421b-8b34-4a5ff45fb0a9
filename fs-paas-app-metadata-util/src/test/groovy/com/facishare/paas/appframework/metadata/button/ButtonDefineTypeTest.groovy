package com.facishare.paas.appframework.metadata.button

import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.impl.UdefButton
import spock.lang.Specification

/**
 * create by <PERSON><PERSON><PERSON> on 2020/12/18
 */
class ButtonDefineTypeTest extends Specification {
    def "test"() {
        given:
        IUdefButton udefButton1 = new UdefButton()
        udefButton1.setApiName("buton1")
        udefButton1.setDefineType(ButtonDefineType.CUSTOM.getId())

        IUdefButton udefButton2 = new UdefButton()
        udefButton2.setApiName("buton2")
        udefButton2.setDefineType(ButtonDefineType.SYSTEM.getId())

        IUdefButton udefButton3 = new UdefButton()
        udefButton3.setApiName("buton3")

        IUdefButton udefButton4 = new UdefButton()
        udefButton4.setApiName("buton4")
        udefButton4.setDefineType(ButtonDefineType.SYSTEM.getId())

        IUdefButton udefButton5 = new UdefButton()
        udefButton5.setApiName("buton5")
        udefButton5.setDefineType(ButtonDefineType.CUSTOM.getId())
        when:
        List<IUdefButton> targetButtonList = [udefButton1, udefButton2, udefButton3, udefButton4, udefButton5]
        targetButtonList.sort Comparator.comparing { it -> ButtonDefineType.of(it.getDefineType()) }
        then:
        println targetButtonList
        targetButtonList == [udefButton2, udefButton4, udefButton1, udefButton3, udefButton5]
    }
}
