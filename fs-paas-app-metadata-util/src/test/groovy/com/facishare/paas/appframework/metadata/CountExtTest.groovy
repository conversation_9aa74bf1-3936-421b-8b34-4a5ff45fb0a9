package com.facishare.paas.appframework.metadata

import com.facishare.paas.metadata.api.describe.Count
import com.facishare.paas.multiRegion.MultiRegionContext
import com.facishare.paas.multiRegion.MultiRegionContextHolder
import spock.lang.Specification

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/7/24
 */
class CountExtTest extends Specification {
    def "FormatResult"() {
        given:
        Count count = Mock(Count)
        count.getDecimalPlaces() >> decimalPlaces
        count.getRoundMode() >> roundingMode
        def countExt = CountExt.of(count)
        when:
        def result = countExt.formatResult(value)
        println("value:" + value + ", roundingMode:" + roundingMode + ", result:" + result)
        then:
        result == expect
//        1 == 1
        where:
        value  | decimalPlaces | roundingMode || expect
        1.567  | 2             | 4            || "1.57"
        1.567  | 2             | 0            || "1.57"
        1.567  | 2             | 1            || "1.56"

        1.564  | 2             | 4            || "1.56"
        1.564  | 2             | 0            || "1.57"
        1.564  | 2             | 1            || "1.56"

        1.562  | 2             | 4            || "1.56"
        1.562  | 2             | 0            || "1.57"
        1.562  | 2             | 1            || "1.56"

        -1.567 | 2             | 4            || "-1.57"
        -1.567 | 2             | 0            || "-1.57"
        -1.567 | 2             | 1            || "-1.56"

        -1.562 | 2             | 4            || "-1.56"
        -1.562 | 2             | 0            || "-1.57"
        -1.562 | 2             | 1            || "-1.56"

        -1.564 | 2             | 4            || "-1.56"
        -1.564 | 2             | 0            || "-1.57"
        -1.564 | 2             | 1            || "-1.56"
    }


    def "fillMultiRegionWithCurrency test"() {
        given:
        def count = Mock(Count)

        def multiRegionContext = Mock(MultiRegionContext)
        def multiRegionContextHolder = Mock(MultiRegionContextHolder)
        multiRegionContextHolder.getMultiRegionContext() >> multiRegionContext
        multiRegionContext.getUserRegion() >> "am_ET"
        count.getDecimalPlaces() >> decimalPlaces
        count.getRoundMode() >> roundingMode
        count.getReturnType() >> "currency"
        def countExt = CountExt.of(count)
        def objectData = ObjectDataExt.of(["id": "sdjasoidjsaiodjiaoji"]).getObjectData()
        when:
        def result = countExt.fillMultiRegionWithCurrency(value, objectData)
        println("value:" + value + ", roundingMode:" + roundingMode + ", result:" + result)
        then:
        result == expect
//        1 == 1
        where:
        value  | decimalPlaces | roundingMode || expect
        100.01 | 2             | 4            || []
    }
}
