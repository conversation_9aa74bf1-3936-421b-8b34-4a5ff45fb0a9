package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * AggregateValue的JUnit 5测试类
 * 测试聚合值包装类功能
 */
class AggregateValueJUnit5Test {

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("of - 工厂方法创建")
    void testOf() {
        IObjectData data = new ObjectData();
        data.set("test", "value");
        
        AggregateValue aggregateValue = AggregateValue.of(data);
        
        assertNotNull(aggregateValue);
        assertSame(data, aggregateValue.getData());
        assertEquals("value", aggregateValue.get("test"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试from工厂方法 - 非Max/Min方式
     */
    @Test
    @DisplayName("from - 工厂方法创建（非Max/Min方式）")
    void testFrom_NonMaxMinWay() {
        // 准备聚合规则
        AggregateRule aggregateRule = mock(AggregateRule.class);
        when(aggregateRule.getId()).thenReturn("rule-123");
        when(aggregateRule.getTenantId()).thenReturn("tenant-456");
        when(aggregateRule.getDimension()).thenReturn("category");
        when(aggregateRule.buildAggFieldName()).thenReturn("sum_amount");
        when(aggregateRule.isMaxOrMinWay()).thenReturn(false);

        // 准备聚合数据
        IObjectData aggregateData = new ObjectData();
        aggregateData.set("category", "Electronics");
        aggregateData.set("sum_amount", 1500.0);
        aggregateData.set(AggregateRule.COUNT_VALUE_KEY, 10);

        long aggregateDate = 1640995200000L; // 2022-01-01

        AggregateValue result = AggregateValue.from(aggregateDate, aggregateRule, aggregateData);

        assertNotNull(result);
        assertFalse(result.isDeleted());
        assertEquals("tenant-456", result.getTenantId());
        assertEquals("rule-123", result.getAggregateRuleId());
        assertEquals(1640995200000L, result.getAggregateDate());
        assertEquals("Electronics", result.getAggregateDimension());
        assertEquals(1500.0, result.get(AggregateValue.AGGREGATE_VALUE));
        assertEquals(10, result.get(AggregateValue.AGGREGATE_COUNT_VALUE));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试from工厂方法 - Max/Min方式
     */
    @Test
    @DisplayName("from - 工厂方法创建（Max/Min方式）")
    void testFrom_MaxMinWay() {
        // 准备聚合规则
        AggregateRule aggregateRule = mock(AggregateRule.class);
        when(aggregateRule.getId()).thenReturn("rule-123");
        when(aggregateRule.getTenantId()).thenReturn("tenant-456");
        when(aggregateRule.getDimension()).thenReturn("category");
        when(aggregateRule.buildAggFieldName()).thenReturn("max_price");
        when(aggregateRule.isMaxOrMinWay()).thenReturn(true);

        // 准备聚合数据
        IObjectData aggregateData = new ObjectData();
        aggregateData.set("category", "Electronics");
        aggregateData.set("max_price", null); // null值在Max/Min方式下保持null
        aggregateData.set(AggregateRule.COUNT_VALUE_KEY, 5);

        long aggregateDate = 1640995200000L;

        AggregateValue result = AggregateValue.from(aggregateDate, aggregateRule, aggregateData);

        assertNotNull(result);
        assertEquals("tenant-456", result.getTenantId());
        assertEquals("rule-123", result.getAggregateRuleId());
        assertEquals(1640995200000L, result.getAggregateDate());
        assertEquals("Electronics", result.getAggregateDimension());
        assertNull(result.get(AggregateValue.AGGREGATE_VALUE)); // Max/Min方式下null值保持null
        assertEquals(5, result.get(AggregateValue.AGGREGATE_COUNT_VALUE));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试from工厂方法 - 空值处理
     */
    @Test
    @DisplayName("from - 空值处理")
    void testFrom_EmptyValueHandling() {
        // 准备聚合规则
        AggregateRule aggregateRule = mock(AggregateRule.class);
        when(aggregateRule.getId()).thenReturn("rule-123");
        when(aggregateRule.getTenantId()).thenReturn("tenant-456");
        when(aggregateRule.getDimension()).thenReturn("category");
        when(aggregateRule.buildAggFieldName()).thenReturn("sum_amount");
        when(aggregateRule.isMaxOrMinWay()).thenReturn(false);

        // 准备聚合数据 - 包含空值
        IObjectData aggregateData = new ObjectData();
        aggregateData.set("category", "Electronics");
        aggregateData.set("sum_amount", null); // 空值
        // 不设置COUNT_VALUE_KEY，测试空值处理

        long aggregateDate = 1640995200000L;

        AggregateValue result = AggregateValue.from(aggregateDate, aggregateRule, aggregateData);

        assertNotNull(result);
        assertEquals(0, result.get(AggregateValue.AGGREGATE_VALUE)); // 非Max/Min方式下空值转为0
        assertEquals(0, result.get(AggregateValue.AGGREGATE_COUNT_VALUE)); // 空值转为0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Getter和Setter方法
     */
    @Test
    @DisplayName("Getter和Setter方法")
    void testGettersAndSetters() {
        IObjectData data = new ObjectData();
        AggregateValue aggregateValue = AggregateValue.of(data);

        // 测试AggregateRuleId
        aggregateValue.setAggregateRuleId("rule-123");
        assertEquals("rule-123", aggregateValue.getAggregateRuleId());

        // 测试AggregateDate
        long testDate = 1640995200000L;
        aggregateValue.setAggregateDate(testDate);
        assertEquals(testDate, aggregateValue.getAggregateDate());

        // 测试AggregateDimension
        aggregateValue.setAggregateDimension("Electronics");
        assertEquals("Electronics", aggregateValue.getAggregateDimension());

        // 测试AggregateValue
        aggregateValue.setAggregateValue(1500.0);
        assertEquals(1500.0, aggregateValue.get(AggregateValue.AGGREGATE_VALUE));

        // 测试AggregateCountValue
        aggregateValue.setAggregateCountValue(10);
        assertEquals(10, aggregateValue.get(AggregateValue.AGGREGATE_COUNT_VALUE));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试keyEqual方法 - 相等情况
     */
    @Test
    @DisplayName("keyEqual - 相等情况")
    void testKeyEqual_Equal() {
        // 创建第一个AggregateValue
        IObjectData data1 = new ObjectData();
        data1.setTenantId("tenant-123");
        AggregateValue value1 = AggregateValue.of(data1);
        value1.setAggregateRuleId("rule-456");
        value1.setAggregateDimension("Electronics");
        value1.setAggregateDate(1640995200000L);

        // 创建第二个AggregateValue（相同的key）
        IObjectData data2 = new ObjectData();
        data2.setTenantId("tenant-123");
        AggregateValue value2 = AggregateValue.of(data2);
        value2.setAggregateRuleId("rule-456");
        value2.setAggregateDimension("Electronics");
        value2.setAggregateDate(1640995200000L);

        assertTrue(value1.keyEqual(value2));
        assertTrue(value2.keyEqual(value1));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试keyEqual方法 - 不同TenantId
     */
    @Test
    @DisplayName("keyEqual - 不同TenantId")
    void testKeyEqual_DifferentTenantId() {
        // 创建第一个AggregateValue
        IObjectData data1 = new ObjectData();
        data1.setTenantId("tenant-123");
        AggregateValue value1 = AggregateValue.of(data1);
        value1.setAggregateRuleId("rule-456");
        value1.setAggregateDimension("Electronics");
        value1.setAggregateDate(1640995200000L);

        // 创建第二个AggregateValue（不同的TenantId）
        IObjectData data2 = new ObjectData();
        data2.setTenantId("tenant-789");
        AggregateValue value2 = AggregateValue.of(data2);
        value2.setAggregateRuleId("rule-456");
        value2.setAggregateDimension("Electronics");
        value2.setAggregateDate(1640995200000L);

        assertFalse(value1.keyEqual(value2));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试keyEqual方法 - 不同AggregateRuleId
     */
    @Test
    @DisplayName("keyEqual - 不同AggregateRuleId")
    void testKeyEqual_DifferentAggregateRuleId() {
        // 创建第一个AggregateValue
        IObjectData data1 = new ObjectData();
        data1.setTenantId("tenant-123");
        AggregateValue value1 = AggregateValue.of(data1);
        value1.setAggregateRuleId("rule-456");
        value1.setAggregateDimension("Electronics");
        value1.setAggregateDate(1640995200000L);

        // 创建第二个AggregateValue（不同的AggregateRuleId）
        IObjectData data2 = new ObjectData();
        data2.setTenantId("tenant-123");
        AggregateValue value2 = AggregateValue.of(data2);
        value2.setAggregateRuleId("rule-789");
        value2.setAggregateDimension("Electronics");
        value2.setAggregateDate(1640995200000L);

        assertFalse(value1.keyEqual(value2));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试keyEqual方法 - 不同AggregateDimension
     */
    @Test
    @DisplayName("keyEqual - 不同AggregateDimension")
    void testKeyEqual_DifferentAggregateDimension() {
        // 创建第一个AggregateValue
        IObjectData data1 = new ObjectData();
        data1.setTenantId("tenant-123");
        AggregateValue value1 = AggregateValue.of(data1);
        value1.setAggregateRuleId("rule-456");
        value1.setAggregateDimension("Electronics");
        value1.setAggregateDate(1640995200000L);

        // 创建第二个AggregateValue（不同的AggregateDimension）
        IObjectData data2 = new ObjectData();
        data2.setTenantId("tenant-123");
        AggregateValue value2 = AggregateValue.of(data2);
        value2.setAggregateRuleId("rule-456");
        value2.setAggregateDimension("Books");
        value2.setAggregateDate(1640995200000L);

        assertFalse(value1.keyEqual(value2));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试keyEqual方法 - 不同AggregateDate
     */
    @Test
    @DisplayName("keyEqual - 不同AggregateDate")
    void testKeyEqual_DifferentAggregateDate() {
        // 创建第一个AggregateValue
        IObjectData data1 = new ObjectData();
        data1.setTenantId("tenant-123");
        AggregateValue value1 = AggregateValue.of(data1);
        value1.setAggregateRuleId("rule-456");
        value1.setAggregateDimension("Electronics");
        value1.setAggregateDate(1640995200000L);

        // 创建第二个AggregateValue（不同的AggregateDate）
        IObjectData data2 = new ObjectData();
        data2.setTenantId("tenant-123");
        AggregateValue value2 = AggregateValue.of(data2);
        value2.setAggregateRuleId("rule-456");
        value2.setAggregateDimension("Electronics");
        value2.setAggregateDate(1641081600000L); // 不同日期

        assertFalse(value1.keyEqual(value2));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试常量值
     */
    @Test
    @DisplayName("常量值验证")
    void testConstants() {
        assertEquals("aggregate_rule_id", AggregateValue.AGGREGATE_RULE_ID);
        assertEquals("aggregate_date", AggregateValue.AGGREGATE_DATE);
        assertEquals("aggregate_dimension", AggregateValue.AGGREGATE_DIMENSION);
        assertEquals("aggregate_value", AggregateValue.AGGREGATE_VALUE);
        assertEquals("aggregate_count_value", AggregateValue.AGGREGATE_COUNT_VALUE);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托功能
     */
    @Test
    @DisplayName("委托功能测试")
    void testDelegateFeatures() {
        IObjectData data = new ObjectData();
        data.setId("test-id");
        data.setTenantId("test-tenant");
        data.set("custom_field", "custom_value");
        
        AggregateValue aggregateValue = AggregateValue.of(data);
        
        // 测试委托的方法
        assertEquals("test-id", aggregateValue.getId());
        assertEquals("test-tenant", aggregateValue.getTenantId());
        assertEquals("custom_value", aggregateValue.get("custom_field"));
        
        // 测试设置值
        aggregateValue.set("new_field", "new_value");
        assertEquals("new_value", aggregateValue.get("new_field"));
        assertEquals("new_value", data.get("new_field")); // 验证委托生效
    }
}
