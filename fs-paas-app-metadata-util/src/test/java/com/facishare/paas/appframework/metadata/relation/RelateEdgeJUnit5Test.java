package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.metadata.relation.RelateEdge.RelateEdgeNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Set;
import java.util.function.Predicate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RelateEdge的JUnit 5测试类
 * 测试关联边类功能
 */
class RelateEdgeJUnit5Test {

    private RelateEdge relateEdge;
    private RelateEdgeNode testNode1;
    private RelateEdgeNode testNode2;
    private RelateEdgeNode testNode3;

    @BeforeEach
    void setUp() {
        relateEdge = new RelateEdge();
        testNode1 = RelateEdgeNode.of(RelateType.M2D, "master_field");
        testNode2 = RelateEdgeNode.of(RelateType.L2R, "lookup_field");
        testNode3 = RelateEdgeNode.of(RelateType.S2S, "self_field");
    }

    /**
     * 测试内容描述：测试默认构造函数
     */
    @Test
    @DisplayName("构造函数 - 默认构造函数")
    void testDefaultConstructor() {
        // Act: 创建RelateEdge实例
        RelateEdge result = new RelateEdge();
        
        // Assert: 验证默认构造函数
        assertNotNull(result);
        Set<RelateEdgeNode> nodes = result.getRelateEdgeNodes();
        assertNotNull(nodes);
        assertTrue(nodes.isEmpty());
    }

    /**
     * 测试内容描述：测试of静态方法
     */
    @Test
    @DisplayName("静态方法 - of方法")
    void testOf() {
        // Arrange: 准备测试节点
        RelateEdgeNode testNode = RelateEdgeNode.of(RelateType.M2D, "test_field");
        
        // Act: 执行of方法
        RelateEdge result = RelateEdge.of(testNode);
        
        // Assert: 验证结果
        assertNotNull(result);
        Set<RelateEdgeNode> nodes = result.getRelateEdgeNodes();
        assertEquals(1, nodes.size());
        assertTrue(nodes.contains(testNode));
    }

    /**
     * 测试内容描述：测试add方法
     */
    @Test
    @DisplayName("操作方法 - add方法")
    void testAdd() {
        // Act: 添加节点
        RelateEdge result = relateEdge.add(testNode1);
        
        // Assert: 验证添加结果
        assertSame(relateEdge, result); // 返回自身，支持链式调用
        Set<RelateEdgeNode> nodes = relateEdge.getRelateEdgeNodes();
        assertEquals(1, nodes.size());
        assertTrue(nodes.contains(testNode1));
        
        // 继续添加节点
        relateEdge.add(testNode2);
        nodes = relateEdge.getRelateEdgeNodes();
        assertEquals(2, nodes.size());
        assertTrue(nodes.contains(testNode1));
        assertTrue(nodes.contains(testNode2));
    }

    /**
     * 测试内容描述：测试add方法 - 重复节点
     */
    @Test
    @DisplayName("边界条件 - add方法重复节点")
    void testAdd_DuplicateNode() {
        // Act: 添加相同节点两次
        relateEdge.add(testNode1);
        relateEdge.add(testNode1);
        
        // Assert: 验证Set特性，不会重复添加
        Set<RelateEdgeNode> nodes = relateEdge.getRelateEdgeNodes();
        assertEquals(1, nodes.size());
        assertTrue(nodes.contains(testNode1));
    }

    /**
     * 测试内容描述：测试getRelateEdgeNodes方法
     */
    @Test
    @DisplayName("访问器方法 - getRelateEdgeNodes方法")
    void testGetRelateEdgeNodes() {
        // Arrange: 添加测试节点
        relateEdge.add(testNode1);
        relateEdge.add(testNode2);
        
        // Act: 获取节点集合
        Set<RelateEdgeNode> result = relateEdge.getRelateEdgeNodes();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(testNode1));
        assertTrue(result.contains(testNode2));
        
        // 验证返回的是不可变集合
        assertThrows(UnsupportedOperationException.class, () -> {
            result.add(testNode3);
        });
    }

    /**
     * 测试内容描述：测试exclude方法
     */
    @Test
    @DisplayName("过滤方法 - exclude方法")
    void testExclude() {
        // Arrange: 添加测试节点
        relateEdge.add(testNode1); // M2D
        relateEdge.add(testNode2); // L2R
        relateEdge.add(testNode3); // S2S
        
        // Act: 排除M2D类型的节点
        Predicate<RelateEdgeNode> filter = node -> node.getRelateType() == RelateType.M2D;
        RelateEdge result = relateEdge.exclude(filter);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertNotSame(relateEdge, result); // 返回新实例
        
        Set<RelateEdgeNode> resultNodes = result.getRelateEdgeNodes();
        assertEquals(2, resultNodes.size());
        assertFalse(resultNodes.contains(testNode1)); // M2D节点被排除
        assertTrue(resultNodes.contains(testNode2));  // L2R节点保留
        assertTrue(resultNodes.contains(testNode3));  // S2S节点保留
        
        // 验证原对象未被修改
        Set<RelateEdgeNode> originalNodes = relateEdge.getRelateEdgeNodes();
        assertEquals(3, originalNodes.size());
    }

    /**
     * 测试内容描述：测试exclude方法 - 空过滤器
     */
    @Test
    @DisplayName("边界条件 - exclude方法空过滤器")
    void testExclude_EmptyFilter() {
        // Arrange: 添加测试节点
        relateEdge.add(testNode1);
        relateEdge.add(testNode2);
        
        // Act: 使用永远返回false的过滤器（不排除任何节点）
        Predicate<RelateEdgeNode> filter = node -> false;
        RelateEdge result = relateEdge.exclude(filter);
        
        // Assert: 验证所有节点都保留
        Set<RelateEdgeNode> resultNodes = result.getRelateEdgeNodes();
        assertEquals(2, resultNodes.size());
        assertTrue(resultNodes.contains(testNode1));
        assertTrue(resultNodes.contains(testNode2));
    }

    /**
     * 测试内容描述：测试exclude方法 - 全部过滤
     */
    @Test
    @DisplayName("边界条件 - exclude方法全部过滤")
    void testExclude_FilterAll() {
        // Arrange: 添加测试节点
        relateEdge.add(testNode1);
        relateEdge.add(testNode2);
        
        // Act: 使用永远返回true的过滤器（排除所有节点）
        Predicate<RelateEdgeNode> filter = node -> true;
        RelateEdge result = relateEdge.exclude(filter);
        
        // Assert: 验证所有节点都被排除
        Set<RelateEdgeNode> resultNodes = result.getRelateEdgeNodes();
        assertTrue(resultNodes.isEmpty());
    }

    /**
     * 测试内容描述：测试allMatchRelateType方法
     */
    @Test
    @DisplayName("匹配方法 - allMatchRelateType方法")
    void testAllMatchRelateType() {
        // Arrange: 添加相同类型的节点
        RelateEdgeNode node1 = RelateEdgeNode.of(RelateType.M2D, "field1");
        RelateEdgeNode node2 = RelateEdgeNode.of(RelateType.M2D, "field2");
        relateEdge.add(node1);
        relateEdge.add(node2);
        
        // Act & Assert: 测试全匹配
        assertTrue(relateEdge.allMatchRelateType(RelateType.M2D));
        assertFalse(relateEdge.allMatchRelateType(RelateType.L2R));
        
        // 添加不同类型的节点
        relateEdge.add(testNode2); // L2R类型
        assertFalse(relateEdge.allMatchRelateType(RelateType.M2D));
        assertFalse(relateEdge.allMatchRelateType(RelateType.L2R));
    }

    /**
     * 测试内容描述：测试allMatchRelateType方法 - 空集合
     */
    @Test
    @DisplayName("边界条件 - allMatchRelateType方法空集合")
    void testAllMatchRelateType_EmptySet() {
        // Act & Assert: 空集合应该返回true（空集合满足所有条件）
        assertTrue(relateEdge.allMatchRelateType(RelateType.M2D));
        assertTrue(relateEdge.allMatchRelateType(RelateType.L2R));
    }

    /**
     * 测试内容描述：测试anyMatchRelateType方法
     */
    @Test
    @DisplayName("匹配方法 - anyMatchRelateType方法")
    void testAnyMatchRelateType() {
        // Arrange: 添加不同类型的节点
        relateEdge.add(testNode1); // M2D
        relateEdge.add(testNode2); // L2R
        relateEdge.add(testNode3); // S2S
        
        // Act & Assert: 测试任意匹配
        assertTrue(relateEdge.anyMatchRelateType(RelateType.M2D));
        assertTrue(relateEdge.anyMatchRelateType(RelateType.L2R));
        assertTrue(relateEdge.anyMatchRelateType(RelateType.S2S));
        assertFalse(relateEdge.anyMatchRelateType(RelateType.D2M));
        assertFalse(relateEdge.anyMatchRelateType(RelateType.UNKNOWN));
    }

    /**
     * 测试内容描述：测试anyMatchRelateType方法 - 空集合
     */
    @Test
    @DisplayName("边界条件 - anyMatchRelateType方法空集合")
    void testAnyMatchRelateType_EmptySet() {
        // Act & Assert: 空集合应该返回false（没有任何元素匹配）
        assertFalse(relateEdge.anyMatchRelateType(RelateType.M2D));
        assertFalse(relateEdge.anyMatchRelateType(RelateType.L2R));
    }

    /**
     * 测试内容描述：测试equals和hashCode方法
     */
    @Test
    @DisplayName("对象比较 - equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Arrange: 创建相同内容的RelateEdge
        RelateEdge edge1 = new RelateEdge();
        RelateEdge edge2 = new RelateEdge();
        
        edge1.add(testNode1);
        edge1.add(testNode2);
        
        edge2.add(testNode1);
        edge2.add(testNode2);
        
        // Act & Assert: 测试equals方法
        assertEquals(edge1, edge2);
        assertEquals(edge1.hashCode(), edge2.hashCode());
        
        // 测试不相等的情况
        RelateEdge edge3 = new RelateEdge();
        edge3.add(testNode1);
        edge3.add(testNode3);
        
        assertNotEquals(edge1, edge3);
        
        // 测试与null的比较
        assertNotEquals(edge1, null);
        
        // 测试与其他类型对象的比较
        assertNotEquals(edge1, "not a RelateEdge");
    }

    /**
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // Arrange: 添加测试节点
        relateEdge.add(testNode1);
        
        // Act: 执行toString方法
        String result = relateEdge.toString();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
        assertTrue(result.contains("RelateEdge"));
    }

    /**
     * 测试内容描述：测试业务场景 - 关联关系构建
     */
    @Test
    @DisplayName("业务场景 - 关联关系构建")
    void testRelationshipBuilding() {
        // Arrange: 模拟业务场景 - 构建对象间的关联关系
        RelateEdgeNode masterToDetail = RelateEdgeNode.of(RelateType.M2D, "detail_records");
        RelateEdgeNode lookupToRelated = RelateEdgeNode.of(RelateType.L2R, "account_id");
        RelateEdgeNode selfReference = RelateEdgeNode.of(RelateType.S2S, "parent_id");
        
        // Act: 构建关联边
        RelateEdge businessEdge = new RelateEdge()
            .add(masterToDetail)
            .add(lookupToRelated)
            .add(selfReference);
        
        // Assert: 验证业务关联关系
        Set<RelateEdgeNode> nodes = businessEdge.getRelateEdgeNodes();
        assertEquals(3, nodes.size());
        
        // 验证包含主从关系
        assertTrue(businessEdge.anyMatchRelateType(RelateType.M2D));
        // 验证包含查找关系
        assertTrue(businessEdge.anyMatchRelateType(RelateType.L2R));
        // 验证包含自关联
        assertTrue(businessEdge.anyMatchRelateType(RelateType.S2S));
    }

    /**
     * 测试内容描述：测试业务场景 - 关联关系过滤
     */
    @Test
    @DisplayName("业务场景 - 关联关系过滤")
    void testRelationshipFiltering() {
        // Arrange: 构建复杂的关联关系
        relateEdge.add(RelateEdgeNode.of(RelateType.M2D, "orders"));
        relateEdge.add(RelateEdgeNode.of(RelateType.L2R, "account"));
        relateEdge.add(RelateEdgeNode.of(RelateType.R2P, "owner"));
        relateEdge.add(RelateEdgeNode.of(RelateType.S2S, "parent"));
        
        // Act: 过滤出只包含数据关联的边（排除人员和部门关联）
        RelateEdge dataOnlyEdge = relateEdge.exclude(node -> 
            node.getRelateType() == RelateType.R2P || 
            node.getRelateType() == RelateType.R2O);
        
        // Assert: 验证过滤结果
        Set<RelateEdgeNode> dataNodes = dataOnlyEdge.getRelateEdgeNodes();
        assertEquals(3, dataNodes.size());
        assertTrue(dataOnlyEdge.anyMatchRelateType(RelateType.M2D));
        assertTrue(dataOnlyEdge.anyMatchRelateType(RelateType.L2R));
        assertTrue(dataOnlyEdge.anyMatchRelateType(RelateType.S2S));
        assertFalse(dataOnlyEdge.anyMatchRelateType(RelateType.R2P));
    }

    /**
     * 测试内容描述：测试业务场景 - 关联类型检查
     */
    @Test
    @DisplayName("业务场景 - 关联类型检查")
    void testRelationshipTypeChecking() {
        // Arrange: 创建只包含主从关系的边
        RelateEdge masterDetailEdge = new RelateEdge()
            .add(RelateEdgeNode.of(RelateType.M2D, "line_items"))
            .add(RelateEdgeNode.of(RelateType.M2D, "attachments"));
        
        // Act & Assert: 验证关联类型检查
        assertTrue(masterDetailEdge.allMatchRelateType(RelateType.M2D));
        assertFalse(masterDetailEdge.allMatchRelateType(RelateType.L2R));
        
        // 验证是否为纯主从关系
        assertTrue(isPureMasterDetailRelation(masterDetailEdge));
        assertFalse(isPureMasterDetailRelation(relateEdge));
    }
    
    private boolean isPureMasterDetailRelation(RelateEdge edge) {
        return !edge.getRelateEdgeNodes().isEmpty() && 
               edge.allMatchRelateType(RelateType.M2D);
    }

    /**
     * 测试内容描述：测试链式调用
     */
    @Test
    @DisplayName("使用场景 - 链式调用")
    void testMethodChaining() {
        // Act: 测试链式调用
        RelateEdge result = new RelateEdge()
            .add(testNode1)
            .add(testNode2)
            .add(testNode3);
        
        // Assert: 验证链式调用结果
        Set<RelateEdgeNode> nodes = result.getRelateEdgeNodes();
        assertEquals(3, nodes.size());
        assertTrue(nodes.contains(testNode1));
        assertTrue(nodes.contains(testNode2));
        assertTrue(nodes.contains(testNode3));
    }

    /**
     * 测试内容描述：测试不可变性
     */
    @Test
    @DisplayName("不可变性 - 返回集合不可变性")
    void testImmutability() {
        // Arrange: 添加测试节点
        relateEdge.add(testNode1);
        
        // Act: 获取节点集合
        Set<RelateEdgeNode> nodes = relateEdge.getRelateEdgeNodes();
        
        // Assert: 验证返回的集合是不可变的
        assertThrows(UnsupportedOperationException.class, () -> {
            nodes.add(testNode2);
        });
        
        assertThrows(UnsupportedOperationException.class, () -> {
            nodes.remove(testNode1);
        });
        
        assertThrows(UnsupportedOperationException.class, () -> {
            nodes.clear();
        });
    }

    /**
     * 测试内容描述：测试null安全性
     */
    @Test
    @DisplayName("安全性验证 - null安全性验证")
    void testNullSafety() {
        // Act & Assert: 验证null安全性
        assertDoesNotThrow(() -> {
            relateEdge.add(testNode1);
            relateEdge.getRelateEdgeNodes();
            relateEdge.allMatchRelateType(RelateType.M2D);
            relateEdge.anyMatchRelateType(RelateType.L2R);
            relateEdge.toString();
        });
        
        // 测试exclude方法的null安全性
        assertDoesNotThrow(() -> {
            relateEdge.exclude(node -> node.getRelateType() == RelateType.M2D);
        });
    }
}
