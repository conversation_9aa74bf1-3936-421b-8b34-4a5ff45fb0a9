package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.exception.ValidateException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * TeamMember的JUnit 5测试类
 * 测试团队成员的基本功能
 */
@ExtendWith(MockitoExtension.class)
class TeamMemberJUnit5Test {

    // ==================== 构造函数测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试基本构造函数
     */
    @Test
    @DisplayName("构造函数 - 基本构造函数")
    void testBasicConstructor() {
        String userId = "user123";
        TeamMember.Role role = TeamMember.Role.OWNER;
        TeamMember.Permission permission = TeamMember.Permission.READANDWRITE;

        TeamMember teamMember = new TeamMember(userId, role, permission);

        assertNotNull(teamMember);
        assertEquals(1, teamMember.getEmployeeList().size());
        assertEquals(userId, teamMember.getEmployee());
        assertEquals(role, teamMember.getRole());
        assertEquals(permission, teamMember.getPermission());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带MemberType的构造函数
     */
    @Test
    @DisplayName("构造函数 - 带MemberType")
    void testConstructorWithMemberType() {
        String userId = "user123";
        TeamMember.Role role = TeamMember.Role.OWNER;
        TeamMember.Permission permission = TeamMember.Permission.READANDWRITE;
        TeamMember.MemberType memberType = TeamMember.MemberType.EMPLOYEE;

        TeamMember teamMember = new TeamMember(userId, role, permission, memberType);

        assertNotNull(teamMember);
        assertEquals(memberType, teamMember.getMemberType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null userId异常
     */
    @Test
    @DisplayName("构造函数异常 - null userId")
    void testConstructor_NullUserId() {
        TeamMember.Role role = TeamMember.Role.OWNER;
        TeamMember.Permission permission = TeamMember.Permission.READANDWRITE;

        ValidateException exception = assertThrows(ValidateException.class, () -> {
            new TeamMember(null, role, permission);
        });

        assertEquals("employee is null", exception.getMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null permission异常
     */
    @Test
    @DisplayName("构造函数异常 - null permission")
    void testConstructor_NullPermission() {
        String userId = "user123";
        TeamMember.Role role = TeamMember.Role.OWNER;

        ValidateException exception = assertThrows(ValidateException.class, () -> {
            new TeamMember(userId, role, null);
        });

        assertEquals("permission is null", exception.getMessage());
    }

    // ==================== toMap方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试toMap方法基本功能
     */
    @Test
    @DisplayName("toMap - 基本功能")
    void testToMap_Basic() {
        String userId = "user123";
        TeamMember.Role role = TeamMember.Role.OWNER;
        TeamMember.Permission permission = TeamMember.Permission.READANDWRITE;

        TeamMember teamMember = new TeamMember(userId, role, permission);
        Map<String, Object> map = teamMember.toMap();

        assertNotNull(map);
        assertEquals(teamMember.getEmployeeList(), map.get(TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME));
        assertEquals(permission.getValue(), map.get(TeamMember.TEAM_MEMBER_PERMISSION_TYPE_API_NAME));
        assertEquals(role.getValue(), map.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toMap方法 - 带outTenantId
     */
    @Test
    @DisplayName("toMap - 带outTenantId")
    void testToMap_WithOutTenantId() {
        String userId = "user123";
        TeamMember.Role role = TeamMember.Role.OWNER;
        TeamMember.Permission permission = TeamMember.Permission.READANDWRITE;
        String outTenantId = "tenant456";

        TeamMember teamMember = new TeamMember(userId, role, permission, outTenantId);
        Map<String, Object> map = teamMember.toMap();

        assertEquals(outTenantId, map.get(TeamMember.TEAM_MEMBER_OUT_EMPLOYEE_EI));
        assertEquals("2", map.get(TeamMember.TEAM_MEMBER_EMPLOYEE_SOURCE_TYPE));
    }

    // ==================== 静态方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试静态方法调用
     */
    @Test
    @DisplayName("静态方法 - 灰度配置方法")
    void testStaticMethods() {
        // 这些方法依赖外部配置，我们只测试它们不抛出异常
        assertDoesNotThrow(() -> {
            TeamMember.isTeamRoleGray("test-tenant");
            TeamMember.isTeamMemberTypeExportGray("test-tenant");
        });
    }

    // ==================== 枚举测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试Role枚举
     */
    @Test
    @DisplayName("枚举测试 - Role枚举")
    void testRoleEnum() {
        // 测试Role枚举的基本功能
        assertEquals("1", TeamMember.Role.OWNER.getValue());
        assertEquals("2", TeamMember.Role.FOLLOWER.getValue());
        assertEquals("3", TeamMember.Role.SERVICE_STAFF.getValue());
        assertEquals("4", TeamMember.Role.NORMAL_STAFF.getValue());
        assertEquals("0", TeamMember.Role.NOT_EXIST.getValue());

        // 测试of方法
        assertEquals(TeamMember.Role.OWNER, TeamMember.Role.of("1"));
        assertEquals(TeamMember.Role.NOT_EXIST, TeamMember.Role.of("unknown"));
    }
}