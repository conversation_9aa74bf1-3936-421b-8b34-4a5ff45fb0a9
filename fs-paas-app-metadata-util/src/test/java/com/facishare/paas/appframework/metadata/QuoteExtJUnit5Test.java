package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.describe.EmployeeFieldDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * QuoteExt的JUnit 5测试类
 * 测试引用字段扩展类功能
 * 
 * GenerateByAI
 * 测试内容描述：测试引用字段扩展的创建、解析和构建功能
 */
class QuoteExtJUnit5Test {

    private Quote mockQuote;
    private QuoteExt quoteExt;
    private IObjectDescribe mockObjectDescribe;
    private IFieldDescribe mockFieldDescribe;

    @BeforeEach
    void setUp() {
        mockQuote = mock(Quote.class);
        quoteExt = QuoteExt.of(mockQuote);
        mockObjectDescribe = mock(IObjectDescribe.class);
        mockFieldDescribe = mock(IFieldDescribe.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("工厂方法 - of方法创建实例")
    void testOf() {
        // Act: 使用of方法创建实例
        QuoteExt result = QuoteExt.of(mockQuote);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertSame(mockQuote, result.getQuote());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseQuoteField方法 - 基本功能
     */
    @Test
    @DisplayName("基本功能 - parseQuoteField方法")
    void testParseQuoteField_BasicFunction() {
        // Arrange: 设置引用字段
        when(mockQuote.getQuoteField()).thenReturn("account__r.name");
        
        // Act: 执行parseQuoteField方法
        Tuple<String, String> result = quoteExt.parseQuoteField();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("account", result.getKey());
        assertEquals("name", result.getValue());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseQuoteField方法 - 复杂字段名
     */
    @Test
    @DisplayName("复杂场景 - parseQuoteField方法复杂字段名")
    void testParseQuoteField_ComplexFieldName() {
        // Arrange: 设置复杂的引用字段
        when(mockQuote.getQuoteField()).thenReturn("custom_object__r.custom_field__c");
        
        // Act: 执行parseQuoteField方法
        Tuple<String, String> result = quoteExt.parseQuoteField();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("custom_object", result.getKey());
        assertEquals("custom_field__c", result.getValue());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildQuoteField静态方法
     */
    @Test
    @DisplayName("静态方法 - buildQuoteField构建引用字段")
    void testBuildQuoteField() {
        // Act: 执行buildQuoteField静态方法
        String result = QuoteExt.buildQuoteField("account", "name");
        
        // Assert: 验证结果
        assertEquals("account__r.name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildQuoteField静态方法 - 参数化测试
     */
    @ParameterizedTest
    @ValueSource(strings = {"name", "email", "phone", "custom_field__c"})
    @DisplayName("参数化测试 - buildQuoteField多种字段")
    void testBuildQuoteField_ParameterizedFields(String quotedField) {
        // Act: 执行buildQuoteField静态方法
        String result = QuoteExt.buildQuoteField("account", quotedField);
        
        // Assert: 验证结果
        assertEquals("account__r." + quotedField, result);
        assertTrue(result.contains("__r."));
        assertTrue(result.startsWith("account"));
        assertTrue(result.endsWith(quotedField));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getQuotedObjectApiName静态方法 - 主从关系字段
     */
    @Test
    @DisplayName("静态方法 - getQuotedObjectApiName主从关系字段")
    void testGetQuotedObjectApiName_MasterDetailField() {
        // Arrange: 设置主从关系字段
        MasterDetail mockMasterDetail = mock(MasterDetail.class);
        when(mockMasterDetail.getTargetApiName()).thenReturn("master_object");
        
        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<FieldDescribeExt> fieldDescribeExtMock = mockStatic(FieldDescribeExt.class)) {
            
            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
            
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.getActiveFieldDescribeSilently("ref_field")).thenReturn(Optional.of(mockFieldDescribe));
            
            fieldDescribeExtMock.when(() -> FieldDescribeExt.of(mockFieldDescribe)).thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isMasterDetailField()).thenReturn(true);
            when(mockFieldDescribeExt.getFieldDescribe()).thenReturn(mockMasterDetail);
            
            // Act: 执行getQuotedObjectApiName方法
            String result = QuoteExt.getQuotedObjectApiName(mockObjectDescribe, "ref_field");
            
            // Assert: 验证结果
            assertEquals("master_object", result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getQuotedObjectApiName静态方法 - 引用字段
     */
    @Test
    @DisplayName("静态方法 - getQuotedObjectApiName引用字段")
    void testGetQuotedObjectApiName_ReferenceField() {
        // Arrange: 设置引用字段
        IObjectReferenceField mockReferenceField = mock(IObjectReferenceField.class);
        when(mockReferenceField.getTargetApiName()).thenReturn("reference_object");
        
        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<FieldDescribeExt> fieldDescribeExtMock = mockStatic(FieldDescribeExt.class)) {
            
            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
            
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.getActiveFieldDescribeSilently("ref_field")).thenReturn(Optional.of(mockFieldDescribe));
            
            fieldDescribeExtMock.when(() -> FieldDescribeExt.of(mockFieldDescribe)).thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isMasterDetailField()).thenReturn(false);
            when(mockFieldDescribeExt.getFieldDescribe()).thenReturn(mockReferenceField);
            
            // Act: 执行getQuotedObjectApiName方法
            String result = QuoteExt.getQuotedObjectApiName(mockObjectDescribe, "ref_field");
            
            // Assert: 验证结果
            assertEquals("reference_object", result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getQuotedObjectApiName静态方法 - 字段不存在
     */
    @Test
    @DisplayName("边界条件 - getQuotedObjectApiName字段不存在")
    void testGetQuotedObjectApiName_FieldNotExists() {
        // Arrange: 设置字段不存在
        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.getActiveFieldDescribeSilently("nonexistent_field")).thenReturn(Optional.empty());
            
            // Act: 执行getQuotedObjectApiName方法
            String result = QuoteExt.getQuotedObjectApiName(mockObjectDescribe, "nonexistent_field");
            
            // Assert: 验证结果
            assertEquals("", result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试usingLastModifiedTime方法 - 使用最后修改时间
     */
    @Test
    @DisplayName("时间字段 - usingLastModifiedTime使用最后修改时间")
    void testUsingLastModifiedTime_True() {
        // Arrange: 设置引用字段为最后修改时间
        when(mockQuote.getQuoteField()).thenReturn("account__r." + DBRecord.LAST_MODIFIED_TIME);
        
        // Act: 执行usingLastModifiedTime方法
        boolean result = quoteExt.usingLastModifiedTime();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试usingLastModifiedTime方法 - 不使用最后修改时间
     */
    @Test
    @DisplayName("时间字段 - usingLastModifiedTime不使用最后修改时间")
    void testUsingLastModifiedTime_False() {
        // Arrange: 设置引用字段为其他字段
        when(mockQuote.getQuoteField()).thenReturn("account__r.name");
        
        // Act: 执行usingLastModifiedTime方法
        boolean result = quoteExt.usingLastModifiedTime();
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试syncSingleFlag方法 - 同步单选标志
     */
    @Test
    @DisplayName("同步功能 - syncSingleFlag同步单选标志")
    void testSyncSingleFlag_WithSingleFlag() {
        // Arrange: 设置字段描述的单选标志
        when(mockFieldDescribe.get(EmployeeFieldDescribe.IS_SINGLE, Boolean.class)).thenReturn(true);
        
        // Act: 执行syncSingleFlag方法
        quoteExt.syncSingleFlag(mockFieldDescribe);
        
        // Assert: 验证set方法被调用
        verify(mockQuote).set(EmployeeFieldDescribe.IS_SINGLE, true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试syncSingleFlag方法 - 无单选标志
     */
    @Test
    @DisplayName("同步功能 - syncSingleFlag无单选标志")
    void testSyncSingleFlag_WithoutSingleFlag() {
        // Arrange: 设置字段描述的单选标志为null
        when(mockFieldDescribe.get(EmployeeFieldDescribe.IS_SINGLE, Boolean.class)).thenReturn(null);
        
        // Act: 执行syncSingleFlag方法
        quoteExt.syncSingleFlag(mockFieldDescribe);
        
        // Assert: 验证set方法没有被调用
        verify(mockQuote, never()).set(eq(EmployeeFieldDescribe.IS_SINGLE), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托功能
     */
    @Test
    @DisplayName("委托功能 - 验证委托方法调用")
    void testDelegateFeatures() {
        // Arrange: 设置委托方法的返回值
        when(mockQuote.getApiName()).thenReturn("test_quote");
        when(mockQuote.getLabel()).thenReturn("Test Quote");
        when(mockQuote.isRequired()).thenReturn(true);
        when(mockQuote.getQuoteField()).thenReturn("account__r.name");
        
        // Act & Assert: 验证委托的方法
        assertEquals("test_quote", quoteExt.getApiName());
        assertEquals("Test Quote", quoteExt.getLabel());
        assertTrue(quoteExt.isRequired());
        assertEquals("account__r.name", quoteExt.getQuoteField());
        
        // 验证委托调用
        verify(mockQuote).getApiName();
        verify(mockQuote).getLabel();
        verify(mockQuote).isRequired();
        verify(mockQuote, times(1)).getQuoteField(); // 只调用一次
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // Arrange: 设置toString返回值
        when(mockQuote.toString()).thenReturn("Quote[apiName=test_quote]");
        
        // Act: 调用toString方法
        String result = quoteExt.toString();
        
        // Assert: 验证toString结果
        assertNotNull(result);
        assertTrue(result.contains("Quote"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals和hashCode方法
     */
    @Test
    @DisplayName("对象比较 - equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Arrange: 创建两个使用相同Quote的实例
        QuoteExt ext1 = QuoteExt.of(mockQuote);
        QuoteExt ext2 = QuoteExt.of(mockQuote);
        
        // Assert: 验证equals和hashCode
        assertEquals(ext1.getQuote(), ext2.getQuote());
        
        // 创建使用不同Quote的实例
        Quote anotherQuote = mock(Quote.class);
        QuoteExt ext3 = QuoteExt.of(anotherQuote);
        
        assertNotEquals(ext1.getQuote(), ext3.getQuote());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseQuoteField方法 - 边界条件
     */
    @Test
    @DisplayName("边界条件 - parseQuoteField边界条件")
    void testParseQuoteField_EdgeCases() {
        // 测试空字符串
        when(mockQuote.getQuoteField()).thenReturn("");
        assertThrows(Exception.class, () -> quoteExt.parseQuoteField());
        
        // 测试null值
        when(mockQuote.getQuoteField()).thenReturn(null);
        assertThrows(Exception.class, () -> quoteExt.parseQuoteField());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildQuoteField静态方法 - 边界条件
     */
    @Test
    @DisplayName("边界条件 - buildQuoteField边界条件")
    void testBuildQuoteField_EdgeCases() {
        // 测试空字符串参数
        String result1 = QuoteExt.buildQuoteField("", "name");
        assertEquals("__r.name", result1);
        
        String result2 = QuoteExt.buildQuoteField("account", "");
        assertEquals("account__r.", result2);
        
        // 测试null参数
        String result3 = QuoteExt.buildQuoteField(null, "name");
        assertEquals("null__r.name", result3);
        
        String result4 = QuoteExt.buildQuoteField("account", null);
        assertEquals("account__r.null", result4);
    }
}
