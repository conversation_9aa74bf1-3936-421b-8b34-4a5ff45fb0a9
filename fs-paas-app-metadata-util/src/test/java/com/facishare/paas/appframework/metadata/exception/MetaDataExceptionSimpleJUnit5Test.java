package com.facishare.paas.appframework.metadata.exception;

import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MetaDataException的简化JUnit 5测试类
 * 测试元数据异常类基本功能
 */
class MetaDataExceptionSimpleJUnit5Test {

    /**
     * 测试内容描述：测试带消息和原因的构造函数
     */
    @Test
    @DisplayName("构造函数 - 带消息和原因的构造函数")
    void testConstructor_MessageAndCause() {
        // Arrange: 准备测试数据
        String message = "Test metadata exception";
        Throwable cause = new RuntimeException("Root cause");
        
        // Act: 创建异常实例
        MetaDataException exception = new MetaDataException(message, cause);
        
        // Assert: 验证异常属性
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertTrue(exception instanceof APPException);
    }

    /**
     * 测试内容描述：测试只带消息的构造函数
     */
    @Test
    @DisplayName("构造函数 - 只带消息的构造函数")
    void testConstructor_MessageOnly() {
        // Arrange: 准备测试数据
        String message = "Test metadata exception without cause";
        
        // Act: 创建异常实例
        MetaDataException exception = new MetaDataException(message);
        
        // Assert: 验证异常属性
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
        assertTrue(exception instanceof APPException);
    }

    /**
     * 测试内容描述：测试带错误码和原因的构造函数
     */
    @Test
    @DisplayName("构造函数 - 带错误码和原因的构造函数")
    void testConstructor_ErrorCodeAndCause() {
        // Arrange: 准备测试数据
        SystemErrorCode errorCode = SystemErrorCode.METADATA_ERROR;
        Throwable cause = new IllegalArgumentException("Invalid argument");
        
        // Act: 创建异常实例
        MetaDataException exception = new MetaDataException(errorCode, cause);
        
        // Assert: 验证异常属性
        // getMessage可能返回null，这是正常的
        assertEquals(cause, exception.getCause());
        assertTrue(exception instanceof APPException);
    }

    /**
     * 测试内容描述：测试只带错误码的构造函数
     */
    @Test
    @DisplayName("构造函数 - 只带错误码的构造函数")
    void testConstructor_ErrorCodeOnly() {
        // Arrange: 准备测试数据
        SystemErrorCode errorCode = SystemErrorCode.METADATA_ERROR;
        
        // Act: 创建异常实例
        MetaDataException exception = new MetaDataException(errorCode);
        
        // Assert: 验证异常属性
        // getMessage可能返回null，这是正常的
        assertNull(exception.getCause());
        assertTrue(exception instanceof APPException);
    }

    /**
     * 测试内容描述：测试继承关系
     */
    @Test
    @DisplayName("继承验证 - 继承关系验证")
    void testInheritance() {
        // Arrange: 创建异常实例
        MetaDataException exception = new MetaDataException("Test message");
        
        // Assert: 验证继承关系
        assertTrue(exception instanceof APPException);
        assertTrue(exception instanceof RuntimeException);
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
    }

    /**
     * 测试内容描述：测试异常抛出和捕获
     */
    @Test
    @DisplayName("异常处理 - 异常抛出和捕获")
    void testThrowAndCatch() {
        // Arrange: 准备测试数据
        String expectedMessage = "Test exception throwing";
        
        // Act & Assert: 验证异常抛出和捕获
        MetaDataException thrownException = assertThrows(MetaDataException.class, () -> {
            throw new MetaDataException(expectedMessage);
        });
        
        assertEquals(expectedMessage, thrownException.getMessage());
    }

    /**
     * 测试内容描述：测试异常链
     */
    @Test
    @DisplayName("异常链 - 异常链验证")
    void testExceptionChain() {
        // Arrange: 创建异常链
        RuntimeException rootCause = new RuntimeException("Root cause");
        IllegalArgumentException intermediateCause = new IllegalArgumentException("Intermediate cause", rootCause);
        MetaDataException topException = new MetaDataException("Top level exception", intermediateCause);
        
        // Act & Assert: 验证异常链
        assertEquals("Top level exception", topException.getMessage());
        assertEquals(intermediateCause, topException.getCause());
        assertEquals(rootCause, topException.getCause().getCause());
    }

    /**
     * 测试内容描述：测试空消息处理
     */
    @Test
    @DisplayName("边界条件 - 空消息处理")
    void testEmptyMessage() {
        // Act: 创建空消息异常
        MetaDataException emptyMessageException = new MetaDataException("");
        MetaDataException nullMessageException = new MetaDataException((String) null);
        
        // Assert: 验证空消息处理
        assertEquals("", emptyMessageException.getMessage());
        assertNull(nullMessageException.getMessage());
    }

    /**
     * 测试内容描述：测试业务场景 - 数据验证异常
     */
    @Test
    @DisplayName("业务场景 - 数据验证异常")
    void testDataValidationException() {
        // Arrange: 模拟数据验证失败场景
        String fieldName = "email";
        String invalidValue = "invalid-email";
        String message = String.format("Invalid %s value: %s", fieldName, invalidValue);
        
        // Act: 创建数据验证异常
        MetaDataException validationException = new MetaDataException(message);
        
        // Assert: 验证业务异常
        assertTrue(validationException.getMessage().contains("Invalid"));
        assertTrue(validationException.getMessage().contains(fieldName));
        assertTrue(validationException.getMessage().contains(invalidValue));
    }

    /**
     * 测试内容描述：测试业务场景 - 数据库操作异常
     */
    @Test
    @DisplayName("业务场景 - 数据库操作异常")
    void testDatabaseOperationException() {
        // Arrange: 模拟数据库操作失败场景
        String operation = "INSERT";
        String tableName = "metadata_table";
        RuntimeException dbCause = new RuntimeException("Connection timeout");
        String message = String.format("Database %s operation failed on table %s", operation, tableName);
        
        // Act: 创建数据库操作异常
        MetaDataException dbException = new MetaDataException(message, dbCause);
        
        // Assert: 验证数据库异常
        assertTrue(dbException.getMessage().contains(operation));
        assertTrue(dbException.getMessage().contains(tableName));
        assertEquals(dbCause, dbException.getCause());
    }

    /**
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // Arrange: 创建异常实例
        String message = "Test toString method";
        MetaDataException exception = new MetaDataException(message);
        
        // Act: 执行toString方法
        String result = exception.toString();
        
        // Assert: 验证toString结果
        assertNotNull(result);
        assertTrue(result.contains("MetaDataException"));
        assertTrue(result.contains(message));
    }

    /**
     * 测试内容描述：测试异常序列化
     */
    @Test
    @DisplayName("序列化 - 异常序列化验证")
    void testExceptionSerialization() {
        // Arrange: 创建异常实例
        String message = "Serialization test exception";
        RuntimeException cause = new RuntimeException("Serialization cause");
        MetaDataException exception = new MetaDataException(message, cause);
        
        // Act & Assert: 验证异常可以被序列化（通过检查关键属性）
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertNotNull(exception.getStackTrace());
        
        // 验证异常实现了Serializable接口
        assertTrue(exception instanceof java.io.Serializable);
    }

    /**
     * 测试内容描述：测试异常消息格式化
     */
    @Test
    @DisplayName("消息格式化 - 异常消息格式化")
    void testExceptionMessageFormatting() {
        // Arrange: 准备格式化参数
        String template = "Operation %s failed for object %s with error code %d";
        String operation = "CREATE";
        String objectName = "TestObject";
        int errorCode = 500;
        String formattedMessage = String.format(template, operation, objectName, errorCode);
        
        // Act: 创建格式化消息的异常
        MetaDataException exception = new MetaDataException(formattedMessage);
        
        // Assert: 验证消息格式化
        assertTrue(exception.getMessage().contains(operation));
        assertTrue(exception.getMessage().contains(objectName));
        assertTrue(exception.getMessage().contains(String.valueOf(errorCode)));
        assertEquals(formattedMessage, exception.getMessage());
    }

    /**
     * 测试内容描述：测试异常在多线程环境中的行为
     */
    @Test
    @DisplayName("线程安全 - 多线程环境中的异常行为")
    void testExceptionInMultiThreadEnvironment() {
        // Arrange: 准备多线程测试
        String message = "Multi-thread exception test";
        
        // Act & Assert: 在多线程环境中测试异常
        Thread thread1 = new Thread(() -> {
            MetaDataException exception = new MetaDataException(message);
            assertEquals(message, exception.getMessage());
        });
        
        Thread thread2 = new Thread(() -> {
            MetaDataException exception = new MetaDataException(message);
            assertEquals(message, exception.getMessage());
        });
        
        assertDoesNotThrow(() -> {
            thread1.start();
            thread2.start();
            thread1.join();
            thread2.join();
        });
    }

    /**
     * 测试内容描述：测试异常的性能
     */
    @Test
    @DisplayName("性能测试 - 异常创建性能")
    void testExceptionPerformance() {
        // Arrange: 准备性能测试
        int iterations = 100;
        String message = "Performance test exception";
        
        // Act: 执行异常创建
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            MetaDataException exception = new MetaDataException(message + i);
            assertNotNull(exception.getMessage());
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // Assert: 验证性能（应该在合理时间内完成）
        assertTrue(duration < 1000, "异常创建应该在1秒内完成，实际耗时: " + duration + "ms");
    }
}
