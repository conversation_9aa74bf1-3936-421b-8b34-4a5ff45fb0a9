package com.facishare.paas.appframework.metadata.layout.rule;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LayoutRuleFilter的JUnit 5测试类
 * 测试布局规则过滤器功能
 */
class LayoutRuleFilterJUnit5Test {

    private LayoutRuleFilter layoutRuleFilter;
    private Map<String, Object> testFilterMap;

    @BeforeEach
    void setUp() {
        // 创建测试用的过滤器数据
        testFilterMap = Maps.newHashMap();
        testFilterMap.put(ILayoutRuleFilter.FIELD_NAME, "status");
        testFilterMap.put(ILayoutRuleFilter.OPERATOR, "equals");
        testFilterMap.put(ILayoutRuleFilter.FIELD_VALUES, Lists.newArrayList("active", "pending"));
        testFilterMap.put(ILayoutRuleFilter.VALUE_TYPE, 1);
        
        layoutRuleFilter = new LayoutRuleFilter(testFilterMap);
    }

    /**
     * 测试内容描述：测试默认构造函数
     */
    @Test
    @DisplayName("构造函数 - 默认构造函数")
    void testDefaultConstructor() {
        // Act: 创建默认实例
        LayoutRuleFilter filter = new LayoutRuleFilter();
        
        // Assert: 验证默认构造函数
        assertNotNull(filter);
    }

    /**
     * 测试内容描述：测试Map构造函数
     */
    @Test
    @DisplayName("构造函数 - Map构造函数")
    void testMapConstructor() {
        // Act: 使用Map创建实例
        LayoutRuleFilter filter = new LayoutRuleFilter(testFilterMap);
        
        // Assert: 验证Map构造函数
        assertNotNull(filter);
        assertEquals("status", filter.getFieldName());
    }

    /**
     * 测试内容描述：测试getFieldName方法
     */
    @Test
    @DisplayName("访问器方法 - getFieldName方法")
    void testGetFieldName() {
        // Act: 执行getFieldName方法
        String result = layoutRuleFilter.getFieldName();
        
        // Assert: 验证结果
        assertEquals("status", result);
    }

    /**
     * 测试内容描述：测试getOperator方法
     */
    @Test
    @DisplayName("访问器方法 - getOperator方法")
    void testGetOperator() {
        // Act: 执行getOperator方法
        String result = layoutRuleFilter.getOperator();
        
        // Assert: 验证结果
        assertEquals("equals", result);
    }

    /**
     * 测试内容描述：测试getFieldValues方法
     */
    @Test
    @DisplayName("访问器方法 - getFieldValues方法")
    void testGetFieldValues() {
        // Act: 执行getFieldValues方法
        List<?> result = layoutRuleFilter.getFieldValues();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("active"));
        assertTrue(result.contains("pending"));
    }

    /**
     * 测试内容描述：测试setFieldValues方法
     */
    @Test
    @DisplayName("修改器方法 - setFieldValues方法")
    void testSetFieldValues() {
        // Arrange: 准备新的字段值
        List<String> newValues = Lists.newArrayList("completed", "cancelled");
        
        // Act: 设置新的字段值
        layoutRuleFilter.setFieldValues(newValues);
        
        // Assert: 验证设置结果
        List<?> result = layoutRuleFilter.getFieldValues();
        assertEquals(2, result.size());
        assertTrue(result.contains("completed"));
        assertTrue(result.contains("cancelled"));
    }

    /**
     * 测试内容描述：测试getValueType方法
     */
    @Test
    @DisplayName("访问器方法 - getValueType方法")
    void testGetValueType() {
        // Act: 执行getValueType方法
        Integer result = layoutRuleFilter.getValueType();
        
        // Assert: 验证结果
        assertEquals(1, result);
    }

    /**
     * 测试内容描述：测试业务场景 - 状态过滤器
     */
    @Test
    @DisplayName("业务场景 - 状态过滤器")
    void testStatusFilter() {
        // Arrange: 创建状态过滤器
        Map<String, Object> statusFilterMap = Maps.newHashMap();
        statusFilterMap.put(ILayoutRuleFilter.FIELD_NAME, "record_status");
        statusFilterMap.put(ILayoutRuleFilter.OPERATOR, "in");
        statusFilterMap.put(ILayoutRuleFilter.FIELD_VALUES, Lists.newArrayList("active", "draft"));
        statusFilterMap.put(ILayoutRuleFilter.VALUE_TYPE, 1);
        
        // Act: 创建状态过滤器
        LayoutRuleFilter statusFilter = new LayoutRuleFilter(statusFilterMap);
        
        // Assert: 验证状态过滤器
        assertNotNull(statusFilter);
        assertEquals("record_status", statusFilter.getFieldName());
        assertEquals("in", statusFilter.getOperator());
        assertEquals(2, statusFilter.getFieldValues().size());
        assertEquals(1, statusFilter.getValueType());
    }

    /**
     * 测试内容描述：测试业务场景 - 数值范围过滤器
     */
    @Test
    @DisplayName("业务场景 - 数值范围过滤器")
    void testNumericRangeFilter() {
        // Arrange: 创建数值范围过滤器
        Map<String, Object> rangeFilterMap = Maps.newHashMap();
        rangeFilterMap.put(ILayoutRuleFilter.FIELD_NAME, "amount");
        rangeFilterMap.put(ILayoutRuleFilter.OPERATOR, "between");
        rangeFilterMap.put(ILayoutRuleFilter.FIELD_VALUES, Lists.newArrayList(1000, 5000));
        rangeFilterMap.put(ILayoutRuleFilter.VALUE_TYPE, 2); // 数值类型
        
        // Act: 创建数值范围过滤器
        LayoutRuleFilter rangeFilter = new LayoutRuleFilter(rangeFilterMap);
        
        // Assert: 验证数值范围过滤器
        assertNotNull(rangeFilter);
        assertEquals("amount", rangeFilter.getFieldName());
        assertEquals("between", rangeFilter.getOperator());
        assertEquals(2, rangeFilter.getFieldValues().size());
        assertEquals(2, rangeFilter.getValueType());
    }

    /**
     * 测试内容描述：测试业务场景 - 日期过滤器
     */
    @Test
    @DisplayName("业务场景 - 日期过滤器")
    void testDateFilter() {
        // Arrange: 创建日期过滤器
        Map<String, Object> dateFilterMap = Maps.newHashMap();
        dateFilterMap.put(ILayoutRuleFilter.FIELD_NAME, "create_time");
        dateFilterMap.put(ILayoutRuleFilter.OPERATOR, "greater_than");
        dateFilterMap.put(ILayoutRuleFilter.FIELD_VALUES, Lists.newArrayList("2023-01-01"));
        dateFilterMap.put(ILayoutRuleFilter.VALUE_TYPE, 3); // 日期类型
        
        // Act: 创建日期过滤器
        LayoutRuleFilter dateFilter = new LayoutRuleFilter(dateFilterMap);
        
        // Assert: 验证日期过滤器
        assertNotNull(dateFilter);
        assertEquals("create_time", dateFilter.getFieldName());
        assertEquals("greater_than", dateFilter.getOperator());
        assertEquals(1, dateFilter.getFieldValues().size());
        assertEquals(3, dateFilter.getValueType());
    }

    /**
     * 测试内容描述：测试业务场景 - 文本过滤器
     */
    @Test
    @DisplayName("业务场景 - 文本过滤器")
    void testTextFilter() {
        // Arrange: 创建文本过滤器
        Map<String, Object> textFilterMap = Maps.newHashMap();
        textFilterMap.put(ILayoutRuleFilter.FIELD_NAME, "name");
        textFilterMap.put(ILayoutRuleFilter.OPERATOR, "contains");
        textFilterMap.put(ILayoutRuleFilter.FIELD_VALUES, Lists.newArrayList("测试"));
        textFilterMap.put(ILayoutRuleFilter.VALUE_TYPE, 0); // 文本类型
        
        // Act: 创建文本过滤器
        LayoutRuleFilter textFilter = new LayoutRuleFilter(textFilterMap);
        
        // Assert: 验证文本过滤器
        assertNotNull(textFilter);
        assertEquals("name", textFilter.getFieldName());
        assertEquals("contains", textFilter.getOperator());
        assertEquals(1, textFilter.getFieldValues().size());
        assertEquals(0, textFilter.getValueType());
    }

    /**
     * 测试内容描述：测试空字段值处理
     */
    @Test
    @DisplayName("边界条件 - 空字段值处理")
    void testEmptyFieldValues() {
        // Arrange: 创建空字段值的过滤器
        Map<String, Object> emptyFilterMap = Maps.newHashMap();
        emptyFilterMap.put(ILayoutRuleFilter.FIELD_NAME, "empty_field");
        emptyFilterMap.put(ILayoutRuleFilter.OPERATOR, "is_null");
        emptyFilterMap.put(ILayoutRuleFilter.FIELD_VALUES, Lists.newArrayList());
        emptyFilterMap.put(ILayoutRuleFilter.VALUE_TYPE, 0);
        
        // Act: 创建空字段值过滤器
        LayoutRuleFilter emptyFilter = new LayoutRuleFilter(emptyFilterMap);
        
        // Assert: 验证空字段值处理
        assertNotNull(emptyFilter);
        assertEquals("empty_field", emptyFilter.getFieldName());
        assertEquals("is_null", emptyFilter.getOperator());
        assertTrue(emptyFilter.getFieldValues().isEmpty());
    }

    /**
     * 测试内容描述：测试null参数处理
     */
    @Test
    @DisplayName("安全性验证 - null参数处理")
    void testNullParameterHandling() {
        // Act & Assert: 验证null参数会抛出异常
        assertThrows(NullPointerException.class, () -> {
            LayoutRuleFilter nullFilter = new LayoutRuleFilter(null);
            nullFilter.getFieldName();
        });
    }

    /**
     * 测试内容描述：测试复杂操作符
     */
    @Test
    @DisplayName("复杂场景 - 复杂操作符")
    void testComplexOperators() {
        // Arrange: 测试各种操作符
        String[] operators = {
            "equals", "not_equals", "greater_than", "less_than",
            "greater_than_or_equal", "less_than_or_equal",
            "in", "not_in", "contains", "not_contains",
            "starts_with", "ends_with", "is_null", "is_not_null"
        };
        
        for (String operator : operators) {
            Map<String, Object> operatorFilterMap = Maps.newHashMap();
            operatorFilterMap.put(ILayoutRuleFilter.FIELD_NAME, "test_field");
            operatorFilterMap.put(ILayoutRuleFilter.OPERATOR, operator);
            operatorFilterMap.put(ILayoutRuleFilter.FIELD_VALUES, Lists.newArrayList("test_value"));
            operatorFilterMap.put(ILayoutRuleFilter.VALUE_TYPE, 0);
            
            // Act: 创建操作符过滤器
            LayoutRuleFilter operatorFilter = new LayoutRuleFilter(operatorFilterMap);
            
            // Assert: 验证操作符过滤器
            assertNotNull(operatorFilter);
            assertEquals(operator, operatorFilter.getOperator());
        }
    }

    /**
     * 测试内容描述：测试多值字段
     */
    @Test
    @DisplayName("复杂场景 - 多值字段")
    void testMultipleValues() {
        // Arrange: 创建多值字段过滤器
        List<Object> multipleValues = Lists.newArrayList(
            "value1", "value2", "value3", "value4", "value5"
        );
        
        Map<String, Object> multiValueFilterMap = Maps.newHashMap();
        multiValueFilterMap.put(ILayoutRuleFilter.FIELD_NAME, "multi_select_field");
        multiValueFilterMap.put(ILayoutRuleFilter.OPERATOR, "in");
        multiValueFilterMap.put(ILayoutRuleFilter.FIELD_VALUES, multipleValues);
        multiValueFilterMap.put(ILayoutRuleFilter.VALUE_TYPE, 1);
        
        // Act: 创建多值过滤器
        LayoutRuleFilter multiValueFilter = new LayoutRuleFilter(multiValueFilterMap);
        
        // Assert: 验证多值过滤器
        assertNotNull(multiValueFilter);
        assertEquals("multi_select_field", multiValueFilter.getFieldName());
        assertEquals("in", multiValueFilter.getOperator());
        assertEquals(5, multiValueFilter.getFieldValues().size());
    }

    /**
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Act: 多次获取相同的数据
        String fieldName1 = layoutRuleFilter.getFieldName();
        String fieldName2 = layoutRuleFilter.getFieldName();
        String operator1 = layoutRuleFilter.getOperator();
        String operator2 = layoutRuleFilter.getOperator();
        
        // Assert: 验证数据一致性
        assertEquals(fieldName1, fieldName2);
        assertEquals(operator1, operator2);
    }

    /**
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // Act: 执行toString方法
        String result = layoutRuleFilter.toString();
        
        // Assert: 验证toString结果
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
    }

    /**
     * 测试内容描述：测试equals和hashCode方法
     */
    @Test
    @DisplayName("对象比较 - equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Arrange: 创建相同内容的过滤器
        LayoutRuleFilter sameFilter = new LayoutRuleFilter(testFilterMap);
        
        // Act & Assert: 测试equals方法
        assertEquals(layoutRuleFilter, layoutRuleFilter); // 自身相等
        
        // 验证hashCode一致性
        assertEquals(layoutRuleFilter.hashCode(), layoutRuleFilter.hashCode());
    }

    /**
     * 测试内容描述：测试线程安全性
     */
    @Test
    @DisplayName("线程安全 - 多线程访问")
    void testThreadSafety() throws InterruptedException {
        // Act: 在多线程中访问
        Thread thread1 = new Thread(() -> {
            String fieldName = layoutRuleFilter.getFieldName();
            assertNotNull(fieldName);
        });
        
        Thread thread2 = new Thread(() -> {
            String operator = layoutRuleFilter.getOperator();
            assertNotNull(operator);
        });
        
        // Assert: 验证线程安全
        assertDoesNotThrow(() -> {
            thread1.start();
            thread2.start();
            thread1.join();
            thread2.join();
        });
    }
}
