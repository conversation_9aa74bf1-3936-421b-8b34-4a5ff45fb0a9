package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.HtmlUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.describe.HtmlRichText;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * RichTextExt的JUnit 5测试类
 * 测试富文本扩展类功能
 */
class RichTextExtJUnit5Test {

    private HtmlRichText mockHtmlRichText;
    private IFieldDescribe mockFieldDescribe;
    private IObjectDescribe mockObjectDescribe;

    @BeforeEach
    void setUp() {
        mockHtmlRichText = mock(HtmlRichText.class);
        mockFieldDescribe = mock(IFieldDescribe.class);
        mockObjectDescribe = mock(IObjectDescribe.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("of - 工厂方法")
    void testOf() {
        RichTextExt result = RichTextExt.of(mockHtmlRichText);

        assertNotNull(result);
        assertSame(mockHtmlRichText, result.getRichText());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRichTextAbstractName方法
     */
    @Test
    @DisplayName("getRichTextAbstractName - 生成抽象字段名")
    void testGetRichTextAbstractName() {
        String fieldName = "content";
        
        String result = RichTextExt.getRichTextAbstractName(fieldName);
        
        assertEquals("content__o", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRichTextAbstractName方法 - 空字段名
     */
    @Test
    @DisplayName("getRichTextAbstractName - 空字段名")
    void testGetRichTextAbstractName_EmptyFieldName() {
        String result1 = RichTextExt.getRichTextAbstractName("");
        assertEquals("__o", result1);

        String result2 = RichTextExt.getRichTextAbstractName(null);
        assertEquals("null__o", result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRichTextNameInMD方法
     */
    @Test
    @DisplayName("getRichTextNameInMD - 生成MD字段名")
    void testGetRichTextNameInMD() {
        String fieldName = "content";
        
        String result = RichTextExt.getRichTextNameInMD(fieldName);
        
        assertEquals("content__e", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRichTextNameInMD方法 - 空字段名
     */
    @Test
    @DisplayName("getRichTextNameInMD - 空字段名")
    void testGetRichTextNameInMD_EmptyFieldName() {
        String result1 = RichTextExt.getRichTextNameInMD("");
        assertEquals("__e", result1);

        String result2 = RichTextExt.getRichTextNameInMD(null);
        assertEquals("null__e", result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isProcessableRichText方法 - HTML富文本类型
     */
    @Test
    @DisplayName("isProcessableRichText - HTML富文本类型")
    void testIsProcessableRichText_HtmlRichText() {
        when(mockFieldDescribe.getType()).thenReturn(IFieldType.HTML_RICH_TEXT);
        
        boolean result = RichTextExt.isProcessableRichText(mockFieldDescribe);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isProcessableRichText方法 - 非HTML富文本类型
     */
    @Test
    @DisplayName("isProcessableRichText - 非HTML富文本类型")
    void testIsProcessableRichText_NotHtmlRichText() {
        when(mockFieldDescribe.getType()).thenReturn(IFieldType.TEXT);
        
        boolean result = RichTextExt.isProcessableRichText(mockFieldDescribe);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isProcessableRichText方法 - null字段
     */
    @Test
    @DisplayName("isProcessableRichText - null字段")
    void testIsProcessableRichText_NullField() {
        boolean result = RichTextExt.isProcessableRichText(null);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isProcessableCooperativeRichText方法 - 协作富文本类型
     */
    @Test
    @DisplayName("isProcessableCooperativeRichText - 协作富文本类型")
    void testIsProcessableCooperativeRichText_RichText() {
        when(mockFieldDescribe.getType()).thenReturn(IFieldType.RICH_TEXT);
        
        boolean result = RichTextExt.isProcessableCooperativeRichText(mockFieldDescribe);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isProcessableCooperativeRichText方法 - 非协作富文本类型
     */
    @Test
    @DisplayName("isProcessableCooperativeRichText - 非协作富文本类型")
    void testIsProcessableCooperativeRichText_NotRichText() {
        when(mockFieldDescribe.getType()).thenReturn(IFieldType.TEXT);
        
        boolean result = RichTextExt.isProcessableCooperativeRichText(mockFieldDescribe);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isProcessableCooperativeRichText方法 - null字段
     */
    @Test
    @DisplayName("isProcessableCooperativeRichText - null字段")
    void testIsProcessableCooperativeRichText_NullField() {
        boolean result = RichTextExt.isProcessableCooperativeRichText(null);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateHtml方法 - 正常HTML
     */
    @Test
    @DisplayName("validateHtml - 正常HTML")
    void testValidateHtml_ValidHtml() {
        try (MockedStatic<HtmlUtils> htmlUtilsMock = mockStatic(HtmlUtils.class)) {
            htmlUtilsMock.when(() -> HtmlUtils.hasScriptTag("<p>Hello World</p>")).thenReturn(false);
            
            assertDoesNotThrow(() -> RichTextExt.validateHtml("<p>Hello World</p>"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateHtml方法 - 包含脚本标签
     */
    @Test
    @DisplayName("validateHtml - 包含脚本标签")
    void testValidateHtml_WithScriptTag() {
        try (MockedStatic<HtmlUtils> htmlUtilsMock = mockStatic(HtmlUtils.class)) {
            htmlUtilsMock.when(() -> HtmlUtils.hasScriptTag("<script>alert('test')</script>")).thenReturn(true);
            
            assertThrows(ValidateException.class, () -> RichTextExt.validateHtml("<script>alert('test')</script>"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateHtml方法 - 空HTML
     */
    @Test
    @DisplayName("validateHtml - 空HTML")
    void testValidateHtml_EmptyHtml() {
        assertDoesNotThrow(() -> RichTextExt.validateHtml(""));
        assertDoesNotThrow(() -> RichTextExt.validateHtml(null));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isRichTextAbstractField方法 - 有效的抽象字段
     */
    @Test
    @DisplayName("isRichTextAbstractField - 有效的抽象字段")
    void testIsRichTextAbstractField_ValidAbstractField() {
        // 创建真实的字段描述
        IFieldDescribe richTextField = mock(IFieldDescribe.class);
        when(richTextField.getApiName()).thenReturn("content");
        when(richTextField.getType()).thenReturn(IFieldType.HTML_RICH_TEXT);

        // Mock ObjectDescribeExt
        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.filterOne(any())).thenReturn(Optional.of(richTextField));
            
            boolean result = RichTextExt.isRichTextAbstractField("content__o", mockObjectDescribe);
            
            assertTrue(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isRichTextAbstractField方法 - 不以__o结尾
     */
    @Test
    @DisplayName("isRichTextAbstractField - 不以__o结尾")
    void testIsRichTextAbstractField_NotEndingWithO() {
        boolean result = RichTextExt.isRichTextAbstractField("content", mockObjectDescribe);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isRichTextAbstractField方法 - 空字段名
     */
    @Test
    @DisplayName("isRichTextAbstractField - 空字段名")
    void testIsRichTextAbstractField_EmptyFieldName() {
        boolean result1 = RichTextExt.isRichTextAbstractField("", mockObjectDescribe);
        assertFalse(result1);

        boolean result2 = RichTextExt.isRichTextAbstractField(null, mockObjectDescribe);
        assertFalse(result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isRichTextAbstractField方法 - 字段不存在
     */
    @Test
    @DisplayName("isRichTextAbstractField - 字段不存在")
    void testIsRichTextAbstractField_FieldNotExists() {
        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.filterOne(any())).thenReturn(Optional.empty());
            
            boolean result = RichTextExt.isRichTextAbstractField("nonexistent__o", mockObjectDescribe);
            
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isRichTextAbstractField方法 - 协作富文本类型
     */
    @Test
    @DisplayName("isRichTextAbstractField - 协作富文本类型")
    void testIsRichTextAbstractField_CooperativeRichText() {
        // 创建协作富文本字段
        IFieldDescribe richTextField = mock(IFieldDescribe.class);
        when(richTextField.getApiName()).thenReturn("content");
        when(richTextField.getType()).thenReturn(IFieldType.RICH_TEXT);

        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.filterOne(any())).thenReturn(Optional.of(richTextField));
            
            boolean result = RichTextExt.isRichTextAbstractField("content__o", mockObjectDescribe);
            
            assertTrue(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isRichTextAbstractField方法 - 非富文本类型
     */
    @Test
    @DisplayName("isRichTextAbstractField - 非富文本类型")
    void testIsRichTextAbstractField_NotRichTextType() {
        // 创建普通文本字段
        IFieldDescribe textField = mock(IFieldDescribe.class);
        when(textField.getApiName()).thenReturn("content");
        when(textField.getType()).thenReturn(IFieldType.TEXT);

        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.filterOne(any())).thenReturn(Optional.of(textField));
            
            boolean result = RichTextExt.isRichTextAbstractField("content__o", mockObjectDescribe);
            
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseTextFromHtml方法
     */
    @Test
    @DisplayName("parseTextFromHtml - 解析HTML文本")
    void testParseTextFromHtml() {
        try (MockedStatic<HtmlUtils> htmlUtilsMock = mockStatic(HtmlUtils.class)) {
            htmlUtilsMock.when(() -> HtmlUtils.parseText("<p>Hello World</p>")).thenReturn("Hello World");
            
            String result = RichTextExt.parseTextFromHtml("<p>Hello World</p>");
            
            assertEquals("Hello World", result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseTextFromHtml方法 - 空HTML
     */
    @Test
    @DisplayName("parseTextFromHtml - 空HTML")
    void testParseTextFromHtml_EmptyHtml() {
        try (MockedStatic<HtmlUtils> htmlUtilsMock = mockStatic(HtmlUtils.class)) {
            htmlUtilsMock.when(() -> HtmlUtils.parseText("")).thenReturn("");
            htmlUtilsMock.when(() -> HtmlUtils.parseText(null)).thenReturn(null);
            
            String result1 = RichTextExt.parseTextFromHtml("");
            assertEquals("", result1);
            
            String result2 = RichTextExt.parseTextFromHtml(null);
            assertNull(result2);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseTextFromHtml方法 - 复杂HTML
     */
    @Test
    @DisplayName("parseTextFromHtml - 复杂HTML")
    void testParseTextFromHtml_ComplexHtml() {
        try (MockedStatic<HtmlUtils> htmlUtilsMock = mockStatic(HtmlUtils.class)) {
            String complexHtml = "<div><p>Hello</p><span>World</span></div>";
            htmlUtilsMock.when(() -> HtmlUtils.parseText(complexHtml)).thenReturn("HelloWorld");
            
            String result = RichTextExt.parseTextFromHtml(complexHtml);
            
            assertEquals("HelloWorld", result);
        }
    }
}
