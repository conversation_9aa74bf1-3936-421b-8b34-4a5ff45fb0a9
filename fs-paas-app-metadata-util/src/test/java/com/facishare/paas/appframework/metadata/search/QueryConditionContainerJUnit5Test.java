package com.facishare.paas.appframework.metadata.search;

import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * QueryConditionContainer的JUnit 5测试类
 * 测试查询条件容器的基本功能
 */
@ExtendWith(MockitoExtension.class)
class QueryConditionContainerJUnit5Test {

    private QueryConditionContainer queryConditionContainer;

    @BeforeEach
    void setUp() {
        queryConditionContainer = new QueryConditionContainer();
    }

    // ==================== 构造函数和初始状态测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试默认构造函数初始化
     */
    @Test
    @DisplayName("构造函数 - 默认初始化")
    void testDefaultConstructor() {
        // 验证初始状态
        assertNotNull(queryConditionContainer.getFilters());
        assertNotNull(queryConditionContainer.getWheres());
        assertTrue(queryConditionContainer.getFilters().isEmpty());
        assertTrue(queryConditionContainer.getWheres().isEmpty());
    }

    // ==================== addFilter方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试添加有效过滤器
     */
    @Test
    @DisplayName("addFilter - 添加有效过滤器")
    void testAddFilter_ValidFilter() {
        // 准备测试数据
        IFilter filter = new Filter();
        
        // 执行测试
        queryConditionContainer.addFilter(filter);
        
        // 验证结果
        assertEquals(1, queryConditionContainer.getFilters().size());
        assertSame(filter, queryConditionContainer.getFilters().get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加null过滤器
     */
    @Test
    @DisplayName("addFilter - 添加null过滤器")
    void testAddFilter_NullFilter() {
        // 先添加一个有效过滤器
        IFilter validFilter = new Filter();
        queryConditionContainer.addFilter(validFilter);
        assertEquals(1, queryConditionContainer.getFilters().size());
        
        // 执行测试 - 添加null过滤器
        queryConditionContainer.addFilter(null);
        
        // 验证结果 - 根据代码逻辑，null会导致filters被重新初始化，然后添加null
        assertEquals(1, queryConditionContainer.getFilters().size());
        assertNull(queryConditionContainer.getFilters().get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加多个过滤器
     */
    @Test
    @DisplayName("addFilter - 添加多个过滤器")
    void testAddFilter_MultipleFilters() {
        // 准备测试数据
        IFilter filter1 = new Filter();
        IFilter filter2 = new Filter();
        IFilter filter3 = new Filter();
        
        // 执行测试
        queryConditionContainer.addFilter(filter1);
        queryConditionContainer.addFilter(filter2);
        queryConditionContainer.addFilter(filter3);
        
        // 验证结果
        assertEquals(3, queryConditionContainer.getFilters().size());
        assertSame(filter1, queryConditionContainer.getFilters().get(0));
        assertSame(filter2, queryConditionContainer.getFilters().get(1));
        assertSame(filter3, queryConditionContainer.getFilters().get(2));
    }

    // ==================== addWheres方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试添加有效的Wheres列表
     */
    @Test
    @DisplayName("addWheres - 添加有效的Wheres列表")
    void testAddWheres_ValidWheresList() {
        // 准备测试数据
        Wheres wheres1 = new Wheres();
        Wheres wheres2 = new Wheres();
        List<Wheres> wheresList = Lists.newArrayList(wheres1, wheres2);
        
        // 执行测试
        queryConditionContainer.addWheres(wheresList);
        
        // 验证结果
        assertEquals(2, queryConditionContainer.getWheres().size());
        assertSame(wheres1, queryConditionContainer.getWheres().get(0));
        assertSame(wheres2, queryConditionContainer.getWheres().get(1));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加空的Wheres列表
     */
    @Test
    @DisplayName("addWheres - 添加空的Wheres列表")
    void testAddWheres_EmptyWheresList() {
        // 准备测试数据
        List<Wheres> emptyList = Lists.newArrayList();
        
        // 执行测试
        queryConditionContainer.addWheres(emptyList);
        
        // 验证结果
        assertTrue(queryConditionContainer.getWheres().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多次添加Wheres列表
     */
    @Test
    @DisplayName("addWheres - 多次添加Wheres列表")
    void testAddWheres_MultipleAdditions() {
        // 准备测试数据
        Wheres wheres1 = new Wheres();
        Wheres wheres2 = new Wheres();
        Wheres wheres3 = new Wheres();
        
        List<Wheres> firstList = Lists.newArrayList(wheres1, wheres2);
        List<Wheres> secondList = Lists.newArrayList(wheres3);
        
        // 执行测试
        queryConditionContainer.addWheres(firstList);
        queryConditionContainer.addWheres(secondList);
        
        // 验证结果
        assertEquals(3, queryConditionContainer.getWheres().size());
        assertSame(wheres1, queryConditionContainer.getWheres().get(0));
        assertSame(wheres2, queryConditionContainer.getWheres().get(1));
        assertSame(wheres3, queryConditionContainer.getWheres().get(2));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当wheres为null时添加Wheres列表
     */
    @Test
    @DisplayName("addWheres - wheres为null时添加")
    void testAddWheres_WhenWheresIsNull() {
        // 准备测试数据 - 手动设置wheres为null
        queryConditionContainer.setWheres(null);
        
        Wheres wheres1 = new Wheres();
        List<Wheres> wheresList = Lists.newArrayList(wheres1);
        
        // 执行测试
        queryConditionContainer.addWheres(wheresList);
        
        // 验证结果
        assertNotNull(queryConditionContainer.getWheres());
        assertEquals(1, queryConditionContainer.getWheres().size());
        assertSame(wheres1, queryConditionContainer.getWheres().get(0));
    }

    // ==================== Getter/Setter方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试setFilters方法
     */
    @Test
    @DisplayName("Getter/Setter - setFilters方法")
    void testSetFilters() {
        // 准备测试数据
        IFilter filter1 = new Filter();
        IFilter filter2 = new Filter();
        List<IFilter> filtersList = Lists.newArrayList(filter1, filter2);
        
        // 执行测试
        queryConditionContainer.setFilters(filtersList);
        
        // 验证结果
        assertSame(filtersList, queryConditionContainer.getFilters());
        assertEquals(2, queryConditionContainer.getFilters().size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setWheres方法
     */
    @Test
    @DisplayName("Getter/Setter - setWheres方法")
    void testSetWheres() {
        // 准备测试数据
        Wheres wheres1 = new Wheres();
        Wheres wheres2 = new Wheres();
        List<Wheres> wheresList = Lists.newArrayList(wheres1, wheres2);
        
        // 执行测试
        queryConditionContainer.setWheres(wheresList);
        
        // 验证结果
        assertSame(wheresList, queryConditionContainer.getWheres());
        assertEquals(2, queryConditionContainer.getWheres().size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置null值
     */
    @Test
    @DisplayName("Getter/Setter - 设置null值")
    void testSetNullValues() {
        // 执行测试
        queryConditionContainer.setFilters(null);
        queryConditionContainer.setWheres(null);
        
        // 验证结果
        assertNull(queryConditionContainer.getFilters());
        assertNull(queryConditionContainer.getWheres());
    }
}
