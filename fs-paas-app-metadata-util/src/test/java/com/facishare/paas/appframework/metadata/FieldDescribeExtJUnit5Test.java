package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.powermock.reflect.Whitebox;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FieldDescribeExt JUnit5 单元测试类
 *
 * GenerateByAI
 *
 * 测试目标：
 * - 提高FieldDescribeExt类的代码覆盖率
 * - 验证字段描述扩展功能的正确性
 * - 确保字段类型判断逻辑的准确性
 *
 * 测试范围：
 * - 工厂方法测试（of方法）
 * - 字段类型判断方法（isTextType、isSelectField等）
 * - 静态工具方法（isUseRangeField、isMultiLangExtraField等）
 * - 数据转换方法（toMap、copyOnWrite等）
 * - 字段属性访问方法
 * - 边界条件和异常处理测试
 *
 * 测试策略：
 * - 使用FieldDescribeFactory创建真实字段描述对象
 * - 测试各种字段类型的判断逻辑
 * - 覆盖正常场景、边界条件和异常情况
 *
 * 覆盖率目标：80%以上
 */
class FieldDescribeExtJUnit5Test {

    private IFieldDescribe mockFieldDescribe;
    private FieldDescribeExt fieldDescribeExt;

    @BeforeEach
    void setUp() {
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "test_field");
        fieldMap.put("type", IFieldType.TEXT);
        fieldMap.put("label", "Test Field");
        mockFieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        fieldDescribeExt = FieldDescribeExt.of(mockFieldDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法 - IFieldDescribe参数
     */
    @Test
    @DisplayName("of - IFieldDescribe参数")
    void testOf_WithFieldDescribe() {
        FieldDescribeExt result = FieldDescribeExt.of(mockFieldDescribe);

        assertNotNull(result);
        assertSame(mockFieldDescribe, result.getFieldDescribe());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法 - Map参数
     */
    @Test
    @DisplayName("of - Map参数")
    void testOf_WithMap() {
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "map_field");
        fieldMap.put("type", IFieldType.NUMBER);

        FieldDescribeExt result = FieldDescribeExt.of(fieldMap);

        assertNotNull(result);
        assertEquals("map_field", result.getApiName());
        assertEquals(IFieldType.NUMBER, result.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLookupNameByFieldName静态方法
     */
    @Test
    @DisplayName("getLookupNameByFieldName - 生成查找字段名")
    void testGetLookupNameByFieldName() {
        String result = FieldDescribeExt.getLookupNameByFieldName("test_field");
        
        assertEquals("test_field__r", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLocalizationNameByFieldName静态方法
     */
    @Test
    @DisplayName("getLocalizationNameByFieldName - 生成本地化字段名")
    void testGetLocalizationNameByFieldName() {
        String result = FieldDescribeExt.getLocalizationNameByFieldName("test_field");
        
        assertEquals("test_field__geo", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldMappingFieldName静态方法
     */
    @Test
    @DisplayName("getFieldMappingFieldName - 生成字段映射名")
    void testGetFieldMappingFieldName() {
        String result = FieldDescribeExt.getFieldMappingFieldName("test_field");
        
        assertEquals("test_field__wrap", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getEmployeeNameByFieldName静态方法
     */
    @Test
    @DisplayName("getEmployeeNameByFieldName - 生成员工字段名")
    void testGetEmployeeNameByFieldName() {
        String result = FieldDescribeExt.getEmployeeNameByFieldName("employee_field");
        
        assertEquals("employee_field__l", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSelectOther静态方法
     */
    @Test
    @DisplayName("getSelectOther - 生成选择其他字段名")
    void testGetSelectOther() {
        String result = FieldDescribeExt.getSelectOther("select_field");
        
        assertEquals("select_field__o", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getQuotedValueNameByFieldName静态方法
     */
    @Test
    @DisplayName("getQuotedValueNameByFieldName - 生成引用值字段名")
    void testGetQuotedValueNameByFieldName() {
        String result = FieldDescribeExt.getQuotedValueNameByFieldName("quote_field");
        
        assertEquals("quote_field__v", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getQuotedFunctionVirtualFieldByFieldName静态方法
     */
    @Test
    @DisplayName("getQuotedFunctionVirtualFieldByFieldName - 生成引用函数虚拟字段名")
    void testGetQuotedFunctionVirtualFieldByFieldName() {
        String result = FieldDescribeExt.getQuotedFunctionVirtualFieldByFieldName("lookup_field");
        
        assertEquals("lookup_field__q", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getShowFieldName静态方法
     */
    @Test
    @DisplayName("getShowFieldName - 生成显示字段名")
    void testGetShowFieldName() {
        String result = FieldDescribeExt.getShowFieldName("field");
        
        assertEquals("field__s", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMultiLangExtraFieldName静态方法
     */
    @Test
    @DisplayName("getMultiLangExtraFieldName - 生成多语言额外字段名")
    void testGetMultiLangExtraFieldName() {
        String result = FieldDescribeExt.getMultiLangExtraFieldName("field");
        
        assertEquals("field__lang", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMultiLangTempFieldName静态方法
     */
    @Test
    @DisplayName("getMultiLangTempFieldName - 生成多语言临时字段名")
    void testGetMultiLangTempFieldName() {
        String result = FieldDescribeExt.getMultiLangTempFieldName("field", Lang.zh_CN);
        
        assertEquals("field__lang_zh-CN", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMaskEncryptFieldName静态方法
     */
    @Test
    @DisplayName("getMaskEncryptFieldName - 生成掩码加密字段名")
    void testGetMaskEncryptFieldName() {
        String result = FieldDescribeExt.getMaskEncryptFieldName("field");
        
        assertEquals("field__encrypt", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOldFieldName静态方法
     */
    @Test
    @DisplayName("getOldFieldName - 生成旧字段名")
    void testGetOldFieldName() {
        String result = FieldDescribeExt.getOldFieldName("field");
        
        assertEquals("field__old", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getChangeFieldName静态方法
     */
    @Test
    @DisplayName("getChangeFieldName - 生成变更字段名")
    void testGetChangeFieldName() {
        String result = FieldDescribeExt.getChangeFieldName("field");
        
        assertEquals("change_field", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOriginalFieldName静态方法
     */
    @Test
    @DisplayName("getOriginalFieldName - 生成原始字段名")
    void testGetOriginalFieldName() {
        String result = FieldDescribeExt.getOriginalFieldName("field");
        
        assertEquals("original_field", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getTeamMemberFieldApiName静态方法
     */
    @Test
    @DisplayName("getTeamMemberFieldApiName - 生成团队成员字段API名")
    void testGetTeamMemberFieldApiName() {
        String result = FieldDescribeExt.getTeamMemberFieldApiName("member", "admin", "read");
        
        assertEquals("member_admin_read", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isOriginalFieldName静态方法 - 原始字段
     */
    @Test
    @DisplayName("isOriginalFieldName - 原始字段")
    void testIsOriginalFieldName_True() {
        boolean result = FieldDescribeExt.isOriginalFieldName("original_field");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isOriginalFieldName静态方法 - 非原始字段
     */
    @Test
    @DisplayName("isOriginalFieldName - 非原始字段")
    void testIsOriginalFieldName_False() {
        boolean result = FieldDescribeExt.isOriginalFieldName("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isOriginalFieldName静态方法 - 特殊原始数据字段
     */
    @Test
    @DisplayName("isOriginalFieldName - 特殊原始数据字段")
    void testIsOriginalFieldName_SpecialOriginalData() {
        boolean result1 = FieldDescribeExt.isOriginalFieldName("original_data");
        boolean result2 = FieldDescribeExt.isOriginalFieldName("original_detail_data");
        
        assertFalse(result1);
        assertFalse(result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCurrencyFieldName静态方法
     */
    @Test
    @DisplayName("getCurrencyFieldName - 生成货币字段名")
    void testGetCurrencyFieldName() {
        String result = FieldDescribeExt.getCurrencyFieldName("amount");
        
        assertEquals("currency_amount", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isChangeFieldName静态方法 - 变更字段
     */
    @Test
    @DisplayName("isChangeFieldName - 变更字段")
    void testIsChangeFieldName_True() {
        boolean result = FieldDescribeExt.isChangeFieldName("change_field");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isChangeFieldName静态方法 - 非变更字段
     */
    @Test
    @DisplayName("isChangeFieldName - 非变更字段")
    void testIsChangeFieldName_False() {
        boolean result = FieldDescribeExt.isChangeFieldName("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldNameByChangeField静态方法 - 有效变更字段
     */
    @Test
    @DisplayName("getFieldNameByChangeField - 有效变更字段")
    void testGetFieldNameByChangeField_Valid() {
        String result = FieldDescribeExt.getFieldNameByChangeField("change_field_name");
        
        assertEquals("field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldNameByChangeField静态方法 - 无效变更字段
     */
    @Test
    @DisplayName("getFieldNameByChangeField - 无效变更字段")
    void testGetFieldNameByChangeField_Invalid() {
        String result = FieldDescribeExt.getFieldNameByChangeField("normal_field");
        
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOriginalFieldNameByChangeField静态方法
     */
    @Test
    @DisplayName("getOriginalFieldNameByChangeField - 从变更字段获取原始字段名")
    void testGetOriginalFieldNameByChangeField() {
        String result = FieldDescribeExt.getOriginalFieldNameByChangeField("change_field_name");
        
        assertEquals("original_field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectField静态方法 - 单选字段
     */
    @Test
    @DisplayName("isSelectField - 单选字段")
    void testIsSelectField_SelectOne() {
        boolean result = FieldDescribeExt.isSelectField(IFieldType.SELECT_ONE);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectField静态方法 - 多选字段
     */
    @Test
    @DisplayName("isSelectField - 多选字段")
    void testIsSelectField_SelectMany() {
        boolean result = FieldDescribeExt.isSelectField(IFieldType.SELECT_MANY);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectField静态方法 - 非选择字段
     */
    @Test
    @DisplayName("isSelectField - 非选择字段")
    void testIsSelectField_NotSelect() {
        boolean result = FieldDescribeExt.isSelectField(IFieldType.TEXT);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectOtherField静态方法 - 选择其他字段
     */
    @Test
    @DisplayName("isSelectOtherField - 选择其他字段")
    void testIsSelectOtherField_True() {
        boolean result = FieldDescribeExt.isSelectOtherField("field__o");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectOtherField静态方法 - 非选择其他字段
     */
    @Test
    @DisplayName("isSelectOtherField - 非选择其他字段")
    void testIsSelectOtherField_False() {
        boolean result = FieldDescribeExt.isSelectOtherField("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiLangExtraField静态方法 - 多语言额外字段
     */
    @Test
    @DisplayName("isMultiLangExtraField - 多语言额外字段")
    void testIsMultiLangExtraField_True() {
        boolean result = FieldDescribeExt.isMultiLangExtraField("field__lang");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiLangExtraField静态方法 - 非多语言额外字段
     */
    @Test
    @DisplayName("isMultiLangExtraField - 非多语言额外字段")
    void testIsMultiLangExtraField_False() {
        boolean result = FieldDescribeExt.isMultiLangExtraField("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaskEncryptField静态方法 - 掩码加密字段
     */
    @Test
    @DisplayName("isMaskEncryptField - 掩码加密字段")
    void testIsMaskEncryptField_True() {
        boolean result = FieldDescribeExt.isMaskEncryptField("field__encrypt");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaskEncryptField静态方法 - 非掩码加密字段
     */
    @Test
    @DisplayName("isMaskEncryptField - 非掩码加密字段")
    void testIsMaskEncryptField_False() {
        boolean result = FieldDescribeExt.isMaskEncryptField("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaskShowFieldName静态方法 - 掩码显示字段
     */
    @Test
    @DisplayName("isMaskShowFieldName - 掩码显示字段")
    void testIsMaskShowFieldName_True() {
        boolean result = FieldDescribeExt.isMaskShowFieldName("field__s");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaskShowFieldName静态方法 - 非掩码显示字段
     */
    @Test
    @DisplayName("isMaskShowFieldName - 非掩码显示字段")
    void testIsMaskShowFieldName_False() {
        boolean result = FieldDescribeExt.isMaskShowFieldName("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMultiLangFieldFromExtraField静态方法
     */
    @Test
    @DisplayName("getMultiLangFieldFromExtraField - 从多语言额外字段获取原字段")
    void testGetMultiLangFieldFromExtraField() {
        String result = FieldDescribeExt.getMultiLangFieldFromExtraField("field_name__lang");
        
        assertEquals("field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSelectFieldFromOtherField静态方法
     */
    @Test
    @DisplayName("getSelectFieldFromOtherField - 从其他字段获取选择字段")
    void testGetSelectFieldFromOtherField() {
        String result = FieldDescribeExt.getSelectFieldFromOtherField("field_name__o");
        
        assertEquals("field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldNameFromMaskEncryptFieldName静态方法
     */
    @Test
    @DisplayName("getFieldNameFromMaskEncryptFieldName - 从掩码加密字段获取原字段名")
    void testGetFieldNameFromMaskEncryptFieldName() {
        String result = FieldDescribeExt.getFieldNameFromMaskEncryptFieldName("field_name__encrypt");
        
        assertEquals("field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getReferenceLabelI18NKey静态方法
     */
    @Test
    @DisplayName("getReferenceLabelI18NKey - 生成引用标签国际化键")
    void testGetReferenceLabelI18NKey() {
        String result = FieldDescribeExt.getReferenceLabelI18NKey("TestObject", "test_field");
        
        assertEquals("TestObject.field.test_field.reference_label", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试generateTargetRelatedListName静态方法
     */
    @Test
    @DisplayName("generateTargetRelatedListName - 生成目标关联列表名")
    void testGenerateTargetRelatedListName() {
        String result1 = FieldDescribeExt.generateTargetRelatedListName();
        String result2 = FieldDescribeExt.generateTargetRelatedListName();
        
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotEquals(result1, result2); // 应该生成不同的名称
        assertTrue(result1.startsWith("target_related_list_"));
        assertTrue(result1.endsWith("__c"));
        assertTrue(result2.startsWith("target_related_list_"));
        assertTrue(result2.endsWith("__c"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isCurrencyOrNumberType静态方法 - 货币类型
     */
    @Test
    @DisplayName("isCurrencyOrNumberType - 货币类型")
    void testIsCurrencyOrNumberType_Currency() {
        boolean result = FieldDescribeExt.isCurrencyOrNumberType(IFieldType.CURRENCY);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isCurrencyOrNumberType静态方法 - 数字类型
     */
    @Test
    @DisplayName("isCurrencyOrNumberType - 数字类型")
    void testIsCurrencyOrNumberType_Number() {
        boolean result = FieldDescribeExt.isCurrencyOrNumberType(IFieldType.NUMBER);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isCurrencyOrNumberType静态方法 - 其他类型
     */
    @Test
    @DisplayName("isCurrencyOrNumberType - 其他类型")
    void testIsCurrencyOrNumberType_Other() {
        boolean result = FieldDescribeExt.isCurrencyOrNumberType(IFieldType.TEXT);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiCurrencyFields静态方法 - 多货币字段
     */
    @Test
    @DisplayName("isMultiCurrencyFields - 多货币字段")
    void testIsMultiCurrencyFields_True() {
        boolean result1 = FieldDescribeExt.isMultiCurrencyFields("mc_currency");
        boolean result2 = FieldDescribeExt.isMultiCurrencyFields("mc_exchange_rate");
        
        assertTrue(result1);
        assertTrue(result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiCurrencyFields静态方法 - 非多货币字段
     */
    @Test
    @DisplayName("isMultiCurrencyFields - 非多货币字段")
    void testIsMultiCurrencyFields_False() {
        boolean result = FieldDescribeExt.isMultiCurrencyFields("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiCurrencyCalculateFields静态方法
     */
    @Test
    @DisplayName("isMultiCurrencyCalculateFields - 多货币计算字段")
    void testIsMultiCurrencyCalculateFields() {
        boolean result1 = FieldDescribeExt.isMultiCurrencyCalculateFields("mc_exchange_rate");
        boolean result2 = FieldDescribeExt.isMultiCurrencyCalculateFields("mc_exchange_rate_version");
        boolean result3 = FieldDescribeExt.isMultiCurrencyCalculateFields("mc_functional_currency");
        boolean result4 = FieldDescribeExt.isMultiCurrencyCalculateFields("normal_field");

        assertTrue(result1);
        assertTrue(result2);
        assertTrue(result3);
        assertFalse(result4);
    }

    // ==================== 新增测试方法 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试isWhereRelation实例方法 - 有关联条件
     */
    @Test
    @DisplayName("实例方法 - isWhereRelation有关联条件")
    void testIsWhereRelation_True() {
        // 准备测试数据 - 模拟有wheres的字段
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "test_field");
        fieldMap.put("type", IFieldType.OBJECT_REFERENCE);
        fieldMap.put("wheres", Lists.newArrayList(Maps.newHashMap()));

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isWhereRelation();

        // 验证结果
        assertTrue(result);
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试isUseRangeField静态方法 - 使用范围字段
     */
    @Test
    @DisplayName("静态方法 - isUseRangeField使用范围字段")
    void testIsUseRangeField_True() {
        // 执行测试
        boolean result = FieldDescribeExt.isUseRangeField(IFieldType.UseRange);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isUseRangeField静态方法 - 非使用范围字段
     */
    @Test
    @DisplayName("静态方法 - isUseRangeField非使用范围字段")
    void testIsUseRangeField_False() {
        // 执行测试
        boolean result = FieldDescribeExt.isUseRangeField(IFieldType.TEXT);

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isTrueOrFalseField实例方法 - 布尔字段
     */
    @Test
    @DisplayName("实例方法 - isTrueOrFalseField布尔字段")
    void testIsTrueOrFalseField_True() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "boolean_field");
        fieldMap.put("type", IFieldType.TRUE_OR_FALSE);

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isTrueOrFalseField();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toMap实例方法
     */
    @Test
    @DisplayName("实例方法 - toMap转换为Map")
    void testToMap_Success() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "test_field");
        fieldMap.put("type", IFieldType.TEXT);
        fieldMap.put("label", "Test Field");

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        Map<String, Object> result = fieldDescribeExt.toMap();

        // 验证结果
        assertNotNull(result);
        assertEquals("test_field", result.get("api_name"));
        assertEquals(IFieldType.TEXT, result.get("type"));
        assertEquals("Test Field", result.get("label"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试copyOnWrite实例方法
     */
    @Test
    @DisplayName("实例方法 - copyOnWrite写时复制")
    void testCopyOnWrite_Success() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "test_field");
        fieldMap.put("type", IFieldType.TEXT);
        fieldMap.put("label", "Test Field");

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        IFieldDescribe result = fieldDescribeExt.copyOnWrite();

        // 验证结果
        assertNotNull(result);
        assertEquals("test_field", result.getApiName());
        assertEquals(IFieldType.TEXT, result.getType());
        assertEquals("Test Field", result.getLabel());
        // 验证是不同的实例
        assertNotSame(fieldDescribe, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isTextType实例方法 - 文本类型
     */
    @Test
    @DisplayName("实例方法 - isTextType文本类型")
    void testIsTextType_True() {
        // 准备测试数据 - TEXT类型
        Map<String, Object> textFieldMap = Maps.newHashMap();
        textFieldMap.put("api_name", "text_field");
        textFieldMap.put("type", IFieldType.TEXT);

        IFieldDescribe textFieldDescribe = FieldDescribeFactory.newInstance(textFieldMap);
        FieldDescribeExt textFieldExt = FieldDescribeExt.of(textFieldDescribe);

        // 准备测试数据 - LONG_TEXT类型
        Map<String, Object> longTextFieldMap = Maps.newHashMap();
        longTextFieldMap.put("api_name", "long_text_field");
        longTextFieldMap.put("type", IFieldType.LONG_TEXT);

        IFieldDescribe longTextFieldDescribe = FieldDescribeFactory.newInstance(longTextFieldMap);
        FieldDescribeExt longTextFieldExt = FieldDescribeExt.of(longTextFieldDescribe);

        // 执行测试
        boolean textResult = textFieldExt.isTextType();
        boolean longTextResult = longTextFieldExt.isTextType();

        // 验证结果
        assertTrue(textResult);
        assertTrue(longTextResult);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isTextType实例方法 - 非文本类型
     */
    @Test
    @DisplayName("实例方法 - isTextType非文本类型")
    void testIsTextType_False() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "number_field");
        fieldMap.put("type", IFieldType.NUMBER);

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isTextType();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isText实例方法 - 文本字段
     */
    @Test
    @DisplayName("实例方法 - isText文本字段")
    void testIsText_True() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "text_field");
        fieldMap.put("type", IFieldType.TEXT);

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isText();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isText实例方法 - 非文本字段
     */
    @Test
    @DisplayName("实例方法 - isText非文本字段")
    void testIsText_False() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "number_field");
        fieldMap.put("type", IFieldType.NUMBER);

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isText();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isTrueOrFalseField实例方法 - 非布尔字段
     */
    @Test
    @DisplayName("实例方法 - isTrueOrFalseField非布尔字段")
    void testIsTrueOrFalseField_False() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "text_field");
        fieldMap.put("type", IFieldType.TEXT);

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isTrueOrFalseField();

        // 验证结果
        assertFalse(result);
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试isDateField实例方法 - 日期字段
     */
    @Test
    @DisplayName("实例方法 - isDateField日期字段")
    void testIsDateField_True() {
        // 准备测试数据 - DATE类型
        Map<String, Object> dateFieldMap = Maps.newHashMap();
        dateFieldMap.put("api_name", "date_field");
        dateFieldMap.put("type", IFieldType.DATE);

        IFieldDescribe dateFieldDescribe = FieldDescribeFactory.newInstance(dateFieldMap);
        FieldDescribeExt dateFieldExt = FieldDescribeExt.of(dateFieldDescribe);

        // 执行测试
        boolean result = dateFieldExt.isDateField();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDateField实例方法 - 非日期字段
     */
    @Test
    @DisplayName("实例方法 - isDateField非日期字段")
    void testIsDateField_False() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "text_field");
        fieldMap.put("type", IFieldType.TEXT);

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isDateField();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectField实例方法 - 选择字段
     */
    @Test
    @DisplayName("实例方法 - isSelectField选择字段")
    void testIsSelectField_True() {
        // 准备测试数据 - SELECT_ONE类型
        Map<String, Object> selectFieldMap = Maps.newHashMap();
        selectFieldMap.put("api_name", "select_field");
        selectFieldMap.put("type", IFieldType.SELECT_ONE);

        IFieldDescribe selectFieldDescribe = FieldDescribeFactory.newInstance(selectFieldMap);
        FieldDescribeExt selectFieldExt = FieldDescribeExt.of(selectFieldDescribe);

        // 准备测试数据 - SELECT_MANY类型
        Map<String, Object> multiSelectFieldMap = Maps.newHashMap();
        multiSelectFieldMap.put("api_name", "multi_select_field");
        multiSelectFieldMap.put("type", IFieldType.SELECT_MANY);

        IFieldDescribe multiSelectFieldDescribe = FieldDescribeFactory.newInstance(multiSelectFieldMap);
        FieldDescribeExt multiSelectFieldExt = FieldDescribeExt.of(multiSelectFieldDescribe);

        // 执行测试
        boolean selectResult = selectFieldExt.isSelectField();
        boolean multiSelectResult = multiSelectFieldExt.isSelectField();

        // 验证结果
        assertTrue(selectResult);
        assertTrue(multiSelectResult);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectField实例方法 - 非选择字段
     */
    @Test
    @DisplayName("实例方法 - isSelectField非选择字段")
    void testIsSelectField_False() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "text_field");
        fieldMap.put("type", IFieldType.TEXT);

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isSelectField();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectOne实例方法 - 单选字段
     */
    @Test
    @DisplayName("实例方法 - isSelectOne单选字段")
    void testIsSelectOne_True() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "select_one_field");
        fieldMap.put("type", IFieldType.SELECT_ONE);

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isSelectOne();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectMany实例方法 - 多选字段
     */
    @Test
    @DisplayName("实例方法 - isSelectMany多选字段")
    void testIsSelectMany_True() {
        // 准备测试数据
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "select_many_field");
        fieldMap.put("type", IFieldType.SELECT_MANY);

        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);

        // 执行测试
        boolean result = fieldDescribeExt.isSelectMany();

        // 验证结果
        assertTrue(result);
    }
}
