package com.facishare.paas.appframework.metadata.dto;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Wheres的JUnit 5测试类
 * 测试查询条件DTO类功能
 */
class WheresJUnit5Test {

    private Wheres wheres;
    private Map<String, Object> testWheresMap;

    @BeforeEach
    void setUp() {
        testWheresMap = Maps.newHashMap();
        testWheresMap.put("field_name", "test_field");
        testWheresMap.put("operator", "eq");
        testWheresMap.put("value", "test_value");
        testWheresMap.put("connector", "AND");
        
        wheres = Wheres.of(testWheresMap);
    }

    /**
     * 测试内容描述：测试of静态方法
     */
    @Test
    @DisplayName("静态方法 - of方法")
    void testOf() {
        // Arrange: 准备测试数据
        Map<String, Object> wheresMap = Maps.newHashMap();
        wheresMap.put("field_name", "account_name");
        wheresMap.put("operator", "contains");
        wheresMap.put("value", "test");
        wheresMap.put("connector", "OR");
        
        // Act: 执行of方法
        Wheres result = Wheres.of(wheresMap);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("account_name", result.get("field_name"));
        assertEquals("contains", result.get("operator"));
        assertEquals("test", result.get("value"));
        assertEquals("OR", result.get("connector"));
    }

    /**
     * 测试内容描述：测试ofList静态方法
     */
    @Test
    @DisplayName("静态方法 - ofList方法")
    void testOfList() {
        // Arrange: 准备测试数据
        Map<String, Object> wheres1 = Maps.newHashMap();
        wheres1.put("field_name", "field1");
        wheres1.put("operator", "eq");
        wheres1.put("value", "value1");
        
        Map<String, Object> wheres2 = Maps.newHashMap();
        wheres2.put("field_name", "field2");
        wheres2.put("operator", "ne");
        wheres2.put("value", "value2");
        
        List<Map<String, Object>> wheresList = Lists.newArrayList(wheres1, wheres2);
        
        // Act: 执行ofList方法
        List<Wheres> result = Wheres.ofList(wheresList);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("field1", result.get(0).get("field_name"));
        assertEquals("field2", result.get(1).get("field_name"));
        
        // 验证每个Wheres都是独立的副本
        assertNotSame(result.get(0), result.get(1));
    }

    /**
     * 测试内容描述：测试ofList静态方法 - 空列表
     */
    @Test
    @DisplayName("边界条件 - ofList方法空列表")
    void testOfList_EmptyList() {
        // Act: 执行ofList方法
        List<Wheres> result = Wheres.ofList(Lists.newArrayList());
        
        // Assert: 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试内容描述：测试ofList静态方法 - null参数
     */
    @Test
    @DisplayName("边界条件 - ofList方法null参数")
    void testOfList_NullParameter() {
        // Act: 执行ofList方法
        List<Wheres> result = Wheres.ofList(null);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试内容描述：测试copy方法
     */
    @Test
    @DisplayName("复制方法 - copy方法")
    void testCopy() {
        // Arrange: 设置原始数据
        // wheres.put("additional_field", "additional_value"); // 注释掉，因为Wheres可能没有put方法
        
        // Act: 执行copy方法
        Wheres copiedWheres = wheres.copy();
        
        // Assert: 验证复制结果
        assertNotNull(copiedWheres);
        assertNotSame(wheres, copiedWheres);
        assertEquals(wheres.get("field_name"), copiedWheres.get("field_name"));
        assertEquals(wheres.get("operator"), copiedWheres.get("operator"));
        assertEquals(wheres.get("value"), copiedWheres.get("value"));
        // assertEquals(wheres.get("additional_field"), copiedWheres.get("additional_field")); // 注释掉相关测试
        
        // 验证修改复制对象不影响原对象
        // copiedWheres.put("field_name", "modified_field"); // 注释掉，因为Wheres可能没有put方法
        assertEquals("test_field", wheres.get("field_name"));
        // assertEquals("modified_field", copiedWheres.get("field_name")); // 注释掉相关测试
    }

    /**
     * 测试内容描述：测试继承关系
     */
    @Test
    @DisplayName("继承验证 - 继承关系验证")
    void testInheritance() {
        // Assert: 验证继承关系
        assertTrue(wheres instanceof com.facishare.paas.metadata.impl.DocumentBasedBean);
    }

    /**
     * 测试内容描述：测试序列化兼容性
     */
    @Test
    @DisplayName("序列化 - 序列化兼容性")
    void testSerializationCompatibility() {
        // Arrange: 设置测试数据
        Map<String, Object> originalMap = Maps.newHashMap();
        originalMap.put("field_name", "serialization_test");
        originalMap.put("operator", "ne");
        originalMap.put("value", "test_value");
        originalMap.put("connector", "AND");
        
        // Act: 通过序列化和反序列化
        Wheres originalWheres = Wheres.of(originalMap);
        String jsonString = originalWheres.toJsonString();
        
        Wheres deserializedWheres = Wheres.of(Maps.newHashMap());
        deserializedWheres.fromJsonString(jsonString);
        
        // Assert: 验证序列化兼容性
        assertEquals(originalWheres.get("field_name"), deserializedWheres.get("field_name"));
        assertEquals(originalWheres.get("operator"), deserializedWheres.get("operator"));
        assertEquals(originalWheres.get("value"), deserializedWheres.get("value"));
        assertEquals(originalWheres.get("connector"), deserializedWheres.get("connector"));
    }

    /**
     * 测试内容描述：测试业务场景 - 简单查询条件
     */
    @Test
    @DisplayName("业务场景 - 简单查询条件")
    void testSimpleQueryCondition() {
        // Arrange: 创建简单查询条件
        Map<String, Object> wheresMap = Maps.newHashMap();
        wheresMap.put("field_name", "status");
        wheresMap.put("operator", "eq");
        wheresMap.put("value", "active");
        wheresMap.put("connector", "AND");
        
        // Act: 创建查询条件
        Wheres simpleWheres = Wheres.of(wheresMap);
        
        // Assert: 验证简单查询条件
        assertEquals("status", simpleWheres.get("field_name"));
        assertEquals("eq", simpleWheres.get("operator"));
        assertEquals("active", simpleWheres.get("value"));
        assertEquals("AND", simpleWheres.get("connector"));
    }

    /**
     * 测试内容描述：测试业务场景 - 复杂查询条件
     */
    @Test
    @DisplayName("业务场景 - 复杂查询条件")
    void testComplexQueryCondition() {
        // Arrange: 创建复杂查询条件
        Map<String, Object> wheresMap = Maps.newHashMap();
        wheresMap.put("field_name", "amount");
        wheresMap.put("operator", "between");
        wheresMap.put("value", Lists.newArrayList(100, 1000));
        wheresMap.put("connector", "OR");
        Map<String, Object> nestedCondition = Maps.newHashMap();
        nestedCondition.put("field_name", "category");
        nestedCondition.put("operator", "in");
        nestedCondition.put("value", Lists.newArrayList("A", "B", "C"));
        wheresMap.put("nested_conditions", Lists.newArrayList(nestedCondition));
        
        // Act: 创建复杂查询条件
        Wheres complexWheres = Wheres.of(wheresMap);
        
        // Assert: 验证复杂查询条件
        assertEquals("amount", complexWheres.get("field_name"));
        assertEquals("between", complexWheres.get("operator"));
        assertEquals(Lists.newArrayList(100, 1000), complexWheres.get("value"));
        assertEquals("OR", complexWheres.get("connector"));
        assertNotNull(complexWheres.get("nested_conditions"));
    }

    /**
     * 测试内容描述：测试业务场景 - 多条件组合
     */
    @Test
    @DisplayName("业务场景 - 多条件组合")
    void testMultipleConditionsCombination() {
        // Arrange: 创建多个查询条件
        Map<String, Object> condition1 = Maps.newHashMap();
        condition1.put("field_name", "name");
        condition1.put("operator", "contains");
        condition1.put("value", "test");
        condition1.put("connector", "AND");
        
        Map<String, Object> condition2 = Maps.newHashMap();
        condition2.put("field_name", "status");
        condition2.put("operator", "eq");
        condition2.put("value", "active");
        condition2.put("connector", "OR");
        
        Map<String, Object> condition3 = Maps.newHashMap();
        condition3.put("field_name", "created_date");
        condition3.put("operator", "gte");
        condition3.put("value", "2023-01-01");
        condition3.put("connector", "AND");
        
        List<Map<String, Object>> conditionsList = Lists.newArrayList(condition1, condition2, condition3);
        
        // Act: 创建多条件组合
        List<Wheres> multipleConditions = Wheres.ofList(conditionsList);
        
        // Assert: 验证多条件组合
        assertEquals(3, multipleConditions.size());
        
        // 验证第一个条件
        assertEquals("name", multipleConditions.get(0).get("field_name"));
        assertEquals("contains", multipleConditions.get(0).get("operator"));
        assertEquals("AND", multipleConditions.get(0).get("connector"));
        
        // 验证第二个条件
        assertEquals("status", multipleConditions.get(1).get("field_name"));
        assertEquals("eq", multipleConditions.get(1).get("operator"));
        assertEquals("OR", multipleConditions.get(1).get("connector"));
        
        // 验证第三个条件
        assertEquals("created_date", multipleConditions.get(2).get("field_name"));
        assertEquals("gte", multipleConditions.get(2).get("operator"));
        assertEquals("AND", multipleConditions.get(2).get("connector"));
    }

    /**
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Arrange: 设置初始数据
        String originalFieldName = "consistency_field";
        String originalOperator = "gt";
        Object originalValue = 100;
        String originalConnector = "AND";
        
        Map<String, Object> wheresMap = Maps.newHashMap();
        wheresMap.put("field_name", originalFieldName);
        wheresMap.put("operator", originalOperator);
        wheresMap.put("value", originalValue);
        wheresMap.put("connector", originalConnector);
        
        Wheres testWheres = Wheres.of(wheresMap);
        
        // Act: 获取数据
        String retrievedFieldName = (String) testWheres.get("field_name");
        String retrievedOperator = (String) testWheres.get("operator");
        Object retrievedValue = testWheres.get("value");
        String retrievedConnector = (String) testWheres.get("connector");
        
        // Assert: 验证数据一致性
        assertEquals(originalFieldName, retrievedFieldName);
        assertEquals(originalOperator, retrievedOperator);
        assertEquals(originalValue, retrievedValue);
        assertEquals(originalConnector, retrievedConnector);
    }

    /**
     * 测试内容描述：测试特殊字符处理
     */
    @Test
    @DisplayName("边界条件 - 特殊字符处理")
    void testSpecialCharacters() {
        // Arrange: 创建包含特殊字符的查询条件
        Map<String, Object> wheresMap = Maps.newHashMap();
        wheresMap.put("field_name", "field@#$%^&*()");
        wheresMap.put("operator", "contains");
        wheresMap.put("value", "value@#$%^&*()");
        wheresMap.put("connector", "AND");
        
        // Act: 创建查询条件
        Wheres specialWheres = Wheres.of(wheresMap);
        
        // Assert: 验证特殊字符处理
        assertEquals("field@#$%^&*()", specialWheres.get("field_name"));
        assertEquals("contains", specialWheres.get("operator"));
        assertEquals("value@#$%^&*()", specialWheres.get("value"));
        assertEquals("AND", specialWheres.get("connector"));
    }

    /**
     * 测试内容描述：测试null安全性
     */
    @Test
    @DisplayName("安全性验证 - null安全性验证")
    void testNullSafety() {
        // Arrange: 创建包含null值的查询条件
        Map<String, Object> wheresMap = Maps.newHashMap();
        wheresMap.put("field_name", null);
        wheresMap.put("operator", null);
        wheresMap.put("value", null);
        wheresMap.put("connector", null);
        
        // Act & Assert: 验证null安全性
        assertDoesNotThrow(() -> {
            Wheres nullWheres = Wheres.of(wheresMap);
            nullWheres.get("field_name");
            nullWheres.get("operator");
            nullWheres.get("value");
            nullWheres.get("connector");
            nullWheres.copy();
        });
    }

    /**
     * 测试内容描述：测试空Map处理
     */
    @Test
    @DisplayName("边界条件 - 空Map处理")
    void testEmptyMap() {
        // Arrange: 创建空Map
        Map<String, Object> emptyMap = Maps.newHashMap();
        
        // Act: 创建查询条件
        Wheres emptyWheres = Wheres.of(emptyMap);
        
        // Assert: 验证空Map处理
        assertNotNull(emptyWheres);
        assertNull(emptyWheres.get("field_name"));
        assertNull(emptyWheres.get("operator"));
        assertNull(emptyWheres.get("value"));
        assertNull(emptyWheres.get("connector"));
    }

    /**
     * 测试内容描述：测试复杂值类型
     */
    @Test
    @DisplayName("复杂场景 - 复杂值类型")
    void testComplexValueTypes() {
        // Arrange: 创建包含复杂值的查询条件
        Map<String, Object> complexValue = Maps.newHashMap();
        complexValue.put("nested_field", "nested_value");
        complexValue.put("nested_number", 123);
        complexValue.put("nested_list", Lists.newArrayList("item1", "item2"));
        
        Map<String, Object> wheresMap = Maps.newHashMap();
        wheresMap.put("field_name", "complex_field");
        wheresMap.put("operator", "eq");
        wheresMap.put("value", complexValue);
        wheresMap.put("connector", "AND");
        
        // Act: 创建查询条件
        Wheres complexWheres = Wheres.of(wheresMap);
        
        // Assert: 验证复杂值处理
        assertEquals("complex_field", complexWheres.get("field_name"));
        assertEquals("eq", complexWheres.get("operator"));
        assertEquals(complexValue, complexWheres.get("value"));
        assertEquals("AND", complexWheres.get("connector"));
    }

    /**
     * 测试内容描述：测试ofList中的copy行为
     */
    @Test
    @DisplayName("复制行为 - ofList中的copy行为验证")
    void testCopyBehaviorInOfList() {
        // Arrange: 创建原始数据
        Map<String, Object> originalMap = Maps.newHashMap();
        originalMap.put("field_name", "original_field");
        originalMap.put("operator", "eq");
        originalMap.put("value", "original_value");
        
        List<Map<String, Object>> originalList = Lists.newArrayList(originalMap);
        
        // Act: 执行ofList方法
        List<Wheres> result = Wheres.ofList(originalList);
        
        // 修改原始Map
        originalMap.put("field_name", "modified_field");
        
        // Assert: 验证copy行为
        assertEquals(1, result.size());
        // 由于ofList中调用了copy()，修改原始Map不应该影响结果
        assertEquals("original_field", result.get(0).get("field_name"));
        assertNotEquals("modified_field", result.get(0).get("field_name"));
    }
}
