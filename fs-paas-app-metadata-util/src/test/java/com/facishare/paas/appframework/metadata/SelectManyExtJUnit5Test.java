package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.SelectMany;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SelectManyExt的JUnit 5测试类
 * 测试多选字段扩展类功能
 * 
 * GenerateByAI
 * 测试内容描述：测试多选字段扩展的创建、选项处理和标签获取功能
 */
class SelectManyExtJUnit5Test {

    private SelectMany mockSelectMany;
    private SelectManyExt selectManyExt;
    private List<ISelectOption> selectOptions;

    @BeforeEach
    void setUp() {
        mockSelectMany = mock(SelectMany.class);
        selectManyExt = SelectManyExt.of(mockSelectMany);
        
        // 创建测试用的选项
        selectOptions = new ArrayList<>();
        
        ISelectOption option1 = mock(ISelectOption.class);
        when(option1.getValue()).thenReturn("value1");
        when(option1.getLabel()).thenReturn("Label 1");
        
        ISelectOption option2 = mock(ISelectOption.class);
        when(option2.getValue()).thenReturn("value2");
        when(option2.getLabel()).thenReturn("Label 2");
        
        ISelectOption option3 = mock(ISelectOption.class);
        when(option3.getValue()).thenReturn("value3");
        when(option3.getLabel()).thenReturn("Label 3");
        
        selectOptions.add(option1);
        selectOptions.add(option2);
        selectOptions.add(option3);
        
        when(mockSelectMany.getSelectOptions()).thenReturn(selectOptions);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("工厂方法 - of方法创建实例")
    void testOf() {
        // Act: 使用of方法创建实例
        SelectManyExt result = SelectManyExt.of(mockSelectMany);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertSame(mockSelectMany, result.getSelectMany());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelsByValues方法 - 基本功能
     */
    @Test
    @DisplayName("基本功能 - getLabelsByValues方法")
    void testGetLabelsByValues_BasicFunction() {
        // Arrange: 准备测试数据
        List<String> values = Lists.newArrayList("value1", "value3");
        
        // Act: 执行getLabelsByValues方法
        List<String> result = selectManyExt.getLabelsByValues(values);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("Label 1"));
        assertTrue(result.contains("Label 3"));
        assertFalse(result.contains("Label 2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelsByValues方法 - 空值列表
     */
    @Test
    @DisplayName("边界条件 - getLabelsByValues方法空值列表")
    void testGetLabelsByValues_EmptyValues() {
        // Act: 传入空列表
        List<String> result1 = selectManyExt.getLabelsByValues(Lists.newArrayList());
        List<String> result2 = selectManyExt.getLabelsByValues(null);
        
        // Assert: 验证返回空列表
        assertNotNull(result1);
        assertTrue(result1.isEmpty());
        
        assertNotNull(result2);
        assertTrue(result2.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelsByValues方法 - 不存在的值
     */
    @Test
    @DisplayName("边界条件 - getLabelsByValues方法不存在的值")
    void testGetLabelsByValues_NonExistentValues() {
        // Arrange: 准备不存在的值
        List<String> values = Lists.newArrayList("nonexistent1", "nonexistent2");
        
        // Act: 执行getLabelsByValues方法
        List<String> result = selectManyExt.getLabelsByValues(values);
        
        // Assert: 验证返回空列表
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelsByValues方法 - 混合存在和不存在的值
     */
    @Test
    @DisplayName("混合场景 - getLabelsByValues方法混合值")
    void testGetLabelsByValues_MixedValues() {
        // Arrange: 准备混合值（存在和不存在的）
        List<String> values = Lists.newArrayList("value1", "nonexistent", "value2");
        
        // Act: 执行getLabelsByValues方法
        List<String> result = selectManyExt.getLabelsByValues(values);
        
        // Assert: 验证只返回存在的标签
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("Label 1"));
        assertTrue(result.contains("Label 2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOptionType方法
     */
    @Test
    @DisplayName("选项类型 - getOptionType方法")
    void testGetOptionType() {
        // Arrange: 设置选项类型
        when(mockSelectMany.get(SelectMany.OPTION_TYPE, String.class)).thenReturn("custom");
        
        // Act: 执行getOptionType方法
        String result = selectManyExt.getOptionType();
        
        // Assert: 验证结果
        assertEquals("custom", result);
        verify(mockSelectMany).get(SelectMany.OPTION_TYPE, String.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOptionType方法 - null值
     */
    @Test
    @DisplayName("边界条件 - getOptionType方法null值")
    void testGetOptionType_NullValue() {
        // Arrange: 设置null选项类型
        when(mockSelectMany.get(SelectMany.OPTION_TYPE, String.class)).thenReturn(null);
        
        // Act: 执行getOptionType方法
        String result = selectManyExt.getOptionType();
        
        // Assert: 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托功能
     */
    @Test
    @DisplayName("委托功能 - 验证委托方法调用")
    void testDelegateFeatures() {
        // Arrange: 设置委托方法的返回值
        when(mockSelectMany.getApiName()).thenReturn("test_select_many");
        when(mockSelectMany.getLabel()).thenReturn("Test Select Many");
        when(mockSelectMany.isRequired()).thenReturn(true);
        
        // Act & Assert: 验证委托的方法
        assertEquals("test_select_many", selectManyExt.getApiName());
        assertEquals("Test Select Many", selectManyExt.getLabel());
        assertTrue(selectManyExt.isRequired());
        
        // 验证委托调用
        verify(mockSelectMany).getApiName();
        verify(mockSelectMany).getLabel();
        verify(mockSelectMany).isRequired();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数化值查找
     */
    @ParameterizedTest
    @ValueSource(strings = {"value1", "value2", "value3"})
    @DisplayName("参数化测试 - 单个值查找标签")
    void testGetLabelsByValues_SingleValue(String value) {
        // Arrange: 准备单个值的列表
        List<String> values = Lists.newArrayList(value);
        
        // Act: 执行getLabelsByValues方法
        List<String> result = selectManyExt.getLabelsByValues(values);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        // 根据值验证对应的标签
        switch (value) {
            case "value1":
                assertTrue(result.contains("Label 1"));
                break;
            case "value2":
                assertTrue(result.contains("Label 2"));
                break;
            case "value3":
                assertTrue(result.contains("Label 3"));
                break;
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试大量选项的性能
     */
    @Test
    @DisplayName("性能测试 - 大量选项处理")
    void testGetLabelsByValues_LargeOptions() {
        // Arrange: 创建大量选项
        List<ISelectOption> largeOptions = new ArrayList<>();
        List<String> testValues = new ArrayList<>();
        
        for (int i = 0; i < 1000; i++) {
            ISelectOption option = mock(ISelectOption.class);
            when(option.getValue()).thenReturn("value" + i);
            when(option.getLabel()).thenReturn("Label " + i);
            largeOptions.add(option);
            
            // 添加一些测试值
            if (i % 100 == 0) {
                testValues.add("value" + i);
            }
        }
        
        when(mockSelectMany.getSelectOptions()).thenReturn(largeOptions);
        
        // Act: 执行getLabelsByValues方法
        List<String> result = selectManyExt.getLabelsByValues(testValues);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(10, result.size()); // 应该有10个匹配的标签
        assertTrue(result.contains("Label 0"));
        assertTrue(result.contains("Label 900"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试选项为空的情况
     */
    @Test
    @DisplayName("边界条件 - 选项列表为空")
    void testGetLabelsByValues_EmptyOptions() {
        // Arrange: 设置空的选项列表
        when(mockSelectMany.getSelectOptions()).thenReturn(Lists.newArrayList());
        
        List<String> values = Lists.newArrayList("value1", "value2");
        
        // Act: 执行getLabelsByValues方法
        List<String> result = selectManyExt.getLabelsByValues(values);
        
        // Assert: 验证返回空列表
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试选项为null的情况
     */
    @Test
    @DisplayName("边界条件 - 选项列表为null")
    void testGetLabelsByValues_NullOptions() {
        // Arrange: 设置null的选项列表
        when(mockSelectMany.getSelectOptions()).thenReturn(null);
        
        List<String> values = Lists.newArrayList("value1", "value2");
        
        // Act & Assert: 验证会抛出异常或返回空列表
        assertThrows(NullPointerException.class, () -> {
            selectManyExt.getLabelsByValues(values);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重复值的处理
     */
    @Test
    @DisplayName("特殊场景 - 重复值处理")
    void testGetLabelsByValues_DuplicateValues() {
        // Arrange: 准备包含重复值的列表
        List<String> values = Lists.newArrayList("value1", "value1", "value2", "value2");

        // Act: 执行getLabelsByValues方法
        List<String> result = selectManyExt.getLabelsByValues(values);

        // Assert: 验证结果
        // 注意：SelectManyExt.getLabelsByValues实现中使用了Stream.filter，
        // 这会自动去重，所以结果中不会有重复的标签
        assertNotNull(result);
        assertEquals(2, result.size()); // 应该返回2个不同的标签

        // 验证包含预期的标签
        assertTrue(result.contains("Label 1"));
        assertTrue(result.contains("Label 2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试选项值为null的情况
     */
    @Test
    @DisplayName("边界条件 - 选项值为null")
    void testGetLabelsByValues_OptionWithNullValue() {
        // Arrange: 创建包含null值的选项
        ISelectOption nullOption = mock(ISelectOption.class);
        when(nullOption.getValue()).thenReturn(null);
        when(nullOption.getLabel()).thenReturn("Null Label");
        
        List<ISelectOption> optionsWithNull = Lists.newArrayList(selectOptions);
        optionsWithNull.add(nullOption);
        
        when(mockSelectMany.getSelectOptions()).thenReturn(optionsWithNull);
        
        List<String> values = Lists.newArrayList("value1", null);
        
        // Act: 执行getLabelsByValues方法
        List<String> result = selectManyExt.getLabelsByValues(values);
        
        // Assert: 验证结果
        assertNotNull(result);
        // 结果应该包含value1对应的标签，但null值的处理取决于具体实现
        assertTrue(result.contains("Label 1"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // Arrange: 设置toString返回值
        when(mockSelectMany.toString()).thenReturn("SelectMany[apiName=test]");
        
        // Act: 调用toString方法
        String result = selectManyExt.toString();
        
        // Assert: 验证toString结果
        assertNotNull(result);
        assertTrue(result.contains("SelectMany"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals和hashCode方法
     */
    @Test
    @DisplayName("对象比较 - equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Arrange: 创建两个使用相同SelectMany的实例
        SelectManyExt ext1 = SelectManyExt.of(mockSelectMany);
        SelectManyExt ext2 = SelectManyExt.of(mockSelectMany);
        
        // Assert: 验证equals和hashCode
        assertEquals(ext1.getSelectMany(), ext2.getSelectMany());
        
        // 创建使用不同SelectMany的实例
        SelectMany anotherSelectMany = mock(SelectMany.class);
        SelectManyExt ext3 = SelectManyExt.of(anotherSelectMany);
        
        assertNotEquals(ext1.getSelectMany(), ext3.getSelectMany());
    }
}
