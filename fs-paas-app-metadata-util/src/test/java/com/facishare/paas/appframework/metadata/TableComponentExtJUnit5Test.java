package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.facishare.paas.metadata.ui.layout.ITableComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * TableComponentExt的JUnit 5测试类
 * 测试表格组件扩展类功能
 */
class TableComponentExtJUnit5Test {

    private ITableComponent mockTableComponent;
    private ObjectDescribeExt mockObjectDescribeExt;
    private IObjectDescribe mockObjectDescribe;
    private TableComponentExt tableComponentExt;

    @BeforeEach
    void setUp() {
        mockTableComponent = mock(ITableComponent.class);
        mockObjectDescribeExt = mock(ObjectDescribeExt.class);
        mockObjectDescribe = mock(IObjectDescribe.class);

        // 设置基本的mock行为
        when(mockTableComponent.getName()).thenReturn("test_table");
        when(mockTableComponent.getType()).thenReturn(TableComponentExt.COMPONENT_TYPE_TABLE);

        tableComponentExt = TableComponentExt.of(mockTableComponent);
    }

    // 创建真实的TableComponent用于需要内部转换的测试
    private TableComponent createRealTableComponent() {
        Map<String, Object> componentMap = Maps.newHashMap();
        componentMap.put("name", "test_table");
        componentMap.put("type", "table");
        componentMap.put("is_new_layout", false);
        return new TableComponent(componentMap);
    }

    // 创建真实的TableComponent，包含字段列表
    private TableComponent createRealTableComponentWithFields(List<Map<String, Object>> fieldMaps) {
        TableComponent component = createRealTableComponent();
        component.set(ITableComponent.INCLUDE_FIELDS, fieldMaps);
        return component;
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法
     */
    @Test
    @DisplayName("静态工厂方法 - of方法创建实例")
    void testOf_CreateInstance() {
        // Act: 使用of方法创建实例
        TableComponentExt result = TableComponentExt.of(mockTableComponent);
        
        // Assert: 验证实例创建
        assertNotNull(result);
        assertEquals(mockTableComponent, result.getTableComponent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeFields方法 - 移除指定字段
     */
    @Test
    @DisplayName("字段管理 - removeFields移除指定字段")
    void testRemoveFields_RemoveSpecifiedFields() {
        // Arrange: 使用真实的TableComponent
        TableComponent realTableComponent = createRealTableComponentWithFields(Lists.newArrayList());

        // 创建字段列表，注意字段的结构需要符合TableColumnExt的期望
        List<Map<String, Object>> fieldMaps = Lists.newArrayList();
        Map<String, Object> field1 = Maps.newHashMap();
        field1.put("api_name", "field1"); // 使用api_name而不是name
        field1.put("name", "field1");
        Map<String, Object> field2 = Maps.newHashMap();
        field2.put("api_name", "field2");
        field2.put("name", "field2");
        Map<String, Object> field3 = Maps.newHashMap();
        field3.put("api_name", "field3");
        field3.put("name", "field3");

        fieldMaps.add(field1);
        fieldMaps.add(field2);
        fieldMaps.add(field3);

        realTableComponent.set(ITableComponent.INCLUDE_FIELDS, fieldMaps);

        TableComponentExt realTableComponentExt = TableComponentExt.of(realTableComponent);
        Set<String> fieldsToRemove = Sets.newHashSet("field1", "field3");

        // Act: 移除指定字段
        realTableComponentExt.removeFields(fieldsToRemove);

        // Assert: 验证字段被移除
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> remainingFields = (List<Map<String, Object>>) realTableComponent.get(ITableComponent.INCLUDE_FIELDS);
        assertEquals(1, remainingFields.size());
        assertEquals("field2", remainingFields.get(0).get("api_name"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeFieldByTypes方法 - 根据类型移除字段
     */
    @Test
    @DisplayName("字段管理 - removeFieldByTypes根据类型移除字段")
    void testRemoveFieldByTypes_RemoveByFieldType() {
        // Arrange: 使用真实的TableComponent
        TableComponent realTableComponent = createRealTableComponent();

        // 创建字段列表，使用正确的字段结构
        List<Map<String, Object>> fieldMaps = Lists.newArrayList();
        Map<String, Object> textField = Maps.newHashMap();
        textField.put("api_name", "text_field");
        textField.put("name", "text_field");
        Map<String, Object> fileField = Maps.newHashMap();
        fileField.put("api_name", "file_field");
        fileField.put("name", "file_field");

        fieldMaps.add(textField);
        fieldMaps.add(fileField);

        realTableComponent.set(ITableComponent.INCLUDE_FIELDS, fieldMaps);

        // Mock字段描述
        IFieldDescribe textFieldDescribe = mock(IFieldDescribe.class);
        IFieldDescribe fileFieldDescribe = mock(IFieldDescribe.class);
        when(textFieldDescribe.getType()).thenReturn("text");
        when(fileFieldDescribe.getType()).thenReturn("file_attachment");

        when(mockObjectDescribeExt.getFieldDescribeSilently("text_field")).thenReturn(Optional.of(textFieldDescribe));
        when(mockObjectDescribeExt.getFieldDescribeSilently("file_field")).thenReturn(Optional.of(fileFieldDescribe));

        TableComponentExt realTableComponentExt = TableComponentExt.of(realTableComponent);

        // Act: 根据类型移除字段（使用varargs参数）
        realTableComponentExt.removeFieldByTypes(mockObjectDescribeExt, "file_attachment");

        // Assert: 验证文件类型字段被移除
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> remainingFields = (List<Map<String, Object>>) realTableComponent.get(ITableComponent.INCLUDE_FIELDS);
        assertEquals(1, remainingFields.size());
        assertEquals("text_field", remainingFields.get(0).get("api_name"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试correctLabel方法 - 修正字段标签
     */
    @Test
    @DisplayName("字段管理 - correctLabel修正字段标签")
    void testCorrectLabel_CorrectFieldLabels() {
        // Arrange: 使用真实的TableComponent
        TableComponent realTableComponent = createRealTableComponent();

        // 创建字段列表，使用正确的字段结构
        List<Map<String, Object>> fieldMaps = Lists.newArrayList();
        Map<String, Object> field1 = Maps.newHashMap();
        field1.put("api_name", "field1");
        field1.put("name", "field1");
        field1.put("label_name", "Old Label 1");

        fieldMaps.add(field1);

        realTableComponent.set(ITableComponent.INCLUDE_FIELDS, fieldMaps);

        // Mock字段描述
        IFieldDescribe fieldDescribe1 = mock(IFieldDescribe.class);
        when(fieldDescribe1.getLabel()).thenReturn("New Label 1");

        when(mockObjectDescribeExt.getFieldDescribeSilently("field1")).thenReturn(Optional.of(fieldDescribe1));

        TableComponentExt realTableComponentExt = TableComponentExt.of(realTableComponent);

        // Act: 修正字段标签
        realTableComponentExt.correctLabel(mockObjectDescribeExt);

        // Assert: 验证方法执行完成，不抛异常即可
        // correctLabel方法的具体行为可能依赖于TableColumn的内部实现
        assertDoesNotThrow(() -> realTableComponentExt.correctLabel(mockObjectDescribeExt));

        // 验证Mock被调用
        verify(mockObjectDescribeExt, atLeastOnce()).getFieldDescribeSilently("field1");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setDefaultFieldListIfEmpty方法 - 设置默认字段列表
     */
    @Test
    @DisplayName("字段管理 - setDefaultFieldListIfEmpty设置默认字段列表")
    void testSetDefaultFieldListIfEmpty_SetDefaultFields() {
        // Arrange: 使用真实的TableComponent
        TableComponent realTableComponent = createRealTableComponent();
        realTableComponent.setIncludeFields(Lists.newArrayList()); // 设置空字段列表

        TableComponentExt realTableComponentExt = TableComponentExt.of(realTableComponent);

        // Act: 设置默认字段列表
        realTableComponentExt.setDefaultFieldListIfEmpty();

        // Assert: 验证默认字段被设置
        List<ITableColumn> fields = realTableComponent.getIncludeFields();
        assertEquals(1, fields.size());
        assertEquals("name", fields.get(0).getName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isShowTag方法 - 显示标签检查
     */
    @Test
    @DisplayName("显示配置 - isShowTag显示标签检查")
    void testIsShowTag_CheckShowTag() {
        // Arrange: 设置显示标签
        when(mockTableComponent.get("is_show_tag", Boolean.class)).thenReturn(true);
        when(mockTableComponent.get("is_new_layout", Boolean.class)).thenReturn(false);
        
        // Act: 检查是否显示标签
        boolean result = tableComponentExt.isShowTag();
        
        // Assert: 验证显示标签检查
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getShowImage方法 - 获取显示图片配置
     */
    @Test
    @DisplayName("显示配置 - getShowImage获取显示图片配置")
    void testGetShowImage_GetImageConfig() {
        // Arrange: 设置显示图片配置
        when(mockTableComponent.get("show_image", String.class)).thenReturn("avatar");
        when(mockTableComponent.get("is_new_layout", Boolean.class)).thenReturn(false);
        
        // Act: 获取显示图片配置
        String result = tableComponentExt.getShowImage();
        
        // Assert: 验证显示图片配置
        assertEquals("avatar", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试initRenderShowImageAndTag方法 - 初始化渲染配置
     */
    @Test
    @DisplayName("初始化 - initRenderShowImageAndTag初始化渲染配置")
    void testInitRenderShowImageAndTag_InitializeRenderConfig() {
        // Arrange: 创建真实的TableComponent来测试containsKey方法
        Map<String, Object> componentMap = Maps.newHashMap();
        componentMap.put("name", "test_table");
        componentMap.put("type", "table");
        // 不设置is_show_tag和show_image，这样containsKey会返回false

        TableComponent realTableComponent = new TableComponent(componentMap);
        TableComponentExt realTableComponentExt = TableComponentExt.of(realTableComponent);

        // Act: 初始化渲染配置
        realTableComponentExt.initRenderShowImageAndTag();

        // Assert: 验证配置被初始化
        assertEquals(false, realTableComponent.get("is_show_tag", Boolean.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enableNewLayout方法 - 新布局启用检查
     */
    @Test
    @DisplayName("布局检查 - enableNewLayout新布局启用检查")
    void testEnableNewLayout_CheckNewLayoutEnabled() {
        // Arrange: 设置启用新布局
        when(mockTableComponent.get("is_new_layout", Boolean.class)).thenReturn(true);
        
        // Act: 检查是否启用新布局
        boolean result = tableComponentExt.enableNewLayout();
        
        // Assert: 验证新布局启用检查
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件 - null字段列表处理
     */
    @Test
    @DisplayName("边界条件 - null字段列表处理")
    void testNullFieldListHandling() {
        // Arrange: 使用真实的TableComponent，设置null字段列表
        TableComponent realTableComponent = createRealTableComponent();
        realTableComponent.set(ITableComponent.INCLUDE_FIELDS, null);

        TableComponentExt realTableComponentExt = TableComponentExt.of(realTableComponent);

        // Act & Assert: 验证null字段列表不会导致异常
        assertDoesNotThrow(() -> realTableComponentExt.removeFields(Sets.newHashSet("any_field")));
        assertDoesNotThrow(() -> realTableComponentExt.correctLabel(mockObjectDescribeExt));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试性能 - 大量字段处理
     */
    @Test
    @DisplayName("性能测试 - 大量字段处理")
    void testPerformance_LargeFieldList() {
        // Arrange: 使用真实的TableComponent，创建大量字段
        TableComponent realTableComponent = createRealTableComponent();

        List<Map<String, Object>> largeFieldList = Lists.newArrayList();
        for (int i = 0; i < 1000; i++) {
            Map<String, Object> field = Maps.newHashMap();
            field.put("api_name", "field" + i);
            field.put("name", "field" + i);
            field.put("label_name", "Label " + i);
            largeFieldList.add(field);
        }

        realTableComponent.set(ITableComponent.INCLUDE_FIELDS, largeFieldList);

        TableComponentExt realTableComponentExt = TableComponentExt.of(realTableComponent);
        Set<String> fieldsToRemove = Sets.newHashSet("field0", "field500", "field999");

        // Act: 处理大量字段
        long startTime = System.currentTimeMillis();
        realTableComponentExt.removeFields(fieldsToRemove);
        long endTime = System.currentTimeMillis();

        // Assert: 验证性能和结果
        assertTrue(endTime - startTime < 1000, "处理1000个字段应在1秒内完成");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> remainingFields = (List<Map<String, Object>>) realTableComponent.get(ITableComponent.INCLUDE_FIELDS);
        assertEquals(997, remainingFields.size()); // 移除了3个字段
    }
}
