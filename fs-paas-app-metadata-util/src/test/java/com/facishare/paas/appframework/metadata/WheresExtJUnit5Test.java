package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.mockito.MockedStatic;

/**
 * GenerateByAI
 * WheresExt的JUnit 5测试类
 * 测试Wheres扩展类功能
 */
class WheresExtJUnit5Test {

    private Wheres testWheres;
    private IObjectDescribe mockObjectDescribe;
    private ObjectDescribeExt mockObjectDescribeExt;

    @BeforeEach
    void setUp() {
        testWheres = new Wheres();
        testWheres.setConnector(Where.CONN.AND.toString());
        
        mockObjectDescribe = mock(IObjectDescribe.class);
        mockObjectDescribeExt = mock(ObjectDescribeExt.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("of - 工厂方法")
    void testOf() {
        WheresExt result = WheresExt.of(testWheres);

        assertNotNull(result);
        assertSame(testWheres, result.getWheres());
        assertEquals(Where.CONN.AND.toString(), result.getConnector());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getWheresByJson静态方法
     */
    @Test
    @DisplayName("getWheresByJson - JSON解析")
    void testGetWheresByJson() {
        String json = "[{\"connector\":\"AND\",\"filters\":[{\"fieldName\":\"name\",\"operator\":\"EQ\",\"value\":\"test\"}]}]";
        
        List<Wheres> result = WheresExt.getWheresByJson(json);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("AND", result.get(0).getConnector());
        assertEquals(1, result.get(0).getFilters().size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getWheresByJson - 空JSON
     */
    @Test
    @DisplayName("getWheresByJson - 空JSON")
    void testGetWheresByJson_EmptyJson() {
        String json = "[]";
        
        List<Wheres> result = WheresExt.getWheresByJson(json);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试multiWheres方法 - 多个条件
     */
    @Test
    @DisplayName("multiWheres - 多个条件")
    void testMultiWheres_Multiple() {
        List<Wheres> wheres = Lists.newArrayList(new Wheres(), new Wheres());
        
        boolean result = WheresExt.multiWheres(wheres);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试multiWheres方法 - 单个条件
     */
    @Test
    @DisplayName("multiWheres - 单个条件")
    void testMultiWheres_Single() {
        List<Wheres> wheres = Lists.newArrayList(new Wheres());
        
        boolean result = WheresExt.multiWheres(wheres);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试multiWheres方法 - 空列表
     */
    @Test
    @DisplayName("multiWheres - 空列表")
    void testMultiWheres_Empty() {
        boolean result = WheresExt.multiWheres(null);
        assertFalse(result);

        result = WheresExt.multiWheres(Lists.newArrayList());
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试hasSpecialFilter方法 - 空列表
     */
    @Test
    @DisplayName("hasSpecialFilter - 空列表")
    void testHasSpecialFilter_Empty() {
        boolean result = WheresExt.hasSpecialFilter(null);
        assertFalse(result);

        result = WheresExt.hasSpecialFilter(Lists.newArrayList());
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试hasSpecialFilter方法 - 普通过滤器
     */
    @Test
    @DisplayName("hasSpecialFilter - 普通过滤器")
    void testHasSpecialFilter_Normal() {
        Wheres wheres = new Wheres();
        Filter filter = new Filter();
        filter.setFieldName("name");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("test"));
        wheres.setFilters(Lists.newArrayList(filter));
        
        List<Wheres> wheresList = Lists.newArrayList(wheres);
        
        boolean result = WheresExt.hasSpecialFilter(wheresList);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleWheresByDescribe方法
     */
    @Test
    @DisplayName("handleWheresByDescribe - 处理字段描述")
    void testHandleWheresByDescribe() {
        // 创建测试数据
        Wheres wheres1 = new Wheres();
        Filter activeFilter = new Filter();
        activeFilter.setFieldName("activeField");
        activeFilter.setOperator(Operator.EQ);
        activeFilter.setFieldValues(Lists.newArrayList("test"));

        Filter inactiveFilter = new Filter();
        inactiveFilter.setFieldName("inactiveField");
        inactiveFilter.setOperator(Operator.EQ);
        inactiveFilter.setFieldValues(Lists.newArrayList("test"));
        
        wheres1.setFilters(Lists.newArrayList(activeFilter, inactiveFilter));
        
        List<Wheres> wheresList = Lists.newArrayList(wheres1);
        
        // Mock ObjectDescribeExt
        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            mockedStatic.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);
            
            when(mockObjectDescribeExt.isFieldActive("activeField")).thenReturn(true);
            when(mockObjectDescribeExt.isFieldActive("inactiveField")).thenReturn(false);
            
            List<Wheres> result = WheresExt.handleWheresByDescribe(wheresList, mockObjectDescribe);
            
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(1, result.get(0).getFilters().size());
            assertEquals("activeField", result.get(0).getFilters().get(0).getFieldName());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleWheresByDescribe方法 - 移除空的Wheres
     */
    @Test
    @DisplayName("handleWheresByDescribe - 移除空的Wheres")
    void testHandleWheresByDescribe_RemoveEmpty() {
        // 创建测试数据
        Wheres wheres1 = new Wheres();
        Filter inactiveFilter = new Filter();
        inactiveFilter.setFieldName("inactiveField");
        inactiveFilter.setOperator(Operator.EQ);
        inactiveFilter.setFieldValues(Lists.newArrayList("test"));
        wheres1.setFilters(Lists.newArrayList(inactiveFilter));
        
        List<Wheres> wheresList = Lists.newArrayList(wheres1);
        
        // Mock ObjectDescribeExt
        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            mockedStatic.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);
            
            when(mockObjectDescribeExt.isFieldActive("inactiveField")).thenReturn(false);
            
            List<Wheres> result = WheresExt.handleWheresByDescribe(wheresList, mockObjectDescribe);
            
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试castToWheres方法
     */
    @Test
    @DisplayName("castToWheres - Map转换为Wheres")
    void testCastToWheres() {
        Map<String, Object> document = Maps.newHashMap();
        List<Map<String, Object>> filters = Lists.newArrayList();
        
        Map<String, Object> filterMap = Maps.newHashMap();
        filterMap.put("fieldName", "name");
        filterMap.put("operator", "EQ");
        filterMap.put("value", "test");
        filters.add(filterMap);
        
        document.put("filters", filters);
        
        Wheres result = WheresExt.castToWheres(document);
        
        assertNotNull(result);
        assertEquals(Where.CONN.OR.toString(), result.getConnector());
        assertEquals(1, result.getFilters().size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试castToWheres方法 - null输入
     */
    @Test
    @DisplayName("castToWheres - null输入")
    void testCastToWheres_Null() {
        Wheres result = WheresExt.castToWheres(null);
        
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试castToWheresList方法
     */
    @Test
    @DisplayName("castToWheresList - List转换")
    void testCastToWheresList() {
        List<Map<String, Object>> documents = Lists.newArrayList();
        
        Map<String, Object> document = Maps.newHashMap();
        List<Map<String, Object>> filters = Lists.newArrayList();
        
        Map<String, Object> filterMap = Maps.newHashMap();
        filterMap.put("fieldName", "name");
        filterMap.put("operator", "EQ");
        filterMap.put("value", "test");
        filters.add(filterMap);
        
        document.put("filters", filters);
        documents.add(document);
        
        List<Wheres> result = WheresExt.castToWheresList(documents);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(Where.CONN.OR.toString(), result.get(0).getConnector());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试castToWheresList方法 - 空输入
     */
    @Test
    @DisplayName("castToWheresList - 空输入")
    void testCastToWheresList_Empty() {
        List<Wheres> result = WheresExt.castToWheresList(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());

        result = WheresExt.castToWheresList(Lists.newArrayList());
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toMap方法
     */
    @Test
    @DisplayName("toMap - 转换为Map")
    void testToMap() {
        Filter filter = new Filter();
        filter.setFieldName("name");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("test"));
        
        testWheres.setFilters(Lists.newArrayList(filter));
        WheresExt wheresExt = WheresExt.of(testWheres);
        
        LinkedHashMap result = wheresExt.toMap();
        
        assertNotNull(result);
        assertEquals("AND", result.get("connector"));
        assertNotNull(result.get("filters"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法
     */
    @Test
    @DisplayName("equals - 相等性测试")
    void testEquals() {
        Filter filter = new Filter();
        filter.setFieldName("name");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("test"));
        
        Wheres wheres1 = new Wheres();
        wheres1.setConnector(Where.CONN.AND.toString());
        wheres1.setFilters(Lists.newArrayList(filter));
        
        Wheres wheres2 = new Wheres();
        wheres2.setConnector(Where.CONN.AND.toString());
        wheres2.setFilters(Lists.newArrayList(filter));
        
        WheresExt ext1 = WheresExt.of(wheres1);
        WheresExt ext2 = WheresExt.of(wheres2);
        
        assertEquals(ext1, ext2);
        assertEquals(ext1.hashCode(), ext2.hashCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法 - 不相等
     */
    @Test
    @DisplayName("equals - 不相等")
    void testEquals_NotEqual() {
        Wheres wheres1 = new Wheres();
        wheres1.setConnector(Where.CONN.AND.toString());
        
        Wheres wheres2 = new Wheres();
        wheres2.setConnector(Where.CONN.OR.toString());
        
        WheresExt ext1 = WheresExt.of(wheres1);
        WheresExt ext2 = WheresExt.of(wheres2);
        
        assertNotEquals(ext1, ext2);
        assertNotEquals(ext1, null);
        assertNotEquals(ext1, "string");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getParentDeptAndOrgName静态方法
     */
    @Test
    @DisplayName("getParentDeptAndOrgName - 生成父部门名称")
    void testGetParentDeptAndOrgName() {
        String apiName = "department";
        
        String result = WheresExt.getParentDeptAndOrgName(apiName);
        
        assertEquals("$parentDeptdepartment#", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试usingLastModifiedTime方法
     */
    @Test
    @DisplayName("usingLastModifiedTime - 检查是否使用最后修改时间")
    void testUsingLastModifiedTime() {
        Filter filter = new Filter();
        filter.setFieldName("last_modified_time");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("2023-01-01"));
        
        testWheres.setFilters(Lists.newArrayList(filter));
        WheresExt wheresExt = WheresExt.of(testWheres);
        
        // 这个测试主要验证方法不会抛出异常
        boolean result = wheresExt.usingLastModifiedTime();
        
        // 由于FilterExt.usingLastModifiedTime()的具体实现未知，我们只验证方法能正常执行
        assertNotNull(result);
    }
}
