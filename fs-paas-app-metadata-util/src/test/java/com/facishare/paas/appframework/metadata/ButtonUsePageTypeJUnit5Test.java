package com.facishare.paas.appframework.metadata;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ButtonUsePageType的JUnit 5测试类
 * 测试按钮使用页面类型枚举功能
 * 
 * GenerateByAI
 * 测试内容描述：测试按钮使用页面类型的获取和解析功能
 * 注意：此类包含国际化标签，测试主要验证方法调用的健壮性
 */
class ButtonUsePageTypeJUnit5Test {

    /**
     * GenerateByAI
     * 测试内容描述：测试枚举基本属性
     */
    @Test
    @DisplayName("枚举基础 - 枚举值基本属性")
    void testEnumBasicProperties() {
        // 验证所有枚举值
        assertEquals(9, ButtonUsePageType.values().length);
        
        // 验证枚举值存在
        assertNotNull(ButtonUsePageType.Detail);
        assertNotNull(ButtonUsePageType.Create);
        assertNotNull(ButtonUsePageType.Edit);
        assertNotNull(ButtonUsePageType.DataList);
        assertNotNull(ButtonUsePageType.RelatedList);
        assertNotNull(ButtonUsePageType.ListBatch);
        assertNotNull(ButtonUsePageType.ListNormal);
        assertNotNull(ButtonUsePageType.ListComponent);
        assertNotNull(ButtonUsePageType.RecycleBin);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getId方法 - 参数化测试
     */
    @ParameterizedTest
    @CsvSource({
        "Detail, detail",
        "Create, create",
        "Edit, edit",
        "DataList, list",
        "RelatedList, related_list",
        "ListBatch, list_batch",
        "ListNormal, list_normal",
        "ListComponent, list_component",
        "RecycleBin, recycle_bin"
    })
    @DisplayName("ID获取 - getId方法验证所有枚举值")
    void testGetId_AllValues(ButtonUsePageType pageType, String expectedId) {
        // Act: 执行测试方法
        String result = pageType.getId();

        // Assert: 验证结果
        assertEquals(expectedId, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态方法 - 有效ID
     */
    @ParameterizedTest
    @CsvSource({
        "detail, Detail",
        "create, Create",
        "edit, Edit",
        "list, DataList",
        "related_list, RelatedList",
        "list_batch, ListBatch",
        "list_normal, ListNormal",
        "list_component, ListComponent",
        "recycle_bin, RecycleBin"
    })
    @DisplayName("静态方法 - of方法有效ID")
    void testOf_ValidIds(String pageTypeId, ButtonUsePageType expected) {
        // Act: 执行测试方法
        ButtonUsePageType result = ButtonUsePageType.of(pageTypeId);

        // Assert: 验证结果
        assertEquals(expected, result);
        assertEquals(pageTypeId, result.getId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态方法 - 无效ID
     */
    @ParameterizedTest
    @ValueSource(strings = {"invalid_page", "unknown", "test", "", "DETAIL", "Create"})
    @DisplayName("边界条件 - of方法无效ID")
    void testOf_InvalidIds(String invalidId) {
        // Act: 执行测试方法
        ButtonUsePageType result = ButtonUsePageType.of(invalidId);

        // Assert: 验证结果 - 应该返回null
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态方法 - null参数
     */
    @Test
    @DisplayName("边界条件 - of方法null参数")
    void testOf_NullParameter() {
        // Act: 执行测试方法
        ButtonUsePageType result = ButtonUsePageType.of(null);

        // Assert: 验证结果 - 应该返回null
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试枚举的ordinal值
     */
    @Test
    @DisplayName("枚举顺序 - ordinal值验证")
    void testOrdinalValues() {
        assertEquals(0, ButtonUsePageType.Detail.ordinal());
        assertEquals(1, ButtonUsePageType.Create.ordinal());
        assertEquals(2, ButtonUsePageType.Edit.ordinal());
        assertEquals(3, ButtonUsePageType.DataList.ordinal());
        assertEquals(4, ButtonUsePageType.RelatedList.ordinal());
        assertEquals(5, ButtonUsePageType.ListBatch.ordinal());
        assertEquals(6, ButtonUsePageType.ListNormal.ordinal());
        assertEquals(7, ButtonUsePageType.ListComponent.ordinal());
        assertEquals(8, ButtonUsePageType.RecycleBin.ordinal());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试枚举的name方法
     */
    @Test
    @DisplayName("名称获取 - name方法")
    void testNameMethod() {
        assertEquals("Detail", ButtonUsePageType.Detail.name());
        assertEquals("Create", ButtonUsePageType.Create.name());
        assertEquals("Edit", ButtonUsePageType.Edit.name());
        assertEquals("DataList", ButtonUsePageType.DataList.name());
        assertEquals("RelatedList", ButtonUsePageType.RelatedList.name());
        assertEquals("ListBatch", ButtonUsePageType.ListBatch.name());
        assertEquals("ListNormal", ButtonUsePageType.ListNormal.name());
        assertEquals("ListComponent", ButtonUsePageType.ListComponent.name());
        assertEquals("RecycleBin", ButtonUsePageType.RecycleBin.name());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试valueOf(String)方法
     */
    @Test
    @DisplayName("名称转换 - valueOf(String)方法")
    void testValueOf_StringName() {
        // 测试有效的枚举名称
        assertEquals(ButtonUsePageType.Detail, ButtonUsePageType.valueOf("Detail"));
        assertEquals(ButtonUsePageType.Create, ButtonUsePageType.valueOf("Create"));
        assertEquals(ButtonUsePageType.Edit, ButtonUsePageType.valueOf("Edit"));
        assertEquals(ButtonUsePageType.DataList, ButtonUsePageType.valueOf("DataList"));
        assertEquals(ButtonUsePageType.RelatedList, ButtonUsePageType.valueOf("RelatedList"));
        assertEquals(ButtonUsePageType.ListBatch, ButtonUsePageType.valueOf("ListBatch"));
        assertEquals(ButtonUsePageType.ListNormal, ButtonUsePageType.valueOf("ListNormal"));
        assertEquals(ButtonUsePageType.ListComponent, ButtonUsePageType.valueOf("ListComponent"));
        assertEquals(ButtonUsePageType.RecycleBin, ButtonUsePageType.valueOf("RecycleBin"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试valueOf(String)方法 - 无效名称
     */
    @Test
    @DisplayName("边界条件 - valueOf(String)方法无效名称")
    void testValueOf_InvalidStringName() {
        assertThrows(IllegalArgumentException.class, () -> {
            ButtonUsePageType.valueOf("INVALID_TYPE");
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试valueOf(String)方法 - null名称
     */
    @Test
    @DisplayName("边界条件 - valueOf(String)方法null名称")
    void testValueOf_NullStringName() {
        assertThrows(NullPointerException.class, () -> {
            ButtonUsePageType.valueOf((String) null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试静态初始化
     */
    @Test
    @DisplayName("初始化 - 静态初始化成功")
    void testStaticInitialization() {
        // 验证静态Map初始化成功
        assertDoesNotThrow(() -> {
            // 通过调用of方法来验证静态Map是否正确初始化
            ButtonUsePageType.of("detail");
            ButtonUsePageType.of("create");
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ID的唯一性
     */
    @Test
    @DisplayName("唯一性验证 - ID唯一性")
    void testIdUniqueness() {
        ButtonUsePageType[] values = ButtonUsePageType.values();
        
        // 验证所有ID都是唯一的
        for (int i = 0; i < values.length; i++) {
            for (int j = i + 1; j < values.length; j++) {
                assertNotEquals(values[i].getId(), values[j].getId(),
                    "ID应该是唯一的: " + values[i].name() + " vs " + values[j].name());
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // 枚举的toString默认返回name()
        assertEquals("Detail", ButtonUsePageType.Detail.toString());
        assertEquals("Create", ButtonUsePageType.Create.toString());
        assertEquals("Edit", ButtonUsePageType.Edit.toString());
        assertEquals("DataList", ButtonUsePageType.DataList.toString());
        assertEquals("RelatedList", ButtonUsePageType.RelatedList.toString());
        assertEquals("ListBatch", ButtonUsePageType.ListBatch.toString());
        assertEquals("ListNormal", ButtonUsePageType.ListNormal.toString());
        assertEquals("ListComponent", ButtonUsePageType.ListComponent.toString());
        assertEquals("RecycleBin", ButtonUsePageType.RecycleBin.toString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试values方法
     */
    @Test
    @DisplayName("枚举数组 - values方法")
    void testValues() {
        ButtonUsePageType[] values = ButtonUsePageType.values();
        
        assertNotNull(values);
        assertEquals(9, values.length);
        
        // 验证顺序
        assertEquals(ButtonUsePageType.Detail, values[0]);
        assertEquals(ButtonUsePageType.Create, values[1]);
        assertEquals(ButtonUsePageType.Edit, values[2]);
        assertEquals(ButtonUsePageType.DataList, values[3]);
        assertEquals(ButtonUsePageType.RelatedList, values[4]);
        assertEquals(ButtonUsePageType.ListBatch, values[5]);
        assertEquals(ButtonUsePageType.ListNormal, values[6]);
        assertEquals(ButtonUsePageType.ListComponent, values[7]);
        assertEquals(ButtonUsePageType.RecycleBin, values[8]);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试枚举的完整性和一致性
     */
    @Test
    @DisplayName("完整性验证 - 枚举一致性检查")
    void testEnumConsistency() {
        // 验证所有枚举值都能通过ID正确转换
        for (ButtonUsePageType pageType : ButtonUsePageType.values()) {
            assertEquals(pageType, ButtonUsePageType.of(pageType.getId()));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试业务逻辑 - 页面类型的含义
     */
    @Test
    @DisplayName("业务逻辑 - 页面类型含义验证")
    void testBusinessLogic() {
        // 验证详情页
        assertEquals("detail", ButtonUsePageType.Detail.getId());

        // 验证创建页
        assertEquals("create", ButtonUsePageType.Create.getId());

        // 验证编辑页
        assertEquals("edit", ButtonUsePageType.Edit.getId());

        // 验证数据列表页
        assertEquals("list", ButtonUsePageType.DataList.getId());

        // 验证相关列表
        assertEquals("related_list", ButtonUsePageType.RelatedList.getId());

        // 验证列表批量操作
        assertEquals("list_batch", ButtonUsePageType.ListBatch.getId());

        // 验证列表通用操作
        assertEquals("list_normal", ButtonUsePageType.ListNormal.getId());

        // 验证列表组件化（独立站点）
        assertEquals("list_component", ButtonUsePageType.ListComponent.getId());

        // 验证回收站
        assertEquals("recycle_bin", ButtonUsePageType.RecycleBin.getId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试枚举的equals方法
     */
    @Test
    @DisplayName("对象比较 - equals方法")
    void testEquals() {
        // 枚举实例是单例的
        assertSame(ButtonUsePageType.Detail, ButtonUsePageType.valueOf("Detail"));
        assertSame(ButtonUsePageType.Create, ButtonUsePageType.valueOf("Create"));

        // 测试equals
        assertEquals(ButtonUsePageType.Detail, ButtonUsePageType.valueOf("Detail"));
        assertNotEquals(ButtonUsePageType.Detail, ButtonUsePageType.Create);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试枚举的hashCode方法
     */
    @Test
    @DisplayName("哈希码 - hashCode方法")
    void testHashCode() {
        // 相同枚举值的hashCode应该相同
        assertEquals(ButtonUsePageType.Detail.hashCode(), ButtonUsePageType.valueOf("Detail").hashCode());
        assertEquals(ButtonUsePageType.Create.hashCode(), ButtonUsePageType.valueOf("Create").hashCode());

        // 不同枚举值的hashCode应该不同
        assertNotEquals(ButtonUsePageType.Detail.hashCode(), ButtonUsePageType.Create.hashCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ID格式一致性
     */
    @Test
    @DisplayName("格式验证 - ID格式一致性")
    void testIdFormatConsistency() {
        // 验证所有ID都是小写字母和下划线
        for (ButtonUsePageType pageType : ButtonUsePageType.values()) {
            String id = pageType.getId();
            assertNotNull(id);
            assertFalse(id.trim().isEmpty());

            // 验证ID格式：小写字母和下划线
            assertTrue(id.matches("^[a-z_]+$"),
                "ID应该只包含小写字母和下划线: " + pageType.name() + " -> " + id);

            // 验证不包含特殊字符
            assertFalse(id.contains("-"), "ID不应该包含连字符: " + id);
            assertFalse(id.contains(" "), "ID不应该包含空格: " + id);
            assertFalse(id.contains("."), "ID不应该包含点号: " + id);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试枚举在switch语句中的使用
     */
    @Test
    @DisplayName("使用场景 - switch语句兼容性")
    void testSwitchCompatibility() {
        // 测试枚举可以在switch语句中正常使用
        for (ButtonUsePageType pageType : ButtonUsePageType.values()) {
            String result;
            switch (pageType) {
                case Detail:
                    result = "详情页面";
                    break;
                case Create:
                    result = "创建页面";
                    break;
                case Edit:
                    result = "编辑页面";
                    break;
                case DataList:
                    result = "数据列表页面";
                    break;
                case RelatedList:
                    result = "相关列表";
                    break;
                case ListBatch:
                    result = "列表批量操作";
                    break;
                case ListNormal:
                    result = "列表通用操作";
                    break;
                case ListComponent:
                    result = "列表组件化";
                    break;
                case RecycleBin:
                    result = "回收站";
                    break;
                default:
                    result = "未知页面类型";
                    break;
            }

            assertNotNull(result);
            assertFalse(result.trim().isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试大小写敏感性
     */
    @Test
    @DisplayName("边界条件 - 大小写敏感性")
    void testCaseSensitivity() {
        // 验证of方法是大小写敏感的
        assertNull(ButtonUsePageType.of("DETAIL"));
        assertNull(ButtonUsePageType.of("Detail"));
        assertNull(ButtonUsePageType.of("CREATE"));
        assertNull(ButtonUsePageType.of("Create"));

        // 只有精确匹配才能成功
        assertEquals(ButtonUsePageType.Detail, ButtonUsePageType.of("detail"));
        assertEquals(ButtonUsePageType.Create, ButtonUsePageType.of("create"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试页面类型的分类
     */
    @Test
    @DisplayName("业务逻辑 - 页面类型分类")
    void testPageTypeCategories() {
        // 单页面操作类型
        ButtonUsePageType[] singlePageTypes = {
            ButtonUsePageType.Detail, ButtonUsePageType.Create, ButtonUsePageType.Edit
        };
        for (ButtonUsePageType pageType : singlePageTypes) {
            assertNotNull(pageType);
            String id = pageType.getId();
            assertTrue(id.equals("detail") || id.equals("create") || id.equals("edit"));
        }

        // 列表操作类型
        ButtonUsePageType[] listPageTypes = {
            ButtonUsePageType.DataList, ButtonUsePageType.RelatedList,
            ButtonUsePageType.ListBatch, ButtonUsePageType.ListNormal,
            ButtonUsePageType.ListComponent
        };
        for (ButtonUsePageType pageType : listPageTypes) {
            assertNotNull(pageType);
            String id = pageType.getId();
            assertTrue(id.contains("list") || id.equals("list"));
        }

        // 特殊类型
        assertEquals("recycle_bin", ButtonUsePageType.RecycleBin.getId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JsonCreator注解功能
     */
    @Test
    @DisplayName("JSON序列化 - JsonCreator注解功能")
    void testJsonCreatorAnnotation() {
        // 验证of方法可以用于JSON反序列化
        // 这个方法被@JsonCreator注解标记

        // 测试有效的JSON值
        assertEquals(ButtonUsePageType.Detail, ButtonUsePageType.of("detail"));
        assertEquals(ButtonUsePageType.Create, ButtonUsePageType.of("create"));
        assertEquals(ButtonUsePageType.Edit, ButtonUsePageType.of("edit"));

        // 测试无效的JSON值
        assertNull(ButtonUsePageType.of("invalid_json_value"));
        assertNull(ButtonUsePageType.of(""));
        assertNull(ButtonUsePageType.of(null));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试业务场景 - 页面类型判断
     */
    @Test
    @DisplayName("业务场景 - 页面类型判断")
    void testPageTypeJudgment() {
        // 模拟业务场景：根据页面类型进行不同处理

        // 详情页 - 通常显示数据详情
        ButtonUsePageType detailType = ButtonUsePageType.of("detail");
        assertEquals(ButtonUsePageType.Detail, detailType);

        // 创建页 - 通常用于新建数据
        ButtonUsePageType createType = ButtonUsePageType.of("create");
        assertEquals(ButtonUsePageType.Create, createType);

        // 编辑页 - 通常用于修改数据
        ButtonUsePageType editType = ButtonUsePageType.of("edit");
        assertEquals(ButtonUsePageType.Edit, editType);

        // 列表页 - 通常显示数据列表
        ButtonUsePageType listType = ButtonUsePageType.of("list");
        assertEquals(ButtonUsePageType.DataList, listType);

        // 相关列表 - 通常显示关联数据
        ButtonUsePageType relatedType = ButtonUsePageType.of("related_list");
        assertEquals(ButtonUsePageType.RelatedList, relatedType);

        // 批量操作 - 通常用于批量处理
        ButtonUsePageType batchType = ButtonUsePageType.of("list_batch");
        assertEquals(ButtonUsePageType.ListBatch, batchType);

        // 回收站 - 通常显示已删除数据
        ButtonUsePageType recycleBinType = ButtonUsePageType.of("recycle_bin");
        assertEquals(ButtonUsePageType.RecycleBin, recycleBinType);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试线程安全性
     */
    @Test
    @DisplayName("线程安全测试 - 并发调用不应该出错")
    void testThreadSafety() {
        Exception exception = null;

        // 创建多个线程并发调用
        Thread[] threads = new Thread[10];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread(() -> {
                try {
                    ButtonUsePageType.of("detail");
                    ButtonUsePageType.of("create");
                    ButtonUsePageType.of("edit");
                    ButtonUsePageType.of("list");
                    ButtonUsePageType.of("invalid");
                    ButtonUsePageType.of(null);
                } catch (Exception e) {
                    // 在实际测试中，这里应该使用更好的异常收集机制
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            assertDoesNotThrow(() -> thread.join(), "线程执行过程中不应该有异常");
        }

        assertNull(exception, "线程执行过程中不应该有异常");
    }
}
