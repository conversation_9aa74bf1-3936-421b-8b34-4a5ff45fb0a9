package com.facishare.paas.appframework.metadata.layout;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LayoutTypes的JUnit 5测试类
 * 测试布局类型常量接口功能
 */
class LayoutTypesJUnit5Test {

    /**
     * 测试内容描述：测试布局类型常量值
     */
    @Test
    @DisplayName("常量值 - 布局类型常量值验证")
    void testLayoutTypeConstants() {
        // 验证所有布局类型常量值
        assertEquals("detail", LayoutTypes.DETAIL);
        assertEquals("add", LayoutTypes.ADD);
        assertEquals("edit", LayoutTypes.EDIT);
        assertEquals("list_layout", LayoutTypes.LIST_LAYOUT);
        assertEquals("list", LayoutTypes.LIST);
        assertEquals("what_list", LayoutTypes.WHAT_LIST);
        assertEquals("flow_task_list", LayoutTypes.FLOW_TASK_LIST);
    }

    /**
     * 测试内容描述：测试ALL_TYPES集合
     */
    @Test
    @DisplayName("集合验证 - ALL_TYPES集合内容验证")
    void testAllTypesSet() {
        Set<String> allTypes = LayoutTypes.ALL_TYPES;
        
        // 验证集合不为空
        assertNotNull(allTypes);
        assertEquals(7, allTypes.size());
        
        // 验证包含所有布局类型
        assertTrue(allTypes.contains(LayoutTypes.DETAIL));
        assertTrue(allTypes.contains(LayoutTypes.ADD));
        assertTrue(allTypes.contains(LayoutTypes.EDIT));
        assertTrue(allTypes.contains(LayoutTypes.LIST_LAYOUT));
        assertTrue(allTypes.contains(LayoutTypes.LIST));
        assertTrue(allTypes.contains(LayoutTypes.WHAT_LIST));
        assertTrue(allTypes.contains(LayoutTypes.FLOW_TASK_LIST));
    }

    /**
     * 测试内容描述：测试TODO_TYPES集合
     */
    @Test
    @DisplayName("集合验证 - TODO_TYPES集合内容验证")
    void testTodoTypesSet() {
        Set<String> todoTypes = LayoutTypes.TODO_TYPES;
        
        // 验证集合不为空
        assertNotNull(todoTypes);
        assertEquals(2, todoTypes.size());
        
        // 验证包含待办布局类型
        assertTrue(todoTypes.contains(LayoutTypes.WHAT_LIST));
        assertTrue(todoTypes.contains(LayoutTypes.FLOW_TASK_LIST));
        
        // 验证不包含其他类型
        assertFalse(todoTypes.contains(LayoutTypes.DETAIL));
        assertFalse(todoTypes.contains(LayoutTypes.ADD));
        assertFalse(todoTypes.contains(LayoutTypes.EDIT));
        assertFalse(todoTypes.contains(LayoutTypes.LIST_LAYOUT));
        assertFalse(todoTypes.contains(LayoutTypes.LIST));
    }

    /**
     * 测试内容描述：测试集合的不可变性
     */
    @Test
    @DisplayName("不可变性 - 集合不可变性验证")
    void testSetImmutability() {
        Set<String> allTypes = LayoutTypes.ALL_TYPES;
        Set<String> todoTypes = LayoutTypes.TODO_TYPES;
        
        // 验证集合是不可变的
        assertThrows(UnsupportedOperationException.class, () -> {
            allTypes.add("new_type");
        });
        
        assertThrows(UnsupportedOperationException.class, () -> {
            todoTypes.add("new_todo_type");
        });
        
        assertThrows(UnsupportedOperationException.class, () -> {
            allTypes.remove(LayoutTypes.DETAIL);
        });
        
        assertThrows(UnsupportedOperationException.class, () -> {
            todoTypes.remove(LayoutTypes.WHAT_LIST);
        });
    }

    /**
     * 测试内容描述：测试布局类型的业务含义
     */
    @Test
    @DisplayName("业务逻辑 - 布局类型业务含义验证")
    void testLayoutTypeBusinessMeaning() {
        // 详情页/流程布局
        assertEquals("detail", LayoutTypes.DETAIL);
        
        // 新建页面
        assertEquals("add", LayoutTypes.ADD);
        
        // 编辑页面
        assertEquals("edit", LayoutTypes.EDIT);
        
        // 列表页-列表页布局
        assertEquals("list_layout", LayoutTypes.LIST_LAYOUT);
        
        // 列表页-移动端摘要布局
        assertEquals("list", LayoutTypes.LIST);
        
        // 待办-all-移动端摘要布局
        assertEquals("what_list", LayoutTypes.WHAT_LIST);
        
        // 待办-all-列表页布局
        assertEquals("flow_task_list", LayoutTypes.FLOW_TASK_LIST);
    }

    /**
     * 测试内容描述：测试布局类型分类
     */
    @Test
    @DisplayName("分类验证 - 布局类型分类验证")
    void testLayoutTypeCategories() {
        // 表单类布局
        String[] formLayouts = {LayoutTypes.DETAIL, LayoutTypes.ADD, LayoutTypes.EDIT};
        for (String layout : formLayouts) {
            assertTrue(LayoutTypes.ALL_TYPES.contains(layout));
            assertFalse(LayoutTypes.TODO_TYPES.contains(layout));
        }
        
        // 列表类布局
        String[] listLayouts = {LayoutTypes.LIST_LAYOUT, LayoutTypes.LIST};
        for (String layout : listLayouts) {
            assertTrue(LayoutTypes.ALL_TYPES.contains(layout));
            assertFalse(LayoutTypes.TODO_TYPES.contains(layout));
        }
        
        // 待办类布局
        String[] todoLayouts = {LayoutTypes.WHAT_LIST, LayoutTypes.FLOW_TASK_LIST};
        for (String layout : todoLayouts) {
            assertTrue(LayoutTypes.ALL_TYPES.contains(layout));
            assertTrue(LayoutTypes.TODO_TYPES.contains(layout));
        }
    }

    /**
     * 测试内容描述：测试布局类型字符串格式
     */
    @ParameterizedTest
    @ValueSource(strings = {"detail", "add", "edit", "list_layout", "list", "what_list", "flow_task_list"})
    @DisplayName("格式验证 - 布局类型字符串格式验证")
    void testLayoutTypeStringFormat(String layoutType) {
        // 验证布局类型格式：小写字母和下划线
        assertTrue(layoutType.matches("^[a-z_]+$"), 
            "布局类型应该只包含小写字母和下划线: " + layoutType);
        
        // 验证不以下划线开头或结尾
        assertFalse(layoutType.startsWith("_"), "布局类型不应该以下划线开头: " + layoutType);
        assertFalse(layoutType.endsWith("_"), "布局类型不应该以下划线结尾: " + layoutType);
        
        // 验证在ALL_TYPES中存在
        assertTrue(LayoutTypes.ALL_TYPES.contains(layoutType), 
            "布局类型应该在ALL_TYPES中存在: " + layoutType);
    }

    /**
     * 测试内容描述：测试集合包含关系
     */
    @Test
    @DisplayName("包含关系 - 集合包含关系验证")
    void testSetContainmentRelationship() {
        // TODO_TYPES是ALL_TYPES的子集
        assertTrue(LayoutTypes.ALL_TYPES.containsAll(LayoutTypes.TODO_TYPES));
        
        // 验证TODO_TYPES中的每个元素都在ALL_TYPES中
        for (String todoType : LayoutTypes.TODO_TYPES) {
            assertTrue(LayoutTypes.ALL_TYPES.contains(todoType));
        }
        
        // 验证ALL_TYPES包含的元素比TODO_TYPES多
        assertTrue(LayoutTypes.ALL_TYPES.size() > LayoutTypes.TODO_TYPES.size());
    }

    /**
     * 测试内容描述：测试布局类型的唯一性
     */
    @Test
    @DisplayName("唯一性验证 - 布局类型唯一性验证")
    void testLayoutTypeUniqueness() {
        String[] allLayoutTypes = {
            LayoutTypes.DETAIL, LayoutTypes.ADD, LayoutTypes.EDIT,
            LayoutTypes.LIST_LAYOUT, LayoutTypes.LIST,
            LayoutTypes.WHAT_LIST, LayoutTypes.FLOW_TASK_LIST
        };
        
        // 验证所有布局类型都是唯一的
        for (int i = 0; i < allLayoutTypes.length; i++) {
            for (int j = i + 1; j < allLayoutTypes.length; j++) {
                assertNotEquals(allLayoutTypes[i], allLayoutTypes[j],
                    "布局类型应该是唯一的: " + allLayoutTypes[i] + " vs " + allLayoutTypes[j]);
            }
        }
    }

    /**
     * 测试内容描述：测试接口的完整性
     */
    @Test
    @DisplayName("完整性验证 - 接口完整性验证")
    void testInterfaceCompleteness() {
        // 验证接口定义了所有必要的常量
        assertNotNull(LayoutTypes.DETAIL);
        assertNotNull(LayoutTypes.ADD);
        assertNotNull(LayoutTypes.EDIT);
        assertNotNull(LayoutTypes.LIST_LAYOUT);
        assertNotNull(LayoutTypes.LIST);
        assertNotNull(LayoutTypes.WHAT_LIST);
        assertNotNull(LayoutTypes.FLOW_TASK_LIST);
        
        // 验证接口定义了所有必要的集合
        assertNotNull(LayoutTypes.ALL_TYPES);
        assertNotNull(LayoutTypes.TODO_TYPES);
        
        // 验证集合不为空
        assertFalse(LayoutTypes.ALL_TYPES.isEmpty());
        assertFalse(LayoutTypes.TODO_TYPES.isEmpty());
    }

    /**
     * 测试内容描述：测试布局类型的命名规范
     */
    @Test
    @DisplayName("命名规范 - 布局类型命名规范验证")
    void testLayoutTypeNamingConvention() {
        // 验证常量名称都是大写
        String[] constantNames = {"DETAIL", "ADD", "EDIT", "LIST_LAYOUT", "LIST", "WHAT_LIST", "FLOW_TASK_LIST"};
        
        for (String constantName : constantNames) {
            // 验证常量名称格式：大写字母和下划线
            assertTrue(constantName.matches("^[A-Z_]+$"), 
                "常量名称应该只包含大写字母和下划线: " + constantName);
        }
        
        // 验证集合名称
        assertTrue("ALL_TYPES".matches("^[A-Z_]+$"));
        assertTrue("TODO_TYPES".matches("^[A-Z_]+$"));
    }
}
