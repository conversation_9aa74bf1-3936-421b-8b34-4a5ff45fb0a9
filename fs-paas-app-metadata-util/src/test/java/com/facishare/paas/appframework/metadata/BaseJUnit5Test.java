package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.foundation.boot.Injector;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * JUnit5测试基类 - 提供通用的测试配置和Mock设置
 * 
 * GenerateByAI
 * 
 * 功能：
 * - 统一的MockitoExtension配置
 * - 通用的Mock设置和测试常量
 * - Spring上下文Mock配置
 * - 测试前的通用初始化
 * 
 * 使用方式：
 * - 继承此类可获得标准化的测试环境
 * - 自动配置常用的Mock对象
 * - 解决Spring上下文依赖问题
 */
@ExtendWith(MockitoExtension.class)
public abstract class BaseJUnit5Test {
    
    // 测试常量
    protected static final String TEST_TENANT_ID = "test-tenant-123";
    protected static final String TEST_USER_ID = "test-user-456";
    protected static final String TEST_API_NAME = "TestObj";
    protected static final String TEST_FIELD_NAME = "test_field";
    protected static final String TEST_LABEL = "测试标签";
    
    // 通用Mock对象
    protected User mockUser;
    protected ApplicationContext mockApplicationContext;
    
    // 静态Mock
    protected MockedStatic<SpringUtil> mockedSpringUtil;
    protected MockedStatic<Injector> mockedInjector;
    
    /**
     * 测试前的通用设置
     */
    @BeforeEach
    void baseSetUp() {
        // 创建通用Mock对象
        mockUser = TestUtils.createDefaultMockUser();
        mockApplicationContext = Mockito.mock(ApplicationContext.class);
        
        // Mock Spring相关的静态方法
        setupSpringMocks();
        
        // 调用子类的具体设置
        specificSetUp();
    }
    
    /**
     * 设置Spring相关的Mock
     */
    private void setupSpringMocks() {
        try {
            // Mock SpringUtil
            mockedSpringUtil = mockStatic(SpringUtil.class);
            mockedSpringUtil.when(SpringUtil::getContext).thenReturn(mockApplicationContext);
            
            // Mock Injector
            mockedInjector = mockStatic(Injector.class);
            mockedInjector.when(Injector::get).thenReturn(mockApplicationContext);
            
        } catch (Exception e) {
            // 如果Mock失败，记录但不中断测试
            System.err.println("Warning: Failed to setup Spring mocks: " + e.getMessage());
        }
    }
    
    /**
     * 子类可以重写此方法进行特定的设置
     */
    protected void specificSetUp() {
        // 默认空实现，子类可以重写
    }
    
    /**
     * 清理静态Mock（在测试类结束时调用）
     */
    protected void cleanupStaticMocks() {
        if (mockedSpringUtil != null) {
            mockedSpringUtil.close();
        }
        if (mockedInjector != null) {
            mockedInjector.close();
        }
    }
    
    /**
     * 创建测试用的User对象
     * 
     * @return Mock的User对象
     */
    protected User createTestUser() {
        return TestUtils.createMockUser(TEST_TENANT_ID, TEST_USER_ID);
    }
    
    /**
     * 创建外部用户的Mock对象
     *
     * @return Mock的外部User对象
     */
    protected User createOutUser() {
        User outUser = TestUtils.createMockUser(TEST_TENANT_ID, TEST_USER_ID);
        when(outUser.isOutUser()).thenReturn(true);
        return outUser;
    }

    /**
     * 创建测试数据构造器
     *
     * @return TestDataBuilder.ObjectDataBuilder
     */
    protected TestDataBuilder.ObjectDataBuilder createObjectDataBuilder() {
        return TestDataBuilder.objectData()
                .withTenantId(TEST_TENANT_ID)
                .withDescribeApiName(TEST_API_NAME);
    }

    /**
     * 创建Mock场景
     *
     * @return MockObjectFactory.MockScenario
     */
    protected MockObjectFactory.MockScenario createMockScenario() {
        return MockObjectFactory.createCompleteScenario();
    }

    /**
     * 断言异常消息包含指定文本
     *
     * @param exception 异常对象
     * @param expectedMessage 期望的消息文本
     */
    protected void assertExceptionMessage(Exception exception, String expectedMessage) {
        assertNotNull(exception, "异常不应为null");
        assertNotNull(exception.getMessage(), "异常消息不应为null");
        assertTrue(exception.getMessage().contains(expectedMessage),
                   "异常消息应包含: " + expectedMessage + ", 实际消息: " + exception.getMessage());
    }

    /**
     * 验证Mock对象的方法被调用
     *
     * @param mock Mock对象
     * @param methodName 方法名（用于错误消息）
     */
    protected void verifyMockCalled(Object mock, String methodName) {
        try {
            verify(mock, atLeastOnce());
        } catch (Exception e) {
            fail("Mock对象的方法 " + methodName + " 应该被调用");
        }
    }

    /**
     * 创建测试用的Map数据
     *
     * @return 包含测试数据的Map
     */
    protected java.util.Map<String, Object> createTestMap() {
        return TestUtils.createTestDataMap();
    }
}
