package com.facishare.paas.appframework.metadata.layout;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LayoutAgentType的JUnit 5测试类
 * 测试布局代理类型枚举功能
 */
class LayoutAgentTypeJUnit5Test {

    /**
     * 测试内容描述：测试所有枚举值
     */
    @ParameterizedTest
    @EnumSource(LayoutAgentType.class)
    @DisplayName("参数化测试 - 所有枚举值")
    void testAllEnumValues(LayoutAgentType agentType) {
        // Assert: 验证枚举值不为null
        assertNotNull(agentType);
        assertNotNull(agentType.name());
        assertFalse(agentType.name().trim().isEmpty());
    }

    /**
     * 测试内容描述：测试MOBILE枚举值
     */
    @Test
    @DisplayName("枚举值测试 - MOBILE枚举值")
    void testMobileEnum() {
        // Act: 获取MOBILE枚举值
        LayoutAgentType mobile = LayoutAgentType.MOBILE;

        // Assert: 验证MOBILE枚举值
        assertNotNull(mobile);
        assertEquals("MOBILE", mobile.name());
        assertEquals(0, mobile.ordinal());
        assertEquals("mobile", mobile.getCode());
    }

    /**
     * 测试内容描述：测试WEB枚举值
     */
    @Test
    @DisplayName("枚举值测试 - WEB枚举值")
    void testWebEnum() {
        // Act: 获取WEB枚举值
        LayoutAgentType web = LayoutAgentType.WEB;

        // Assert: 验证WEB枚举值
        assertNotNull(web);
        assertEquals("WEB", web.name());
        assertEquals(1, web.ordinal());
        assertEquals("web", web.getCode());
    }

    /**
     * 测试内容描述：测试SIDEBAR枚举值
     */
    @Test
    @DisplayName("枚举值测试 - SIDEBAR枚举值")
    void testSidebarEnum() {
        // Act: 获取SIDEBAR枚举值
        LayoutAgentType sidebar = LayoutAgentType.SIDEBAR;

        // Assert: 验证SIDEBAR枚举值
        assertNotNull(sidebar);
        assertEquals("SIDEBAR", sidebar.name());
        assertEquals(2, sidebar.ordinal());
        assertEquals("sidebar", sidebar.getCode());
    }

    /**
     * 测试内容描述：测试valueOf方法
     */
    @Test
    @DisplayName("枚举方法 - valueOf方法")
    void testValueOf() {
        // Act & Assert: 测试valueOf方法
        assertEquals(LayoutAgentType.MOBILE, LayoutAgentType.valueOf("MOBILE"));
        assertEquals(LayoutAgentType.WEB, LayoutAgentType.valueOf("WEB"));
        assertEquals(LayoutAgentType.SIDEBAR, LayoutAgentType.valueOf("SIDEBAR"));
    }

    /**
     * 测试内容描述：测试getCode方法
     */
    @Test
    @DisplayName("业务方法 - getCode方法")
    void testGetCode() {
        // Act & Assert: 测试getCode方法
        assertEquals("mobile", LayoutAgentType.MOBILE.getCode());
        assertEquals("web", LayoutAgentType.WEB.getCode());
        assertEquals("sidebar", LayoutAgentType.SIDEBAR.getCode());
    }

    /**
     * 测试内容描述：测试of方法 - 正常情况
     */
    @Test
    @DisplayName("业务方法 - of方法正常情况")
    void testOf_ValidCodes() {
        // Act & Assert: 测试of方法
        assertEquals(LayoutAgentType.MOBILE, LayoutAgentType.of("mobile"));
        assertEquals(LayoutAgentType.WEB, LayoutAgentType.of("web"));
        assertEquals(LayoutAgentType.SIDEBAR, LayoutAgentType.of("sidebar"));
    }

    /**
     * 测试内容描述：测试of方法 - 无效代码
     */
    @Test
    @DisplayName("业务方法 - of方法无效代码")
    void testOf_InvalidCodes() {
        // Act & Assert: 测试of方法返回null
        assertNull(LayoutAgentType.of("invalid"));
        assertNull(LayoutAgentType.of("MOBILE")); // 大写应该返回null
        assertNull(LayoutAgentType.of(""));
        assertNull(LayoutAgentType.of(null));
    }

    /**
     * 测试内容描述：测试valueOf方法异常情况
     */
    @Test
    @DisplayName("异常处理 - valueOf方法异常情况")
    void testValueOf_InvalidValue() {
        // Act & Assert: 验证无效值抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            LayoutAgentType.valueOf("INVALID_TYPE");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            LayoutAgentType.valueOf("sidebar"); // 小写应该抛出异常
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            LayoutAgentType.valueOf("");
        });
    }

    /**
     * 测试内容描述：测试valueOf方法null参数
     */
    @Test
    @DisplayName("异常处理 - valueOf方法null参数")
    void testValueOf_NullParameter() {
        // Act & Assert: 验证null参数抛出异常
        assertThrows(NullPointerException.class, () -> {
            LayoutAgentType.valueOf(null);
        });
    }

    /**
     * 测试内容描述：测试values方法
     */
    @Test
    @DisplayName("枚举方法 - values方法")
    void testValues() {
        // Act: 执行values方法
        LayoutAgentType[] values = LayoutAgentType.values();

        // Assert: 验证values方法结果
        assertNotNull(values);
        assertEquals(3, values.length); // 应该有3个枚举值

        // 验证包含预期的枚举值
        assertEquals(LayoutAgentType.MOBILE, values[0]);
        assertEquals(LayoutAgentType.WEB, values[1]);
        assertEquals(LayoutAgentType.SIDEBAR, values[2]);
    }

    /**
     * 测试内容描述：测试枚举的toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // Act & Assert: 测试toString方法
        assertEquals("MOBILE", LayoutAgentType.MOBILE.toString());
        assertEquals("WEB", LayoutAgentType.WEB.toString());
        assertEquals("SIDEBAR", LayoutAgentType.SIDEBAR.toString());
    }

    /**
     * 测试内容描述：测试枚举的name方法
     */
    @Test
    @DisplayName("枚举方法 - name方法")
    void testName() {
        // Act & Assert: 测试name方法
        assertEquals("MOBILE", LayoutAgentType.MOBILE.name());
        assertEquals("WEB", LayoutAgentType.WEB.name());
        assertEquals("SIDEBAR", LayoutAgentType.SIDEBAR.name());
    }

    /**
     * 测试内容描述：测试枚举的ordinal方法
     */
    @Test
    @DisplayName("枚举方法 - ordinal方法")
    void testOrdinal() {
        // Act & Assert: 测试ordinal方法
        assertEquals(0, LayoutAgentType.MOBILE.ordinal());
        assertEquals(1, LayoutAgentType.WEB.ordinal());
        assertEquals(2, LayoutAgentType.SIDEBAR.ordinal());
    }

    /**
     * 测试内容描述：测试枚举的equals方法
     */
    @Test
    @DisplayName("对象比较 - equals方法")
    void testEquals() {
        // Act & Assert: 测试equals方法
        assertEquals(LayoutAgentType.SIDEBAR, LayoutAgentType.SIDEBAR);
        
        // 验证与自身相等
        LayoutAgentType sidebar1 = LayoutAgentType.SIDEBAR;
        LayoutAgentType sidebar2 = LayoutAgentType.SIDEBAR;
        assertEquals(sidebar1, sidebar2);
        assertSame(sidebar1, sidebar2);
    }

    /**
     * 测试内容描述：测试枚举的hashCode方法
     */
    @Test
    @DisplayName("对象比较 - hashCode方法")
    void testHashCode() {
        // Act: 获取hashCode
        int hashCode1 = LayoutAgentType.SIDEBAR.hashCode();
        int hashCode2 = LayoutAgentType.SIDEBAR.hashCode();
        
        // Assert: 验证hashCode一致性
        assertEquals(hashCode1, hashCode2);
    }

    /**
     * 测试内容描述：测试枚举在switch语句中的使用
     */
    @Test
    @DisplayName("使用场景 - switch语句中的使用")
    void testEnumInSwitch() {
        // Act & Assert: 测试在switch语句中使用枚举
        for (LayoutAgentType agentType : LayoutAgentType.values()) {
            String description = getAgentTypeDescription(agentType);
            assertNotNull(description);
            assertFalse(description.trim().isEmpty());
        }
    }

    private String getAgentTypeDescription(LayoutAgentType agentType) {
        switch (agentType) {
            case MOBILE:
                return "移动端代理类型，用于移动端布局";
            case WEB:
                return "Web端代理类型，用于Web端布局";
            case SIDEBAR:
                return "侧边栏代理类型，用于侧边栏布局";
            default:
                return "未知代理类型";
        }
    }

    /**
     * 测试内容描述：测试枚举的序列化兼容性
     */
    @Test
    @DisplayName("序列化 - 枚举序列化兼容性")
    void testEnumSerialization() {
        // Act & Assert: 验证枚举可以被序列化
        for (LayoutAgentType agentType : LayoutAgentType.values()) {
            // 验证枚举实现了Serializable接口
            assertTrue(agentType instanceof java.io.Serializable);
            
            // 验证枚举的name和toString一致性
            assertEquals(agentType.name(), agentType.toString());
        }
    }

    /**
     * 测试内容描述：测试枚举的线程安全性
     */
    @Test
    @DisplayName("线程安全 - 枚举线程安全性")
    void testEnumThreadSafety() {
        // Act & Assert: 验证枚举的线程安全性
        // 枚举天然是线程安全的，这里验证多次访问的一致性
        LayoutAgentType sidebar1 = LayoutAgentType.SIDEBAR;
        LayoutAgentType sidebar2 = LayoutAgentType.SIDEBAR;
        
        assertSame(sidebar1, sidebar2);
        assertEquals(sidebar1.ordinal(), sidebar2.ordinal());
        
        // 验证在不同线程中访问的一致性
        Thread thread1 = new Thread(() -> {
            LayoutAgentType threadAgentType = LayoutAgentType.SIDEBAR;
            assertEquals("SIDEBAR", threadAgentType.name());
        });
        
        Thread thread2 = new Thread(() -> {
            LayoutAgentType threadAgentType = LayoutAgentType.SIDEBAR;
            assertEquals(2, threadAgentType.ordinal());
        });
        
        assertDoesNotThrow(() -> {
            thread1.start();
            thread2.start();
            thread1.join();
            thread2.join();
        });
    }

    /**
     * 测试内容描述：测试业务场景 - 代理类型判断
     */
    @Test
    @DisplayName("业务场景 - 代理类型判断")
    void testAgentTypeIdentification() {
        // Act & Assert: 测试代理类型判断
        LayoutAgentType agentType = LayoutAgentType.SIDEBAR;
        
        assertTrue(isSidebarType(agentType));
        assertFalse(isUnknownType(agentType));
    }

    private boolean isSidebarType(LayoutAgentType agentType) {
        return agentType == LayoutAgentType.SIDEBAR;
    }

    private boolean isUnknownType(LayoutAgentType agentType) {
        return agentType == null;
    }

    /**
     * 测试内容描述：测试业务场景 - 代理类型配置
     */
    @Test
    @DisplayName("业务场景 - 代理类型配置")
    void testAgentTypeConfiguration() {
        // Act & Assert: 测试代理类型配置
        LayoutAgentType configuredType = LayoutAgentType.SIDEBAR;
        
        // 验证配置的代理类型
        assertNotNull(configuredType);
        assertEquals("SIDEBAR", configuredType.name());
        
        // 验证配置有效性
        assertTrue(isValidAgentType(configuredType));
    }

    private boolean isValidAgentType(LayoutAgentType agentType) {
        if (agentType == null) {
            return false;
        }
        
        // 检查是否是已知的代理类型
        for (LayoutAgentType validType : LayoutAgentType.values()) {
            if (validType == agentType) {
                return true;
            }
        }
        return false;
    }

    /**
     * 测试内容描述：测试业务场景 - 代理类型转换
     */
    @Test
    @DisplayName("业务场景 - 代理类型转换")
    void testAgentTypeConversion() {
        // Act & Assert: 测试代理类型转换
        String typeString = "SIDEBAR";
        LayoutAgentType convertedType = LayoutAgentType.valueOf(typeString);
        
        assertEquals(LayoutAgentType.SIDEBAR, convertedType);
        assertEquals(typeString, convertedType.name());
        
        // 验证转换的一致性
        String backToString = convertedType.toString();
        assertEquals(typeString, backToString);
    }

    /**
     * 测试内容描述：测试性能 - 大量枚举操作
     */
    @Test
    @DisplayName("性能测试 - 大量枚举操作")
    void testPerformance() {
        // Arrange: 准备性能测试
        int iterations = 10000;
        
        // Act: 执行大量枚举操作
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            LayoutAgentType agentType = LayoutAgentType.SIDEBAR;
            assertNotNull(agentType.name());
            assertTrue(agentType.ordinal() >= 0);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // Assert: 验证性能（应该在合理时间内完成）
        assertTrue(duration < 1000, "大量枚举操作应该在1秒内完成，实际耗时: " + duration + "ms");
    }

    /**
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Act: 多次获取相同的枚举值
        LayoutAgentType type1 = LayoutAgentType.SIDEBAR;
        LayoutAgentType type2 = LayoutAgentType.SIDEBAR;
        LayoutAgentType type3 = LayoutAgentType.valueOf("SIDEBAR");
        
        // Assert: 验证数据一致性
        assertSame(type1, type2);
        assertSame(type1, type3);
        assertEquals(type1.name(), type2.name());
        assertEquals(type1.ordinal(), type3.ordinal());
    }
}
