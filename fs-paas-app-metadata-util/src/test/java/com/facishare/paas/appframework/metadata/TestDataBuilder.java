package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 测试数据构造器 - 提供结构化的测试数据构造
 * 
 * GenerateByAI
 * 
 * 功能：
 * - 使用Builder模式构造复杂测试数据
 * - 提供链式调用的数据构造API
 * - 支持随机数据和固定数据生成
 * 
 * 使用场景：
 * - 复杂测试场景的数据准备
 * - 参数化测试的数据生成
 * - 性能测试的大量数据构造
 */
public class TestDataBuilder {
    
    /**
     * ObjectData构造器
     */
    public static class ObjectDataBuilder {
        private final Map<String, Object> data = Maps.newHashMap();
        
        public ObjectDataBuilder withId(String id) {
            data.put("_id", id);
            return this;
        }
        
        public ObjectDataBuilder withRandomId() {
            data.put("_id", "test-" + UUID.randomUUID().toString().substring(0, 8));
            return this;
        }
        
        public ObjectDataBuilder withName(String name) {
            data.put("name", name);
            return this;
        }
        
        public ObjectDataBuilder withDescription(String description) {
            data.put("description", description);
            return this;
        }
        
        public ObjectDataBuilder withTenantId(String tenantId) {
            data.put("tenant_id", tenantId);
            return this;
        }
        
        public ObjectDataBuilder withDescribeApiName(String apiName) {
            data.put("describe_api_name", apiName);
            return this;
        }
        
        public ObjectDataBuilder withField(String fieldName, Object value) {
            data.put(fieldName, value);
            return this;
        }
        
        public ObjectDataBuilder withCurrentTimestamp() {
            long timestamp = LocalDateTime.now().toEpochSecond(ZoneOffset.UTC) * 1000;
            data.put("created_time", timestamp);
            data.put("updated_time", timestamp);
            return this;
        }
        
        public ObjectDataBuilder withOwner(String userId) {
            data.put("owner", Lists.newArrayList(userId));
            return this;
        }
        
        public Map<String, Object> buildMap() {
            return Maps.newHashMap(data);
        }
        
        public IObjectData buildMock() {
            return TestUtils.createMockObjectData(buildMap());
        }
    }
    
    /**
     * User构造器
     */
    public static class UserBuilder {
        private String tenantId = TestUtils.TEST_TENANT_ID;
        private String userId = TestUtils.TEST_USER_ID;
        private boolean isOutUser = false;
        private String outTenantId = null;
        private String outUserId = null;
        
        public UserBuilder withTenantId(String tenantId) {
            this.tenantId = tenantId;
            return this;
        }
        
        public UserBuilder withUserId(String userId) {
            this.userId = userId;
            return this;
        }
        
        public UserBuilder asOutUser(String outTenantId, String outUserId) {
            this.isOutUser = true;
            this.outTenantId = outTenantId;
            this.outUserId = outUserId;
            return this;
        }
        
        public UserBuilder asInternalUser() {
            this.isOutUser = false;
            this.outTenantId = null;
            this.outUserId = null;
            return this;
        }
        
        public User buildMock() {
            User mockUser = TestUtils.createMockUser(tenantId, userId);
            if (isOutUser) {
                org.mockito.Mockito.when(mockUser.isOutUser()).thenReturn(true);
                org.mockito.Mockito.when(mockUser.getOutTenantId()).thenReturn(outTenantId);
                org.mockito.Mockito.when(mockUser.getOutUserId()).thenReturn(outUserId);
            }
            return mockUser;
        }
    }
    
    /**
     * 测试场景构造器
     */
    public static class ScenarioBuilder {
        private User user;
        private IObjectDescribe objectDescribe;
        private List<IObjectData> dataList = Lists.newArrayList();
        private Map<String, Object> context = Maps.newHashMap();
        
        public ScenarioBuilder withUser(User user) {
            this.user = user;
            return this;
        }
        
        public ScenarioBuilder withDefaultUser() {
            this.user = TestUtils.createDefaultMockUser();
            return this;
        }
        
        public ScenarioBuilder withObjectDescribe(IObjectDescribe describe) {
            this.objectDescribe = describe;
            return this;
        }
        
        public ScenarioBuilder withMockObjectDescribe(String apiName) {
            this.objectDescribe = MockObjectFactory.createMockObjectDescribeWithFields(apiName, 3);
            return this;
        }
        
        public ScenarioBuilder withObjectData(IObjectData data) {
            this.dataList.add(data);
            return this;
        }
        
        public ScenarioBuilder withObjectDataList(List<IObjectData> dataList) {
            this.dataList.addAll(dataList);
            return this;
        }
        
        public ScenarioBuilder withContext(String key, Object value) {
            this.context.put(key, value);
            return this;
        }
        
        public TestScenario build() {
            return new TestScenario(user, objectDescribe, dataList, context);
        }
    }
    
    /**
     * 测试场景数据容器
     */
    public static class TestScenario {
        private final User user;
        private final IObjectDescribe objectDescribe;
        private final List<IObjectData> dataList;
        private final Map<String, Object> context;
        
        public TestScenario(User user, IObjectDescribe objectDescribe, 
                           List<IObjectData> dataList, Map<String, Object> context) {
            this.user = user;
            this.objectDescribe = objectDescribe;
            this.dataList = dataList;
            this.context = context;
        }
        
        public User getUser() { return user; }
        public IObjectDescribe getObjectDescribe() { return objectDescribe; }
        public List<IObjectData> getDataList() { return dataList; }
        public IObjectData getFirstData() { return dataList.isEmpty() ? null : dataList.get(0); }
        public Map<String, Object> getContext() { return context; }
        public Object getContextValue(String key) { return context.get(key); }
    }
    
    // 静态工厂方法
    public static ObjectDataBuilder objectData() {
        return new ObjectDataBuilder();
    }
    
    public static UserBuilder user() {
        return new UserBuilder();
    }
    
    public static ScenarioBuilder scenario() {
        return new ScenarioBuilder();
    }
    
    // 常用预设数据
    public static class Presets {
        
        public static IObjectData simpleObjectData() {
            return objectData()
                    .withRandomId()
                    .withName("测试对象")
                    .withDescription("测试描述")
                    .withTenantId(TestUtils.TEST_TENANT_ID)
                    .withDescribeApiName(TestUtils.TEST_API_NAME)
                    .withCurrentTimestamp()
                    .buildMock();
        }
        
        public static User adminUser() {
            return user()
                    .withTenantId("admin-tenant")
                    .withUserId("admin-user")
                    .asInternalUser()
                    .buildMock();
        }
        
        public static User externalUser() {
            return user()
                    .withTenantId("internal-tenant")
                    .withUserId("internal-user")
                    .asOutUser("external-tenant", "external-user")
                    .buildMock();
        }
        
        public static TestScenario basicCrudScenario() {
            return scenario()
                    .withDefaultUser()
                    .withMockObjectDescribe("TestObject")
                    .withObjectData(simpleObjectData())
                    .withContext("operation", "create")
                    .build();
        }
    }
}
