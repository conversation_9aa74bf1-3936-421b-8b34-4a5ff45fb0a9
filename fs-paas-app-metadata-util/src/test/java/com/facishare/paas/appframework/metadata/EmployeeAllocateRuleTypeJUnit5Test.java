package com.facishare.paas.appframework.metadata;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EmployeeAllocateRuleType的JUnit 5测试类
 * 测试员工分配规则类型枚举功能
 */
class EmployeeAllocateRuleTypeJUnit5Test {

    /**
     * 测试内容描述：测试枚举基本属性
     */
    @Test
    @DisplayName("枚举基础 - 枚举值基本属性")
    void testEnumBasicProperties() {
        // 验证所有枚举值
        assertEquals(2, EmployeeAllocateRuleType.values().length);
        
        // 验证枚举值存在
        assertNotNull(EmployeeAllocateRuleType.AUTO);
        assertNotNull(EmployeeAllocateRuleType.MANUAL);
    }

    /**
     * 测试内容描述：测试getType方法 - 参数化测试
     */
    @ParameterizedTest
    @CsvSource({
        "AUTO, auto",
        "MANUAL, manual"
    })
    @DisplayName("类型获取 - getType方法验证所有枚举值")
    void testGetType_AllValues(EmployeeAllocateRuleType ruleType, String expectedType) {
        // Act: 执行测试方法
        String result = ruleType.getType();

        // Assert: 验证结果
        assertEquals(expectedType, result);
    }

    /**
     * 测试内容描述：测试枚举的ordinal值
     */
    @Test
    @DisplayName("枚举顺序 - ordinal值验证")
    void testOrdinalValues() {
        assertEquals(0, EmployeeAllocateRuleType.AUTO.ordinal());
        assertEquals(1, EmployeeAllocateRuleType.MANUAL.ordinal());
    }

    /**
     * 测试内容描述：测试枚举的name方法
     */
    @Test
    @DisplayName("名称获取 - name方法")
    void testNameMethod() {
        assertEquals("AUTO", EmployeeAllocateRuleType.AUTO.name());
        assertEquals("MANUAL", EmployeeAllocateRuleType.MANUAL.name());
    }

    /**
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // 枚举的toString默认返回name()
        assertEquals("AUTO", EmployeeAllocateRuleType.AUTO.toString());
        assertEquals("MANUAL", EmployeeAllocateRuleType.MANUAL.toString());
    }

    /**
     * 测试内容描述：测试valueOf(String)方法
     */
    @Test
    @DisplayName("名称转换 - valueOf(String)方法")
    void testValueOf_StringName() {
        // 测试有效的枚举名称
        assertEquals(EmployeeAllocateRuleType.AUTO, EmployeeAllocateRuleType.valueOf("AUTO"));
        assertEquals(EmployeeAllocateRuleType.MANUAL, EmployeeAllocateRuleType.valueOf("MANUAL"));
    }

    /**
     * 测试内容描述：测试valueOf(String)方法 - 无效名称
     */
    @Test
    @DisplayName("边界条件 - valueOf(String)方法无效名称")
    void testValueOf_InvalidStringName() {
        assertThrows(IllegalArgumentException.class, () -> {
            EmployeeAllocateRuleType.valueOf("INVALID_TYPE");
        });
    }

    /**
     * 测试内容描述：测试valueOf(String)方法 - null名称
     */
    @Test
    @DisplayName("边界条件 - valueOf(String)方法null名称")
    void testValueOf_NullStringName() {
        assertThrows(NullPointerException.class, () -> {
            EmployeeAllocateRuleType.valueOf((String) null);
        });
    }

    /**
     * 测试内容描述：测试枚举的equals方法
     */
    @Test
    @DisplayName("对象比较 - equals方法")
    void testEquals() {
        // 枚举实例是单例的
        assertSame(EmployeeAllocateRuleType.AUTO, EmployeeAllocateRuleType.valueOf("AUTO"));
        assertSame(EmployeeAllocateRuleType.MANUAL, EmployeeAllocateRuleType.valueOf("MANUAL"));
        
        // 测试equals
        assertEquals(EmployeeAllocateRuleType.AUTO, EmployeeAllocateRuleType.valueOf("AUTO"));
        assertNotEquals(EmployeeAllocateRuleType.AUTO, EmployeeAllocateRuleType.MANUAL);
    }

    /**
     * 测试内容描述：测试枚举的hashCode方法
     */
    @Test
    @DisplayName("哈希码 - hashCode方法")
    void testHashCode() {
        // 相同枚举值的hashCode应该相同
        assertEquals(EmployeeAllocateRuleType.AUTO.hashCode(), EmployeeAllocateRuleType.valueOf("AUTO").hashCode());
        assertEquals(EmployeeAllocateRuleType.MANUAL.hashCode(), EmployeeAllocateRuleType.valueOf("MANUAL").hashCode());
        
        // 不同枚举值的hashCode应该不同
        assertNotEquals(EmployeeAllocateRuleType.AUTO.hashCode(), EmployeeAllocateRuleType.MANUAL.hashCode());
    }

    /**
     * 测试内容描述：测试values方法
     */
    @Test
    @DisplayName("枚举数组 - values方法")
    void testValues() {
        EmployeeAllocateRuleType[] values = EmployeeAllocateRuleType.values();
        
        assertNotNull(values);
        assertEquals(2, values.length);
        
        // 验证顺序
        assertEquals(EmployeeAllocateRuleType.AUTO, values[0]);
        assertEquals(EmployeeAllocateRuleType.MANUAL, values[1]);
    }

    /**
     * 测试内容描述：测试枚举的完整性和一致性
     */
    @Test
    @DisplayName("完整性验证 - 枚举一致性检查")
    void testEnumConsistency() {
        // 验证所有枚举值都有对应的type
        for (EmployeeAllocateRuleType ruleType : EmployeeAllocateRuleType.values()) {
            assertNotNull(ruleType.getType());
            assertFalse(ruleType.getType().trim().isEmpty());
        }
    }

    /**
     * 测试内容描述：测试业务逻辑 - 分配规则的含义
     */
    @Test
    @DisplayName("业务逻辑 - 分配规则含义验证")
    void testBusinessLogic() {
        // 验证AUTO表示自动分配
        assertEquals("auto", EmployeeAllocateRuleType.AUTO.getType());
        
        // 验证MANUAL表示手动分配
        assertEquals("manual", EmployeeAllocateRuleType.MANUAL.getType());
        
        // 验证类型值的唯一性
        assertNotEquals(EmployeeAllocateRuleType.AUTO.getType(), EmployeeAllocateRuleType.MANUAL.getType());
    }

    /**
     * 测试内容描述：测试类型值的唯一性
     */
    @Test
    @DisplayName("唯一性验证 - 类型值唯一性")
    void testTypeUniqueness() {
        EmployeeAllocateRuleType[] values = EmployeeAllocateRuleType.values();
        
        // 验证所有类型值都是唯一的
        for (int i = 0; i < values.length; i++) {
            for (int j = i + 1; j < values.length; j++) {
                assertNotEquals(values[i].getType(), values[j].getType(),
                    "类型值应该是唯一的: " + values[i].name() + " vs " + values[j].name());
            }
        }
    }

    /**
     * 测试内容描述：测试final字段的不可变性
     */
    @Test
    @DisplayName("不可变性 - final字段验证")
    void testImmutability() {
        // 验证type字段是final的，通过多次调用getType()验证返回值一致
        EmployeeAllocateRuleType auto = EmployeeAllocateRuleType.AUTO;
        String type1 = auto.getType();
        String type2 = auto.getType();
        
        assertSame(type1, type2); // 应该返回相同的字符串实例
        assertEquals(type1, type2);
    }

    /**
     * 测试内容描述：测试枚举在switch语句中的使用
     */
    @Test
    @DisplayName("使用场景 - switch语句兼容性")
    void testSwitchCompatibility() {
        // 测试枚举可以在switch语句中正常使用
        for (EmployeeAllocateRuleType ruleType : EmployeeAllocateRuleType.values()) {
            String result;
            switch (ruleType) {
                case AUTO:
                    result = "自动分配";
                    break;
                case MANUAL:
                    result = "手动分配";
                    break;
                default:
                    result = "未知分配";
                    break;
            }

            assertNotNull(result);
            assertFalse(result.trim().isEmpty());
        }
    }

    /**
     * 测试内容描述：测试枚举的序列化兼容性
     */
    @Test
    @DisplayName("序列化 - 枚举序列化兼容性")
    void testSerializationCompatibility() {
        // 验证枚举可以通过name()和valueOf()进行序列化和反序列化
        for (EmployeeAllocateRuleType original : EmployeeAllocateRuleType.values()) {
            String serialized = original.name();
            EmployeeAllocateRuleType deserialized = EmployeeAllocateRuleType.valueOf(serialized);
            
            assertSame(original, deserialized);
            assertEquals(original.getType(), deserialized.getType());
        }
    }
}
