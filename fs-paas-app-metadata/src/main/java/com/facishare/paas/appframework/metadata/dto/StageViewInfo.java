package com.facishare.paas.appframework.metadata.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.TableColumnExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2020/02/05
 */
@Data
public class StageViewInfo {
    @JsonProperty("api_name")
    @JSONField(name = "api_name")
    private String apiName;
    private String label;
    @JsonProperty("render_type")
    @JSONField(name = "render_type")
    private String renderType;

    public ITableColumn toIncludeField() {
        return TableColumnExt.of(apiName, label, renderType).getTableColumn();
    }

    public static List<StageViewInfo> fromJsonArray(String json) {
        return CollectionUtils.nullToEmpty(JSON.parseArray(json, StageViewInfo.class));
    }

    public static StageViewInfo fromIncludeField(ITableColumn column) {
        StageViewInfo stageViewInfo = new StageViewInfo();
        stageViewInfo.setApiName(getApiName(column));
        stageViewInfo.setLabel(column.getLabelName());
        stageViewInfo.setRenderType(column.getRenderType());
        return stageViewInfo;
    }

    public static StageViewInfo fromFormField(IFormField field) {
        StageViewInfo stageViewInfo = new StageViewInfo();
        stageViewInfo.setApiName(field.getFieldName());
        stageViewInfo.setRenderType(field.getRenderType());
        return stageViewInfo;
    }

    private static String getApiName(ITableColumn column) {
        String name = column.getName();
        if (!Strings.isNullOrEmpty(name)) {
            return name;
        }
        return column.get(IObjectDescribe.API_NAME, String.class);
    }

    public static List<StageViewInfo> fromIncludeFields(List<ITableColumn> columns) {
        return columns.stream().map(StageViewInfo::fromIncludeField).collect(Collectors.toList());
    }

    public static List<StageViewInfo> fromFormFields(List<IFormField> columns) {
        return columns.stream().map(StageViewInfo::fromFormField).collect(Collectors.toList());
    }

    public static List<StageViewInfo> fromComponent(TableComponent component) {
        List<ITableColumn> includeFields = component.getIncludeFields();
        if (CollectionUtils.notEmpty(includeFields)) {
            return fromIncludeFields(includeFields);
        }
        return component.getFieldSections().stream()
                .flatMap(fieldSection -> fieldSection.getFields().stream())
                .map(StageViewInfo::fromFormField)
                .collect(Collectors.toList());
    }

    public static TableComponent toTableComponent(List<StageViewInfo> stageViewInfos) {
        List<ITableColumn> includeFields = stageViewInfos.stream()
                .map(StageViewInfo::toIncludeField)
                .collect(Collectors.toList());

        TableComponent tableComponent = new TableComponent();
        tableComponent.setIncludeFields(includeFields);
        return tableComponent;
    }
}
