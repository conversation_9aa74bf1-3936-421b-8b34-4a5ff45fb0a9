package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.MetaDataGlobalService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.dao.pg.entity.metadata.ImportAreaEntity;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class CountryAreaBatchImportFieldDataConverter extends AbstractImportBatchFieldDataConverter {


    @Autowired
    private MetaDataGlobalService metaDataGlobalService;

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(GroupField.GROUP_TYPE_AREA);
    }

    @Override
    protected List<BatchConvertResult> convert(List<BatchConvertData> convertDataList, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, DataConverterContext context) {
        List<BatchConvertResult> convertResults = Lists.newArrayList();
        if (CollectionUtils.empty(convertDataList)) {
            return convertResults;
        }
        List<IObjectData> dataList = convertDataList.stream().map(BatchConvertData::getObjectData).collect(Collectors.toList());
        List<ImportAreaEntity> importAreaEntityList = buildImportAreaEntityList(dataList, fieldDescribe);
        List<ImportAreaEntity> areaInfos = metaDataGlobalService.batchQueryCountryAreasByParentCodeAndLabel(context.getUser(), importAreaEntityList);
        for (BatchConvertData convertData : convertDataList) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(convertData.getObjectData());
            String name = objectDataExt.getStringValueInImport(fieldDescribe.getApiName());
            if (StringUtils.isBlank(name)) {
                continue;
            }
            if (IFieldType.COUNTRY.equals(fieldDescribe.getType())) {
                String code = areaInfos.stream()
                        .filter(x -> Objects.equals(x.getName(), name))
                        .map(ImportAreaEntity::getFsUniqueCode)
                        .findFirst()
                        .orElse(null);
                if (StringUtils.isBlank(code)) {
                    convertResults.add(BatchConvertResult.of(ConvertResult.buildError(I18NExt.getOrDefault(I18NKey.IMPORT_AREA_NOT_EXIST, "{0}不存在", name)), convertData.getNo()));// ignoreI18n
                    continue;
                }
                objectDataExt.set(fieldDescribe.getApiName(), code);
            } else {
                SelectOne countryAreaFieldDescribe = (SelectOne) fieldDescribe;
                String parentApiName = countryAreaFieldDescribe.getCascadeParentApiName();
                String parentCode = objectDataExt.getStringValueInImport(parentApiName);
                if (StringUtils.isBlank(parentCode)) {
                    convertResults.add(BatchConvertResult.of(ConvertResult.buildError(I18NExt.getOrDefault(I18NKey.AREA_NOT_NULL,
                            "{0}不能为空", ObjectDescribeExt.of(objectDescribe).getFieldLabelByName(parentApiName))), convertData.getNo()));// ignoreI18n
                    continue;
                }
                if (!NumberUtils.isDigits(parentCode)) {
                    continue;
                }
                String code = areaInfos.stream()
                        .filter(x -> Objects.equals(x.getName(), name) && Objects.equals(x.getParentFsuniqueCode(), parentCode))
                        .map(ImportAreaEntity::getFsUniqueCode)
                        .findFirst()
                        .orElse(null);
                if (StringUtils.isBlank(code)) {
                    convertResults.add(BatchConvertResult.of(ConvertResult.buildError(I18NExt.getOrDefault(I18NKey.IMPORT_AREA_NOT_MATCH,
                            "【{0}}】与【{1}】级联关系不存在，或【{2}}】名称不正确。", objectDataExt.getCountryLabelInImport(parentApiName), name, name)), convertData.getNo()));// ignoreI18n
                } else {
                    objectDataExt.set(fieldDescribe.getApiName(), code);
                }
            }
        }
        return convertResults;
    }


    public List<ImportAreaEntity> buildImportAreaEntityList(List<IObjectData> dataList, IFieldDescribe fieldDescribe) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        List<ImportAreaEntity> importAreaEntityList = Lists.newArrayList();
        SelectOne countryAreaFieldDescribe = (SelectOne) fieldDescribe;
        for (IObjectData data : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            String name = dataExt.getStringValueInImport(fieldDescribe.getApiName());
            if (Strings.isNullOrEmpty(name)) {
                continue;
            }
            dataExt.setCountryLabelInImport(fieldDescribe.getApiName(), name);
            if (IFieldType.COUNTRY.equals(fieldDescribe.getType())) {
                ImportAreaEntity importAreaEntity = new ImportAreaEntity();
                importAreaEntity.setName(name);
                importAreaEntityList.add(importAreaEntity);
                continue;
            }
            String parentCode = dataExt.getStringValueInImport(countryAreaFieldDescribe.getCascadeParentApiName());
            if (StringUtils.isBlank(parentCode) || !NumberUtils.isDigits(parentCode)) {
                continue;
            }
            ImportAreaEntity importAreaEntity = new ImportAreaEntity();
            importAreaEntity.setName(name);
            importAreaEntity.setParentFsuniqueCode(parentCode);
            importAreaEntityList.add(importAreaEntity);
        }
        return importAreaEntityList;
    }
}
