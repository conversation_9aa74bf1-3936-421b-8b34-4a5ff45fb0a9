package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribeExtra;

import java.util.List;
import java.util.Map;

/**
 * 对象附加属性业务 Service
 */
public interface ExtraDescribeLogicService {

    /**
     * 保存对象附加属性（会考虑对象描述中的 icon_index）
     * @param tenantId 租户编号
     * @param objDesc 对象描述
     * @param extraList 对象附加描述
     * @return 结果
     */
    List<IObjectDescribeExtra> save(String tenantId, IObjectDescribe objDesc, Map<String, Object> extraList);

    /**
     * 保存对象附加属性
     * @param tenantId 租户编号
     * @param objApi 对象 API
     * @param extraList 对象附加描述
     * @return 结果
     */
    List<IObjectDescribeExtra> save(String tenantId, String objApi, Map<String, Object> extraList);

    /**
     * 查对象附加属性
     * @param tenantId 租户编号
     * @param objApis 对象API
     * @return 结果
     */
    Map<String, IObjectDescribeExtra> find(String tenantId,  List<String> objApis);

}
