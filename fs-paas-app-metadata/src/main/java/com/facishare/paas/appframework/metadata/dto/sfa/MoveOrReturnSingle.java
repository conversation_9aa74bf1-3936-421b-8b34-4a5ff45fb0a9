package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;

public interface MoveOrReturnSingle {
    @Data
    class Arg {
        @JSONField(name = IObjectData.ID)
        String objectID;
        @JSONField(name = "SalesCluePoolID")
        String leadsPoolId;
        @JSONField(name = "HighSeasID")
        String highSeasId;
        @JSONField(name = "BackReason")
        String backReason;
    }
}
