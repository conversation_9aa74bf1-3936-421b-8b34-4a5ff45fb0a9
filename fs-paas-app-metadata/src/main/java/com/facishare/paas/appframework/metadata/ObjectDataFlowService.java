package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.metadata.dto.MasterApprovalResult;
import com.facishare.paas.metadata.api.IObjectData;

/**
 * Created by zhouwr on 2019/11/11
 */
public interface ObjectDataFlowService {

    void saveMasterDataApprovalResult(IObjectData detailData, MasterApprovalResult masterApprovalResult);

    MasterApprovalResult getMasterDataApprovalResult(String tenantId, String objectApiName, String dataId);

}
