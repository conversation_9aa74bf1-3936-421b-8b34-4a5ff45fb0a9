package com.facishare.paas.appframework.metadata.publicobject.verify;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.publicobject.module.InternationalVerifyMessage;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by <PERSON>hao<PERSON>ju on 2023/12/26
 */
@Data
public
class VerifyResult {
    private List<String> messageList;

    private List<InternationalVerifyMessage> messages;

    private VerifyResult(InternationalVerifyMessage message) {
        String defaultMessage = message.getDefaultInternationalValue();
        if (StringUtils.isBlank(defaultMessage)) {
            this.messages = Lists.newArrayList();
            this.messageList = Lists.newArrayList();
        } else {
            this.messages = Lists.newArrayList(message);
            this.messageList = Lists.newArrayList(defaultMessage);
        }
    }

    public static VerifyResult buildError(InternationalVerifyMessage errorMessage) {
        return new VerifyResult(errorMessage);
    }

    public static VerifyResult buildEmpty() {
        return new VerifyResult(InternationalVerifyMessage.of(""));
    }

    public boolean success() {
        return CollectionUtils.empty(messageList);
    }

    private void append(String message) {
        if (StringUtils.isBlank(message)) {
            return;
        }
        messageList.add(message);
    }

    public void append(InternationalVerifyMessage message) {
        messages.add(message);
        append(message.getDefaultInternationalValue());
    }

    public void appends(Collection<InternationalVerifyMessage> messages) {
        if (CollectionUtils.empty(messages)) {
            return;
        }
        messages.forEach(this::append);
    }

    public VerifyResult merge(VerifyResult verifyResult) {
        if (Objects.isNull(verifyResult) || verifyResult.success()) {
            return this;
        }
        appends(verifyResult.getMessages());
        return this;
    }

    public String toMessage() {
        if (CollectionUtils.notEmpty(messages)) {
            return messages.stream()
                    .map(InternationalVerifyMessage::toString)
                    .collect(Collectors.joining(","));
        }
        if (CollectionUtils.empty(messageList)) {
            return null;
        }
        return String.join(";", messageList);
    }
}
