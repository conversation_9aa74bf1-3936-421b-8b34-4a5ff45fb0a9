package com.facishare.paas.appframework.metadata.dto.auth;

import com.facishare.paas.auth.model.RoleViewPojo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface AddRoleViewModel {

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends BaseAuthArg {
        private List<RoleViewPojo> roleViewPojos;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BaseAuthResult {
    }

}
