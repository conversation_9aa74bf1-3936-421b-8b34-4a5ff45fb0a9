package com.facishare.paas.appframework.metadata.fieldextra;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.OrderByExt;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtFieldBackgroundExtra;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.repository.model.MtFieldBackgroundExtra.*;
import static com.facishare.paas.metadata.api.DBRecord.CREATE_TIME;
import static com.facishare.paas.metadata.api.Tenantable.TENANT_ID;

@Service
public class FieldBackgroundExtraServiceImpl implements FieldBackgroundExtraService {

    @Autowired
    private IRepository<MtFieldBackgroundExtra> repository;

    @Override
    public List<MtFieldBackgroundExtra> findAll(User user, String describeApiName) {
        SearchQuery searchQuery = SearchQueryImpl.filters(Lists.newArrayList(
                FilterExt.of(Operator.EQ, TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, DESCRIBE_API_NAME, describeApiName).getFilter()
        ));

        return queryData(user, searchQuery, 2000);
    }

    @Override
    public List<MtFieldBackgroundExtra> find(User user, String describeApiName, List<String> fieldApiNames) {
        SearchQuery searchQuery = SearchQueryImpl.filters(Lists.newArrayList(
                FilterExt.of(Operator.EQ, TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, DESCRIBE_API_NAME, describeApiName).getFilter(),
                FilterExt.of(Operator.IN, FIELD_API_NAME, fieldApiNames).getFilter()
        ));
        return queryData(user, searchQuery, 2000);
    }

    @Override
    public void upsert(User user, MtFieldBackgroundExtra fieldBackgroundExtra) {
        if (Objects.isNull(fieldBackgroundExtra)) {
            return;
        }
        bulkUpsert(user, Lists.newArrayList(fieldBackgroundExtra));
    }

    @Override
    public void bulkUpsert(User user, List<MtFieldBackgroundExtra> fieldBackgroundExtras) {
        if (CollectionUtils.isEmpty(fieldBackgroundExtras)) {
            return;
        }
        String describeApiName = fieldBackgroundExtras.get(0).getDescribeApiName();
        List<String> fieldApiNames = fieldBackgroundExtras.stream()
                .map(MtFieldBackgroundExtra::getFieldApiName)
                .collect(Collectors.toList());
        List<MtFieldBackgroundExtra> fieldBackgroundExtrasInDb = find(user, describeApiName, fieldApiNames);
        fieldBackgroundExtras.forEach(fieldBackgroundExtra -> fieldBackgroundExtrasInDb.stream()
                .filter(x -> StringUtils.equals(x.getFieldApiName(), fieldBackgroundExtra.getFieldApiName()))
                .findFirst()
                .ifPresent(data -> mergeData(fieldBackgroundExtra, data)));
        repository.bulkUpsert(user, fieldBackgroundExtras);
    }

    private static void mergeData(MtFieldBackgroundExtra fieldBackgroundExtra, MtFieldBackgroundExtra fieldBackgroundExtraInDb) {
        if (Objects.nonNull(fieldBackgroundExtraInDb.getId())) {
            fieldBackgroundExtra.setId(fieldBackgroundExtraInDb.getId());
            fieldBackgroundExtra.setCreateTime(fieldBackgroundExtraInDb.getCreateTime());
            fieldBackgroundExtra.setCreateBy(fieldBackgroundExtraInDb.getCreateBy());
            fieldBackgroundExtra.setLastModifiedBy(fieldBackgroundExtraInDb.getLastModifiedBy());
            fieldBackgroundExtra.setLastModifiedTime(System.currentTimeMillis());
        }
        if (Objects.isNull(fieldBackgroundExtra.getRemark())) {
            fieldBackgroundExtra.setRemark(fieldBackgroundExtraInDb.getRemark());
        }
        if (Objects.isNull(fieldBackgroundExtra.getOptionRemark())) {
            fieldBackgroundExtra.setOptionRemark(fieldBackgroundExtraInDb.getOptionRemark());
        } else {
            List<OptionRemark> optionRemarkList = fieldBackgroundExtra.getOptionRemark();
            List<OptionRemark> optionRemarkListInDb = fieldBackgroundExtraInDb.getOptionRemark();
            if (CollectionUtils.isEmpty(optionRemarkListInDb) || CollectionUtils.isEmpty(optionRemarkList)) {
                return;
            }
            Map<String, OptionRemark> optionRemarkMap = optionRemarkList.stream().collect(Collectors.toMap(OptionRemark::getOptionValue, Function.identity(), (x, y) -> x));
            Map<String, OptionRemark> optionRemarkMapInDb = optionRemarkListInDb.stream().collect(Collectors.toMap(OptionRemark::getOptionValue, Function.identity(), (x, y) -> x));
            optionRemarkMapInDb.putAll(optionRemarkMap);
            fieldBackgroundExtra.setOptionRemark(Lists.newArrayList(optionRemarkMapInDb.values()));
        }
    }

    private List<MtFieldBackgroundExtra> queryData(User user, SearchQuery searchQuery, int limit) {
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(CREATE_TIME, false)))
                .limit(limit)
                .offset(0)
                .build();
        return repository.findBy(user, query, MtFieldBackgroundExtra.class);
    }

    @Override
    public void delete(User user, String describeApiName, List<String> fieldApiNames) {
        if (CollectionUtils.isEmpty(fieldApiNames)) {
            return;
        }
        repository.bulkDeleteWithInternalDescribe(user, find(user, describeApiName, fieldApiNames));
    }

    @Override
    public void bulkDelete(User user, String describeApiName) {
        repository.bulkDeleteWithInternalDescribe(user, findAll(user, describeApiName));
    }
}
