package com.facishare.paas.appframework.metadata.count;

import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

/**
 * Created by zhouwr on 2018/4/25
 */
public interface CountFieldCalculator {

    void calculate(IObjectData masterData, List<IObjectData> detailDataList, Count countField, IObjectDescribe detailDescribe);

    void calculateWithDbValue(IObjectData masterData, List<IObjectData> detailDataList, Count countField,
                              IObjectDescribe detailDescribe, CountValues countValues);
}
