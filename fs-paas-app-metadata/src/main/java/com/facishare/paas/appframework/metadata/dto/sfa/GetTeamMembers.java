package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

public interface GetTeamMembers {
    @Data
    class Arg {

        @SerializedName("ObjectType")
        @JSONField(name = "ObjectType")
        private String object_type;

        @SerializedName("DataID")
        @JSONField(name = "DataID")
        private String object_id;
    }

    @Getter
    @Setter
    class Result extends BaseResult {
        private List<TeamMemberPojo> value;
    }

    @Data
    class TeamMemberPojo {
        @SerializedName("TeamMemberID")
        @JSONField(name = "TeamMemberID")
        private String teamMemberID;

        @SerializedName("DataID")
        @JSONField(name = "DataID")
        private String dataID;

        @SerializedName("EmployeeID")
        @JSONField(name = "EmployeeID")
        private Integer employeeID;

        @SerializedName("OwnerID")
        @JSONField(name = "OwnerID")
        private String ownerID;

        @SerializedName("TeamMemberTypeList")
        @JSONField(name = "TeamMemberTypeList")
        private List<Integer> teamMemberTypeList;

        @SerializedName("PermissionTypeEnum")
        @JSONField(name = "PermissionTypeEnum")
        private Integer permissionType;
    }
}
