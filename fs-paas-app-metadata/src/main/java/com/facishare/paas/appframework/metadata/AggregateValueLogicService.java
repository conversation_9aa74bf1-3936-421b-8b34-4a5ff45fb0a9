package com.facishare.paas.appframework.metadata;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

public interface AggregateValueLogicService {

    /**
     * 根据规则id查询聚合值规则
     *
     * @param tenantId 企业id
     * @param ruleId   聚合规则id
     * @return 聚合值规则
     */
    AggregateRule findAggregateRuleById(String tenantId, String ruleId);

    /**
     * 根据规则id查询聚合值规则
     *
     * @param tenantId 企业id
     * @param ruleIds  聚合规则id
     * @return 聚合规则列列表
     */
    List<AggregateRule> findAggregateRuleByIds(String tenantId, List<String> ruleIds);

    /**
     * 按照指定条件汇总聚合值
     *
     * @param tenantId     企业id
     * @param ruleId       聚合规则id
     * @param dimension    聚合维度
     * @param startDate    起始日期
     * @param endDate      结束日期
     * @param aggregateWay 聚合方式(sum-求和,count-计数,avg-平均值,max-最大值,min-最小值)
     * @param scale        小数位数
     * @param roundingMode 小数位进位模式
     * @return 指定条件的聚合值的汇总值
     */
    BigDecimal getAggregateValue(String tenantId, String ruleId, String dimension, long startDate, long endDate,
                                 String aggregateWay, int scale, RoundingMode roundingMode);
}
