package com.facishare.paas.appframework.metadata.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.FileExtUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 文件信息基础类
 * 包含文件的通用属性和方法，用于统一处理文件相关的数据结构
 *
 * <AUTHOR> (refactored from ImageInfo and AttachFieldDataConverter.Attachment)
 */
@Data
public class BaseFileInfo {

    @JSONField(name = "ext")
    private String ext;

    @JSONField(name = "path")
    private String path;

    @JSONField(name = "filename")
    private String fileName;

    @JSONField(name = "signature")
    private String signature;

    /**
     * 获取用于XML导出的文件路径格式
     * 格式：fileName#path#signature 或 path#path#signature
     *
     * @param tenantId 租户ID，用于灰度配置判断
     * @return 格式化的文件路径字符串
     */
    public String getFilePathForXml(String tenantId) {
        String result;
        if (StringUtils.isBlank(fileName)) {
            if (StringUtils.isNotBlank(ext) && !StringUtils.endsWith(path, ext)) {
                result = (path + "." + ext) + "#" + path;
            } else {
                result = path + "#" + path;
            }
        } else {
            // 对fileName中的#号进行转义，避免与分隔符#冲突
            String escapedFileName = FileExtUtil.escapeFileName(fileName);
            
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILE_PATH_EXTENSION_GRAY, tenantId)
                    && StringUtils.isNotBlank(ext) && !StringUtils.endsWith(escapedFileName, ext)) {
                result = (escapedFileName + "." + ext) + "#" + path;
            } else {
                result = escapedFileName + "#" + path;
            }
        }
        // 使用灰度配置判断是否包含签名部分
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILE_BATCH_PACK_NEW_API, tenantId)
                && StringUtils.isNotBlank(signature)) {
            result = result + "#" + signature;
        }
        return result;
    }
}
