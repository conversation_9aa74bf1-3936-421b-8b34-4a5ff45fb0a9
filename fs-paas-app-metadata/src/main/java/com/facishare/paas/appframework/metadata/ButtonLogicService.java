package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/11/21
 */
public interface ButtonLogicService {
    List<IButton> getButtonByComponentActions(User user,
                                              ComponentActions actions,
                                              IObjectDescribe objectDescribe,
                                              IObjectData objectData,
                                              Boolean isDetailPage);

    /**
     * 不做权限校验
     *
     * @param user
     * @param actions
     * @param objectDescribe
     * @param objectData
     * @return
     */
    List<IButton> getButtonByComponentActionsWithoutFilter(User user,
                                                           ComponentActions actions,
                                                           IObjectDescribe objectDescribe,
                                                           IObjectData objectData);

    /**
     * 过滤button
     *
     * @param user
     * @param objectDescribe
     * @param relatedListButtons
     * @return
     */
    Map<RelatedObjectDescribeStructure, List<IButton>> filterButton(User user,
                                                                    IObjectDescribe objectDescribe,
                                                                    Map<RelatedObjectDescribeStructure, List<IButton>> relatedListButtons);

    List<ObjectAction> filterAction(User user, IObjectDescribe describe, IObjectData data, List<ObjectAction> actionList);


    Map<ButtonUsePageType, List<IButton>> findButtonsByUsePages(User user,
                                                                IObjectDescribe describe,
                                                                List<ButtonUsePageType> usePageTypes);

    List<IButton> filterByFunPrivilege(User user, IObjectDescribe describe, List<IButton> buttonList);

    IUdefButton updateButtonAndFunctionPrivilegePrivilege(User user, IUdefButton button, List<String> roles);

    List<IButton> filterPartnerButtons(User user, String describeApiName, List<IButton> buttons);

    List<IButton> filterPartnerButtonsWithDescribe(User user, IObjectDescribe describe, List<IButton> buttons);
}
