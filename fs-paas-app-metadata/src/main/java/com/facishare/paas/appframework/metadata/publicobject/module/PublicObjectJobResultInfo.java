package com.facishare.paas.appframework.metadata.publicobject.module;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/18
 */
@Data
public class PublicObjectJobResultInfo {
    private Integer totalCount;
    private List<String> errorMessages;
    private List<InternationalVerifyMessage> internationalErrorMessages;
    private String displayStatus;

    public static PublicObjectJobResultInfo fromJson(String json) {
        return JacksonUtils.fromJson(json, PublicObjectJobResultInfo.class);
    }

    public String toJson() {
        return JacksonUtils.toJson(this);
    }

    public String errorMessage() {
        if (CollectionUtils.notEmpty(internationalErrorMessages)) {
            return internationalErrorMessages.stream()
                    .map(InternationalVerifyMessage::toString)
                    .collect(Collectors.joining(","));
        }
        return String.join(";", errorMessages);
    }
}
