package com.facishare.paas.appframework.metadata.options;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * create by zhao<PERSON> on 2021/12/13
 */
@Data
@AllArgsConstructor(staticName = "of")
public class OptionReference {
    private List<SimpleReference> optionReferences;
    private Map<String, IObjectDescribe> describeMap;

    public boolean isEmpty() {
        return CollectionUtils.empty(optionReferences);
    }

    public Map<IObjectDescribe, List<IFieldDescribe>> getReferenceMap(boolean onlySelectOne) {
        if (isEmpty()) {
            return Collections.emptyMap();
        }
        Map<IObjectDescribe, List<IFieldDescribe>> result = Maps.newHashMap();

        for (SimpleReference reference : optionReferences) {
            IObjectDescribe describe = describeMap.get(reference.getDescribeApiName());
            if (Objects.isNull(describe)) {
                continue;
            }
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(reference.getFieldApiName());
            if (Objects.isNull(fieldDescribe)) {
                continue;
            }
            if (onlySelectOne && !FieldDescribeExt.of(fieldDescribe).isSelectOne()) {
                continue;
            }
            result.computeIfAbsent(describe, it -> Lists.newArrayList()).add(fieldDescribe);
        }

        return result;
    }

    @Data
    @Builder
    public static class SimpleReference {

        private String describeApiName;
        private String describeDisplayName;
        private String fieldApiName;
        private String fieldName;
        private Boolean active;
        private String fieldType;

        public static SimpleReference from(IObjectDescribe describe, IFieldDescribe fieldDescribe) {
            if (Objects.isNull(describe) || Objects.isNull(fieldDescribe)) {
                return null;
            }
            return SimpleReference.builder()
                    .describeApiName(describe.getApiName())
                    .describeDisplayName(describe.getDisplayName())
                    .fieldApiName(fieldDescribe.getApiName())
                    .fieldName(fieldDescribe.getLabel())
                    .active(fieldDescribe.isActive())
                    .fieldType(fieldDescribe.getType())
                    .build();
        }
    }
}
