package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zhaopx on 2017/11/21.
 */
public interface Lock {
    @Data
    @Builder
    class Arg {
        @SerializedName("ObjectType")
        @JSONField(name = "ObjectType")
        private Integer object_type;

        @SerializedName("ObjectIDs")
        @JSONField(name = "ObjectIDs")
        private List<String> object_ids;

        @SerializedName("IsLocked")
        @JSONField(name = "IsLocked")
        private Boolean is_locked;
    }

    @Getter
    @Setter
    class Result extends BaseResult {

    }
}
