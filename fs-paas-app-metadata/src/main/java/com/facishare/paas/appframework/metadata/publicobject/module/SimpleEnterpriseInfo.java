package com.facishare.paas.appframework.metadata.publicobject.module;

import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import lombok.Builder;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/29
 */
@Data
@Builder
public class SimpleEnterpriseInfo {
    private final String enterpriseId;
    private final String enterpriseAccount;
    private final String enterpriseName;

    public static SimpleEnterpriseInfo from(SimpleEnterpriseData enterpriseData) {
        return SimpleEnterpriseInfo.builder()
                .enterpriseId(String.valueOf(enterpriseData.getEnterpriseId()))
                .enterpriseAccount(enterpriseData.getEnterpriseAccount())
                .enterpriseName(enterpriseData.getEnterpriseName())
                .build();
    }
}
