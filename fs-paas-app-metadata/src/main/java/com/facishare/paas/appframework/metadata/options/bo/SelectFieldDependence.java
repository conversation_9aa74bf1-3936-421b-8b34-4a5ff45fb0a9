package com.facishare.paas.appframework.metadata.options.bo;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.repository.model.MtFieldDependence;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.RecordType;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/7/29
 */
@Slf4j
@Getter
public class SelectFieldDependence {
    private final String describeApiName;
    private final String fieldApiName;
    private final String childFieldName;
    private final List<OptionDependence> dependence;

    public SelectFieldDependence(String describeApiName, String fieldApiName, String childFieldName, List<OptionDependence> dependence) {
        if (StringUtils.isAnyEmpty(describeApiName, fieldApiName, childFieldName)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        this.describeApiName = describeApiName;
        this.fieldApiName = fieldApiName;
        this.childFieldName = childFieldName;
        this.dependence = ImmutableList.copyOf(dependence);
    }

    public static SelectFieldDependence create(@NonNull IFieldDescribe fieldDescribe, @NonNull IFieldDescribe childField) {
        if (!Objects.equals(fieldDescribe.getDescribeApiName(), childField.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        List<OptionDependence> dependence = Lists.newArrayList();
        for (MetadataOptionWrapper selectOption : getSelectOptions(fieldDescribe)) {
            List<String> childOptions = selectOption.getChildOption(((SelectOne) childField));
            dependence.add(new OptionDependence(selectOption.getValue(), childOptions));
        }
        return new SelectFieldDependence(fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName(), childField.getApiName(), dependence);
    }

    private static List<MetadataOptionWrapper> getSelectOptions(IFieldDescribe fieldDescribe) {
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (fieldDescribeExt.isSelectMany() || fieldDescribeExt.isSelectOne()) {
            return ((SelectOne) fieldDescribe).getSelectOptions().stream()
                    .map(MetadataOptionWrapper::from)
                    .collect(Collectors.toList());
        }
        if (fieldDescribeExt.isRecordType()) {
            return ((RecordType) fieldDescribe).getRecordTypeOptions().stream()
                    .map(MetadataOptionWrapper::from)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public static SelectFieldDependence createWithOutDependence(@NonNull IFieldDescribe fieldDescribe, @NonNull IFieldDescribe childField) {
        if (!Objects.equals(fieldDescribe.getDescribeApiName(), childField.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        return new SelectFieldDependence(fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName(), childField.getApiName(), Collections.emptyList());
    }

    public static SelectFieldDependence from(MtFieldDependence fieldDependence) {
        List<OptionDependence> dependence = fieldDependence.getDependence().stream()
                .map(OptionDependence::from)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return new SelectFieldDependence(fieldDependence.getDescribeApiName(), fieldDependence.getFieldApiName(), fieldDependence.getChildFieldName(), dependence);
    }

    public static SelectFieldDependence verifyAndFindOne(IObjectDescribe describe, String fieldApiName, String childFieldName, Supplier<SelectFieldDependence> supplier) {
        SelectFieldDependence selectFieldDependence = new SelectFieldDependence(describe.getApiName(), fieldApiName, childFieldName, Collections.emptyList());
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        FieldDescribeExt fieldDescribe = selectFieldDependence.getParentField(describeExt);
        FieldDescribeExt childField = selectFieldDependence.getChildField(describeExt);

        if (!childField.isParentApiName(fieldApiName)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        if (selectFieldDependence.isGeneralOptions(fieldDescribe, childField)) {
            return supplier.get();
        }

        return SelectFieldDependence.create(fieldDescribe.getFieldDescribe(), childField.getFieldDescribe());
    }

    public static ConvertResultInfo convert2Deleted(IObjectDescribe describe, String fieldApiName, String childFieldName) {
        SelectFieldDependence selectFieldDependence = new SelectFieldDependence(describe.getApiName(), fieldApiName, childFieldName, Collections.emptyList());
        return selectFieldDependence.convert(describe, (fieldDescribe, childField) -> {
        }, childField -> childField.setCascadeParentApiName(null));
    }

    public ConvertResultInfo convert2Create(IObjectDescribe describe) {
        return convert(describe, this::verify2Create, childField -> childField.setCascadeParentApiName(fieldApiName));
    }

    public ConvertResultInfo convert2Update(IObjectDescribe describe) {
        return convert(describe, this::verify2Update, childField -> {
        });
    }

    public ConvertResultInfo convert2Copy(IObjectDescribe describe) {
        return convert(describe, (fieldDescribe, childField) -> {}, childField -> childField.setCascadeParentApiName(fieldApiName));
    }

    public Map<String, Object> fillOption(IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        FieldDescribeExt parentField = getParentField(describeExt);
        FieldDescribeExt childField = getChildField(describeExt);
        convert(parentField, childField);

        Map<String, Object> result = Maps.newHashMap();
        result.put(IFieldDescribe.API_NAME, parentField.getApiName());
        result.put(IFieldDescribe.TYPE, parentField.getType());
        result.put(SelectOneFieldDescribe.OPTIONS, parentField.get(SelectOneFieldDescribe.OPTIONS));
        return result;
    }

    public boolean isChildFields(String parentValue, Collection<String> currentValues) {
        if (CollectionUtils.empty(currentValues) || containsIgnoreCase(currentValues)) {
            return true;
        }
        for (OptionDependence optionDependence : dependence) {
            if (Objects.equals(optionDependence.getValue(), parentValue)) {
                List<String> childOptions = optionDependence.getChildOptions();
                if (CollectionUtils.empty(childOptions)) {
                    return false;
                }
                return childOptions.containsAll(currentValues);
            }
        }
        return false;
    }

    private boolean containsIgnoreCase(Collection<String> currentValues) {
        if (CollectionUtils.empty(currentValues)) {
            return false;
        }
        return currentValues.stream().allMatch("null"::equalsIgnoreCase);
    }

    private ConvertResultInfo convert(IObjectDescribe describe, BiConsumer<FieldDescribeExt, FieldDescribeExt> verify, Consumer<FieldDescribeExt> consumer) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        FieldDescribeExt fieldDescribe = getParentField(describeExt);
        FieldDescribeExt childField = getChildField(describeExt);
        verify.accept(fieldDescribe, childField);

        if (isGeneralOptions(fieldDescribe, childField)) {
            MtFieldDependence fieldDependence = convert();
            consumer.accept(childField);
            return new ConvertResultInfo(fieldDependence, childField.getFieldDescribe());
        }
        convert(fieldDescribe, childField);
        consumer.accept(childField);
        return new ConvertResultInfo(null, fieldDescribe.getFieldDescribe(), childField.getFieldDescribe());
    }

    private FieldDescribeExt getChildField(ObjectDescribeExt describeExt) {
        return describeExt.getFieldDescribeSilently(getChildFieldName())
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isCascadeChildField)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
    }

    private FieldDescribeExt getParentField(ObjectDescribeExt describeExt) {
        return describeExt.getFieldDescribeSilently(getFieldApiName())
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isCascadeParentField)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
    }

    private void verify2Create(FieldDescribeExt fieldDescribe, FieldDescribeExt childField) {
        if (CollectionUtils.notEmpty(childField.getCascadeParentApiNames())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
    }

    private void verify2Update(FieldDescribeExt fieldDescribe, FieldDescribeExt childField) {
        List<String> cascadeParentApiNames = childField.getCascadeParentApiNames();
        if (CollectionUtils.empty(cascadeParentApiNames) || !cascadeParentApiNames.contains(fieldDescribe.getApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
    }

    private MtFieldDependence convert() {
        MtFieldDependence fieldDependence = new MtFieldDependence();
        fieldDependence.setDescribeApiName(describeApiName);
        fieldDependence.setFieldApiName(fieldApiName);
        fieldDependence.setChildFieldName(childFieldName);
        List<MtFieldDependence.FieldDependence> dependence = getDependence().stream()
                .map(OptionDependence::convert)
                .collect(Collectors.toList());
        fieldDependence.setDependence(dependence);
        return fieldDependence;
    }

    private void convert(FieldDescribeExt fieldDescribe, FieldDescribeExt childField) {
        Set<String> childOptionSet = getSelectOptions(childField.getFieldDescribe()).stream()
                .map(MetadataOptionWrapper::getValue)
                .collect(Collectors.toSet());
        Map<String, List<String>> stringListMap = dependence.stream().collect(Collectors.toMap(OptionDependence::getValue, OptionDependence::getChildOptions));
        for (MetadataOptionWrapper selectOption : getSelectOptions(fieldDescribe.getFieldDescribe())) {
            List<String> strings = stringListMap.getOrDefault(selectOption.getValue(), Collections.emptyList());
            List<String> childOptions = strings.stream().filter(childOptionSet::contains).collect(Collectors.toList());
            selectOption.updateChildOption(childFieldName, childOptions);
        }
    }

    private boolean isGeneralOptions(FieldDescribeExt fieldDescribe, FieldDescribeExt childField) {
        return fieldDescribe.isGeneralOptions() || childField.isGeneralOptions();
    }

    public static class ConvertResultInfo {
        private final MtFieldDependence fieldDependence;
        @Getter
        private final List<IFieldDescribe> fieldDescribes;

        private ConvertResultInfo(MtFieldDependence fieldDependence, IFieldDescribe... fieldDescribes) {
            this.fieldDependence = fieldDependence;
            this.fieldDescribes = ImmutableList.copyOf(fieldDescribes);
        }

        public Optional<MtFieldDependence> getFieldDependence() {
            return Optional.ofNullable(fieldDependence);
        }
    }

}
