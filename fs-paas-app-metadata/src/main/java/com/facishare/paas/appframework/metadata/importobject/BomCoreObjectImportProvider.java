package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.util.SfaGrayUtil;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @illustration
 * @slogan:
 * @version:
 */
@Component
public class BomCoreObjectImportProvider extends DefaultObjectImportProvider {


    @Override
    public String getObjectCode() {
        return Utils.BOM_CORE_API_NAME;
    }


    @Override
    protected String getObjectName(IObjectDescribe objectDescribe) {
        return super.getObjectName(objectDescribe);
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe describe, IUniqueRule uniqueRule) {
        if (!SfaGrayUtil.openCPQ(RequestContextManager.getContext().getTenantId())) {
            return Optional.empty();
        }
        return super.getImportObject(describe, uniqueRule);
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_INSERT_IMPORT;
    }

    @Override
    protected boolean getOpenWorkFlow(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected boolean getIsCheckOutOwner(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected boolean getIsRemoveOutTeamMember(IObjectDescribe objectDescribe) {
        return true;
    }
}
