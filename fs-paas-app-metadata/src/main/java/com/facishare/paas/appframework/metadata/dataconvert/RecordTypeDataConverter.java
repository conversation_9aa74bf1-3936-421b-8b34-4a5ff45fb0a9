package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.google.common.base.Strings;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Created by linqiuying on 17/5/18.
 */
public class RecordTypeDataConverter extends AbstractFieldDataConverter {
  @Override
  public String convertFieldData(SessionContext sessionContext) {
    List<IRecordTypeOption> options = ((RecordTypeFieldDescribe)getFieldDescribe()).getRecordTypeOptions();
    String oldData = getObjectData().get(getFieldDescribe().getApiName(),String.class);
    if (CollectionUtils.isEmpty(options) || Strings.isNullOrEmpty(oldData)) {
      return "";
    }

    for (IRecordTypeOption option : options) {
      if (option.getApiName().equals(oldData)) {
        return option.getLabel();
      }
    }
    return oldData;
  }
}
