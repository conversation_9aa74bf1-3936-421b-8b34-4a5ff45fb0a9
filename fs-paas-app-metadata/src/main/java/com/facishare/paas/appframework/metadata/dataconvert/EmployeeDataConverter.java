package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by linqiuying on 17/5/18.
 */
@Component
public class EmployeeDataConverter extends BaseFieldConverter {
    @Autowired
    private OrgService orgService;

    @Override
    protected String convert(IObjectData objectData, IFieldDescribe fieldDescribe, DataConvertContext context) {
        Object o = objectData.get(fieldDescribe.getApiName());
        if (Objects.isNull(o) || !(o instanceof List) || ((List) o).isEmpty()) {
            return null;
        }

        List<String> list = (List) o;
        User user = context.getUser();
        Map<String, String> map = orgService.getUserNameMapByIds(user.getTenantId(), user.getUserId(), list);
        if (IFieldType.EMPLOYEE.equals(fieldDescribe.getType())) {
            String value = String.valueOf(list.get(0));
            return map.get(value);
        } else if (IFieldType.EMPLOYEE_MANY.equals(fieldDescribe.getType())) {
            return list.stream()
                    .map(id -> map.get(String.valueOf(id)))
                    .collect(Collectors.joining("|"));
        }
        return null;

    }


    @Override
    public String convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, DataConvertContext context) {
        return convert(objectData, fieldDescribe, context);
    }

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.EMPLOYEE, IFieldType.EMPLOYEE_MANY);
    }
}
