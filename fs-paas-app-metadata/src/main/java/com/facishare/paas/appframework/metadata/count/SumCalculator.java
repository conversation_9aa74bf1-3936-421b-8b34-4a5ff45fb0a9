package com.facishare.paas.appframework.metadata.count;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by zhouwr on 2018/4/25
 */
public class SumCalculator extends AbstractCountFieldCalculator {

    @Override
    protected BigDecimal doCalculate(BigDecimal result, List<IObjectData> detailDataList, Count countField,
                                     CountValues countValues) {
        for (IObjectData data : detailDataList) {
            BigDecimal value = getFieldValue(data, countField.getCountFieldApiName(), countField.getReturnType(), true);
            if (result == null) {
                result = value;
            } else if (value != null) {
                result = result.add(value);
            }
        }

        return result;
    }

}
