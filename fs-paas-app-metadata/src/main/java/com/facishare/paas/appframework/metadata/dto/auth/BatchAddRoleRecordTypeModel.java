package com.facishare.paas.appframework.metadata.dto.auth;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface BatchAddRoleRecordTypeModel {

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends BaseAuthArg {
        private List<String> entityIds;
        private String roleCode;
        private String recordTypeId;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BaseAuthResult {
    }

}
