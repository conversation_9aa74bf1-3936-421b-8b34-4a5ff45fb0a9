package com.facishare.paas.appframework.metadata.metadatahandle;

import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.privilege.metadatahandle.ObjectDisplayNameHandle;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;

/**
 * create by z<PERSON><PERSON> on 2019/04/03
 */
@Service
public class ObjectDisplayNameHandleImpl implements ObjectDisplayNameHandle {
    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    public String findObjectDisplayName(@Nonnull String tenantId, @Nonnull String apiName) {
        IObjectDescribe object = describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName);
        return object.getDisplayName();
    }
}
