package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.metadata.MetaDataGlobalService;
import com.facishare.paas.appframework.metadata.dto.TownInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.TownFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class TownDataConverter extends BaseFieldConverter {

    @Autowired
    private MetaDataGlobalService metaDataGlobalService;

    @Override
    protected String convert(IObjectData objectData, IFieldDescribe fieldDescribe, DataConvertContext context) {
        String value = objectData.get(fieldDescribe.getApiName(), String.class);
        if (StringUtils.isBlank(value)) {
            return "";
        }
        if (!(fieldDescribe instanceof TownFieldDescribe)) {
            return "";
        }
        TownFieldDescribe townFieldDescribe = (TownFieldDescribe) fieldDescribe;
        String parentApiName = townFieldDescribe.getCascadeParentApiName();
        if (StringUtils.isBlank(parentApiName)) {
            return "";
        }
        String districtId = objectData.get(parentApiName, String.class);
        List<TownInfo> townOptions = metaDataGlobalService.getTownInfoByDistrictIds(Sets.newHashSet(districtId), context.getUser());
        Map<String, String> townMap = townOptions.stream()
                .filter(x -> value.equals(x.getTownId()))
                .collect(Collectors.toMap(TownInfo::getTownId, TownInfo::getTownName));
        return townMap.getOrDefault(value, value);
    }

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.TOWN);
    }
}
