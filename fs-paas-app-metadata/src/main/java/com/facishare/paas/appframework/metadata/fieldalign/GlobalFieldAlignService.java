package com.facishare.paas.appframework.metadata.fieldalign;

import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/5/13
 */
public interface GlobalFieldAlignService {

    GlobalFieldAlign getGlobalFieldAlign(String tenantId);

    void updateGlobalFieldAlign(String tenantId, GlobalFieldAlign fieldAlign);

    GlobalFieldAlign.FieldAlign getFieldAlignByLayout(String tenantId, ILayout layout);

    void handleLayoutWithGlobalFieldAlign(String tenantId, IObjectDescribe describe, ILayout layout, Lang lang);

    boolean isGrayFieldAlign(String tenantId, String describeApiName);
}
