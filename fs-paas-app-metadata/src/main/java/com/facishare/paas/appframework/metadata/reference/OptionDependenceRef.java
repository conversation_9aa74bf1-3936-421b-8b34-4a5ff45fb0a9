package com.facishare.paas.appframework.metadata.reference;

import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ref.RefMessage.ActionType;
import com.facishare.paas.appframework.core.model.ref.RefMessage.CreateArg;
import com.facishare.paas.appframework.core.model.ref.RefMessage.DeleteArg;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Ref;
import com.facishare.paas.appframework.core.util.WhereUsedUtil;
import com.facishare.paas.appframework.core.util.WhereUsedUtil.SOURCE_MATCH_TYPE;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.TableComponentExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class OptionDependenceRef {

    /**
     * 应用方（source）类型，对应配置文件
     */
    public static final String REF_TYPE_VAL = "option_dependence";

    /**
     * 对象名称【{0}】，父字段名称【{1}】，子字段名称【{2}】
     */
    public static final String REF_DISPLAY_TYPE_VAL = "o.p.c";

    /**
     * 用来去重
     * 根据父字段 API
     */
    private final Set<String> distinctParentField = Sets.newHashSet();


    private List<CreateArg> createArgs = Lists.newArrayList();
    private List<DeleteArg> deleteArgs = Lists.newArrayList();

    private OptionDependenceRef() {
    }

    public static Ref buildRefByOptionDependence(ActionType actionType, Tuple<IFieldDescribe, IFieldDescribe> parentChild,
                                                  IObjectDescribe sourceObjDesc, IFieldDescribe targetFieldDesc) {
        OptionDependenceRef ref = new OptionDependenceRef();
        return ref.buildRef(actionType, parentChild, sourceObjDesc, targetFieldDesc);
    }

    private Ref buildRef(ActionType actionType, Tuple<IFieldDescribe, IFieldDescribe> parentChild,
                          IObjectDescribe sourceObjDesc, IFieldDescribe targetFieldDesc) {
        if (Objects.equals(actionType, ActionType.CREATE) || Objects.equals(actionType, ActionType.DELETE_AND_CREATE)) {
            getCreateRefs(parentChild, sourceObjDesc, targetFieldDesc);
        }
        if (Objects.equals(actionType, ActionType.DELETE_AND_CREATE) || Objects.equals(actionType, ActionType.DELETE)) {
            buildDefaultDeleteArg(parentChild, sourceObjDesc, targetFieldDesc);
        }
        return Ref.builder().action(actionType.getCode()).createArgs(createArgs).deleteArgs(deleteArgs).tenantId(sourceObjDesc.getTenantId()).build();
    }

    private void getCreateRefs(Tuple<IFieldDescribe, IFieldDescribe> parentChild,
                               IObjectDescribe sourceObjDesc, IFieldDescribe targetFieldDesc) {
        if (Objects.isNull(parentChild)) {
            return;
        }
        appendCreateArg(parentChild, sourceObjDesc, targetFieldDesc);
    }

    private void appendCreateArg(Tuple<IFieldDescribe, IFieldDescribe> parentChild,
                                 IObjectDescribe sourceObjDesc, IFieldDescribe targetFieldDesc) {
        if (Objects.isNull(createArgs)) {
            createArgs = Lists.newArrayList();
        }

        if (ObjectUtils.anyNull(parentChild, sourceObjDesc, targetFieldDesc)) {
            return;
        }

        // 大前提： targetFieldDesc 必须是自定义字段 因为只有自定义字段才能查被用在何处
        if (!FieldDescribeExt.of(targetFieldDesc).isCustomField()) {
            return;
        }

        // 去重 ：！！！根据 parentField
        if (distinctParentField.contains(parentChild.getKey().getApiName())) {
            // 存在直接跳过
            return;
        } else {
            // 不存在则加入
            distinctParentField.add(targetFieldDesc.getApiName());
        }


        CreateArg createArg = CreateArg.builder()
                .refType(REF_TYPE_VAL)
                .sourceDisplayType(REF_DISPLAY_TYPE_VAL)
                .sourceValue(WhereUsedUtil.buildSourceValue(sourceObjDesc.getApiName(), parentChild.getKey().getApiName(), parentChild.getValue().getApiName()))
                .sourceLabel(WhereUsedUtil.buildSourceLabel(sourceObjDesc.getDisplayName(), parentChild.getKey().getLabel(), parentChild.getValue().getLabel()))
                .targetType(EntityReferenceService.DESCRIBE_FIELD_TYPE)
                .targetValue(WhereUsedUtil.buildTargetValue(sourceObjDesc.getApiName(), targetFieldDesc.getApiName()))
                .targetLabel(WhereUsedUtil.buildTargetLabel(sourceObjDesc.getDisplayName(), targetFieldDesc.getLabel()))
                .build();
        createArgs.add(createArg);
    }


    private void buildDefaultDeleteArg(Tuple<IFieldDescribe, IFieldDescribe> parentChild,
                                       IObjectDescribe sourceObjDesc, IFieldDescribe targetFieldDesc) {
        if (Objects.isNull(deleteArgs)) {
            deleteArgs = Lists.newArrayList();
        }

        if (ObjectUtils.anyNull(parentChild)) {
            return;
        }
        DeleteArg deleteArg = DeleteArg.builder()
                .refType(REF_TYPE_VAL)
                .archivedSourceDisplayTypes(RefFieldService.ARCHIVED_TYPE_MAP.get(REF_TYPE_VAL))
                .sourceMatchType(SOURCE_MATCH_TYPE.EQ.name())
                .sourceValue(WhereUsedUtil.buildSourceValue(parentChild.getKey().getDescribeApiName(), parentChild.getKey().getApiName(), parentChild.getValue().getApiName()))
                .build();
        deleteArgs.add(deleteArg);

    }


}
