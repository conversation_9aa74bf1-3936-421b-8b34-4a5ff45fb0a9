package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

/**
 * Created by z<PERSON>o<PERSON> on 2024/3/5
 */
@Component
public class MobileFlowBaseLayoutFactory extends BaseLayoutFactory {
    protected MobileFlowBaseLayoutFactory(IComponentFactoryManager componentFactoryManager) {
        super(componentFactoryManager);
    }

    @Override
    public String supportLayoutType() {
        return LayoutTypes.WHAT_LIST;
    }

    @Override
    public ILayout generateDefaultLayout(Context context, IObjectDescribe describe) {
        IObjectDescribe whatDescribe = context.getWhatDescribe();
        ILayout layout = new Layout();
        layout.setId(IdGenerator.get());
        layout.setWhatApiName(whatDescribe.getApiName());
        layout.setPackage(DefObjConstants.PACKAGE_NAME_CRM);
        layout.setAgentType(ILayout.AGENT_TYPE_MOBILE);
        layout.setName(String.format("layout_%s_%s_mobile", whatDescribe.getApiName(), describe.getApiName()));
        layout.setRefObjectApiName(describe.getApiName());
        layout.setLayoutType(ILayout.WHAT_LAYOUT_TYPE);
        layout.setIsDefault(true);
        layout.setDisplayName(I18N.text(I18NKey.DEFAULT_FLOW_WHAT_LAYOUT));
        layout.setVersion(1);
        layout.setIsShowFieldname(true);

        layout.setCreateTime(whatDescribe.getCreateTime());
        layout.setCreatedBy(whatDescribe.getCreatedBy());
        layout.setLastModifiedTime(whatDescribe.getCreateTime());
        layout.setLastModifiedBy(whatDescribe.getCreatedBy());

        IComponentFactory componentFactory = getComponentFactory(IComponent.TYPE_WHAT);
        IComponent component = componentFactory.createDefaultComponent(context, describe);
        layout.setComponents(Lists.newArrayList(component));
        addDefaultI18n(layout, describe.getTenantId(), I18NKey.DEFAULT_FLOW_WHAT_LAYOUT);

        return layout;
    }
}
