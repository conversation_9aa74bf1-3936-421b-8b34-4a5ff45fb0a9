package com.facishare.paas.appframework.metadata.dimension;

import com.facishare.paas.appframework.common.service.dto.DimensionInfo;
import com.facishare.paas.appframework.core.model.User;

import java.util.List;

public interface DimensionLogicService {

    /**
     * 根据id换取name信息
     *
     * @param ids
     * @param user
     * @return
     */
    List<DimensionInfo> getDimensionInfoByIds(List<String> ids, User user);

    /**
     * 根据name换取id信息
     *
     * @param names
     * @param user
     * @return
     */
    List<DimensionInfo> getDimensionInfoByNames(List<String> names, User user);

    List<DimensionInfo> getDimensionInfoByCodes(List<String> codes, User user);

}
