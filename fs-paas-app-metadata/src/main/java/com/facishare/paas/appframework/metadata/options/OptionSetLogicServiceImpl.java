package com.facishare.paas.appframework.metadata.options;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.service.ReferenceServiceProxy;
import com.facishare.paas.appframework.common.service.dto.DeleteAndCreateReference;
import com.facishare.paas.appframework.common.service.dto.DeleteReference;
import com.facishare.paas.appframework.common.service.dto.FindReferenceByTarget;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.i18n.MetadataI18NKey;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.repository.model.MtOptionSet.DEFINE_TYPE;

/**
 * create by zhaoju on 2021/12/13
 */
@Slf4j
@Service
public class OptionSetLogicServiceImpl implements OptionSetLogicService {

    public static final int MAX_COUNT = 20;
    public static final int MAX_OPTIONS_SIZE = 3500;
    public static final int REFERENCE_LIMIT = 200;
    public static final String METADATA_OPTIONS = "metadata_options";
    public static final String OPTION_SET_EVENT_TOPIC = "fs-paas-option-set-event";

    @Autowired
    private IRepository<MtOptionSet> repository;
    @Autowired
    private ReferenceServiceProxy referenceServiceProxy;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private AppDefaultRocketMQProducer optionChangeProducer;

    @Override
    public MtOptionSet create(User user, MtOptionSet options) {
        if (Objects.isNull(options)) {
            return null;
        }
        MtOptionSet.DefineType.isValidDefineType(options.getDefineType());
        if (!options.isPreset()) {
            checkCount(user);
        }
        validateOptionSets(options, null);
        checkLabel(user, options);

        options.setTenantId(user.getTenantId());
        options.setCreateBy(user.getUserId());
        options.setCreateTime(System.currentTimeMillis());
        options.setActive(true);
        options.setObjectDescribeApiName(MtOptionSet.MT_OPTION_OBJ_API_NAME);

        syncI18n(user, options);
        List<MtOptionSet> result = repository.bulkCreate(user, Lists.newArrayList(options));
        return result.get(0);
    }

    private void checkLabel(User user, MtOptionSet option) {
        if (Objects.isNull(option)) {
            return;
        }
        List<MtOptionSet> optionSets = findAll(user);
        String optionLabel = option.getLabel();
        String optionApiName = option.getApiName();
        boolean labelIsExit = optionSets.stream().anyMatch(it -> Objects.equals(it.getLabel(), optionLabel) && !Objects.equals(it.getApiName(), optionApiName));
        if (labelIsExit) {
            throw new ValidateException(I18N.text(I18NKey.OPTION_SET_NAME_DUPLICATE, optionLabel));
        }
    }

    private void validateOptionSize(MtOptionSet options) {
        if (CollectionUtils.nullToEmpty(options.getOptions()).size() > MAX_OPTIONS_SIZE) {
            throw new ValidateException(I18N.text(I18NKey.OPTION_SET_MAX_OPTIONS, MAX_OPTIONS_SIZE));
        }
    }

    private void validateOptionSets(MtOptionSet options, MtOptionSet oldOption) {
        validateOptionSize(options);
        // 记录之前的选项name
        Set<String> oldOptionNames = Sets.newHashSet();
        if (Objects.nonNull(oldOption)) {
            for (MtOptionSet.MtOption mtOption : CollectionUtils.nullToEmpty(oldOption.getOptions())) {
                oldOptionNames.add(mtOption.getValue());
            }
        }
        checkIfVersionConflict(options, oldOption);
        Set<String> optionNames = Sets.newHashSet();
        for (MtOptionSet.MtOption mtOption : CollectionUtils.nullToEmpty(options.getOptions())) {
            String value = mtOption.getValue();
            // 选项值不允许重复
            if (!optionNames.add(value)) {
                log.warn("option value duplicate, ei:{}, apiName:{}, duplicate value:{}", options.getTenantId(), options.getApiName(), value);
                throw new ValidateException(I18NExt.text(I18NKey.OPTION_VALUE_DUPLICATE));
            }
            // 新建的选项不能以other开头,已经存在的选项不校验
            if (StringUtils.startsWith(value, "other") && !Objects.equals(value, "other") && !oldOptionNames.contains(value)) {
                throw new ValidateException(I18NExt.text(MetadataI18NKey.OPTION_CANNOT_START_WITH_OTHER));
            }
        }
    }

    private void checkIfVersionConflict(MtOptionSet options, MtOptionSet oldOption) {
        if (Objects.isNull(options) || Objects.isNull(oldOption)) {
            return;
        }
        Integer version = options.getVersion();
        Integer oldOptionVersion = oldOption.getVersion();
        if (Objects.isNull(version) || Objects.isNull(oldOptionVersion)) {
            return;
        }
        if (version < oldOptionVersion) {
            log.warn("checkIfVersionConflict, apiName:{}, oldVersion:{}, newVersion:{}", options.getApiName(), oldOptionVersion, version);
            throw new ValidateException(I18NExt.text(I18NKey.OPTION_SET_VERSION_CONFLICT));
        }
    }

    @Override
    @Transactional
    public MtOptionSet update(User user, MtOptionSet options, boolean onlyUpdateOptions) {
        if (Objects.isNull(options) || Strings.isNullOrEmpty(options.getApiName())) {
            return null;
        }
        MtOptionSet.DefineType.isValidDefineType(options.getDefineType());
        MtOptionSet oldOption = find(user, options.getApiName()).orElse(null);
        if (Objects.isNull(oldOption)) {
            return null;
        }
        if (!oldOption.custom()) {
            throw new ValidateException(I18NExt.text(I18NKey.SYSTEM_OPTION_SET_CANNOT_MODIFY));
        }
        validateOptionSets(options, oldOption);
        if (!onlyUpdateOptions) {
            checkLabel(user, options);
        }
        final boolean change = !oldOption.compareOptions(options);
        // 合并数据
        MtOptionSet merge = oldOption.merge(options, onlyUpdateOptions);
        // 更新
        final MtOptionSet result = updateOption(user, merge);
        // 尝试触发计算
        sendOptionChangeMessage(user, options.getApiName(), change);
        return result;
    }

    private void sendOptionChangeMessage(User user, String apiName, boolean changed) {
        try {
            OptionSetChangeMessage message = OptionSetChangeMessage.of(user.getTenantId(), apiName, changed);
            byte[] bytes = JSON.toJSONString(message).getBytes(Charsets.UTF_8);
            Message msg = new Message(OPTION_SET_EVENT_TOPIC, "", bytes);
            optionChangeProducer.sendMessage(msg);
        } catch (Exception e) {
            log.warn("send message fail, ei:{}, apiName:{}, changed:{}", user.getTenantId(), apiName, changed, e);
        }
    }

    private MtOptionSet updateOption(User user, MtOptionSet options) {
        options.setLastModifiedBy(user.getUserId());
        options.setLastModifiedTime(System.currentTimeMillis());
        options.setObjectDescribeApiName(MtOptionSet.MT_OPTION_OBJ_API_NAME);
        MtOptionSet result = repository.update(user, options);
        syncI18n(user, options);
        return result;
    }

    private void syncI18n(User user, MtOptionSet option) {
        if (!i18nEnable(user.getTenantId())) {
            return;
        }
        I18nClient i18nClient = I18nClient.getInstance();
        Set<Localization> localizations = Sets.newHashSet();
        final String lang = I18N.getContext().getLanguage();
        for (MtOptionSet.MtOption mtOption : option.getOptions()) {
            final String label = mtOption.getLabel();
            final String key = GetI18nKeyUtil.getCurrentOptionSetLabelKey(option.getApiName(), mtOption.getValue());
            final Localization localization = Localization.builder()
                    .key(key)
                    .tags(Lists.newArrayList(METADATA_OPTIONS))
                    .tenantId(user.getTenantIdInt())
                    .build();
            i18nClient.build(localization, lang, label);
            localizations.add(localization);
        }
        i18nClient.save(Long.parseLong(user.getTenantId()), localizations, false);
    }

    @Override
    public List<MtOptionSet> findAll(User user) {
        SearchQuery searchQuery = SearchQueryImpl.filter(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());

        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(1000)
                .offset(0)
                .build();

        return findAndReplaceOptionLabel(user, query);
    }

    @Override
    public Map<String, MtOptionSet> findByApiNames(User user, Collection<String> optionApiNames) {
        SearchQuery searchQuery = SearchQueryImpl.filters(Lists.newArrayList(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.IN, MtOptionSet.API_NAME, Lists.newArrayList(optionApiNames)).getFilter()
        ));

        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(1000)
                .offset(0)
                .build();
        List<MtOptionSet> optionSets = findAndReplaceOptionLabel(user, query);
        return optionSets.stream()
                .collect(Collectors.toMap(MtOptionSet::getApiName, Function.identity()));
    }

    private List<MtOptionSet> findAndReplaceOptionLabel(User user, Query query) {
        List<MtOptionSet> result = repository.findBy(user, query, MtOptionSet.class);
        if (!i18nEnable(user.getTenantId())) {
            return result;
        }
        String lang = RequestUtil.getLanguageTag();
        for (MtOptionSet mtOptionSet : result) {
            for (MtOptionSet.MtOption option : mtOptionSet.getOptions()) {
                String label = I18NExt.getOnlyText(GetI18nKeyUtil.getCurrentOptionSetLabelKey(mtOptionSet.getApiName(), option.getValue()));
                if (!Strings.isNullOrEmpty(label)) {
                    option.setLabel(label);
                }
            }
            mtOptionSet.synI18nFromDataMultiLang(lang);
        }
        return result;
    }

    private boolean i18nEnable(String tenantId) {
        Map<String, Boolean> existModule = licenseService.existModule(tenantId, Sets.newHashSet(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP));
        return BooleanUtils.isTrue(existModule.get(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP));
    }

    @Override
    public Optional<MtOptionSet> find(User user, String optionApiName) {
        return findOne(user, searchQuery -> searchQuery.and(FilterExt.of(Operator.EQ, MtOptionSet.API_NAME, optionApiName).getFilter()));
    }

    private Optional<MtOptionSet> findOne(User user, UnaryOperator<SearchQuery> unaryOperator) {
        SearchQuery searchQuery = SearchQueryImpl.filter(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        searchQuery = unaryOperator.apply(searchQuery)
                .and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(1)
                .offset(0)
                .build();
        List<MtOptionSet> result = findAndReplaceOptionLabel(user, query);
        return result.stream().findFirst();
    }

    @Override
    public MtOptionSet enable(User user, String optionApiName) {
        return enableOrDisable(user, optionApiName, true);
    }

    @Override
    public MtOptionSet disable(User user, String optionApiName) {
        return enableOrDisable(user, optionApiName, false);
    }

    private MtOptionSet enableOrDisable(User user, String optionApiName, boolean enable) {
        return findOne(user, searchQuery -> {
            List<IFilter> filters = Lists.newArrayList(
                    FilterExt.of(Operator.EQ, MtOptionSet.API_NAME, optionApiName).getFilter(),
                    FilterExt.of(Operator.N, IFieldDescribe.IS_ACTIVE, String.valueOf(enable)).getFilter());
            return searchQuery.and(filters);
        }).map(option -> {
            option.setActive(enable);
            return repository.update(user, option);
        }).orElse(null);
    }

    @Override
    public void deleted(User user, String optionApiName) {
        final MtOptionSet option = find(user, optionApiName)
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.OPTION_SET_NOT_EXIST_OR_DELETED)));
        if (!option.custom()) {
            throw new ValidateException(I18NExt.text(I18NKey.SYSTEM_OPTION_SET_CANNOT_MODIFY));
        }
        List<OptionReference.SimpleReference> reference = findReference(user, optionApiName).getOptionReferences();
        if (CollectionUtils.notEmpty(reference)) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.OPTION_SET_REFERENCE_RELATION,
                    I18NKey.OPTION_SET_REFERENCE_RELATION, option.getLabel()));
        }
        repository.bulkInvalidAndDelete(user, Lists.newArrayList(option));
    }

    @Override
    public void checkCount(User user) {
        IFilter tenant = FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter();
        IFilter typeNoPackage = FilterExt.of(Operator.N, DEFINE_TYPE, MtOptionSet.DefineType.PACKAGE.getValue()).getFilter();
        SearchQuery searchQuery = SearchQueryImpl.filter(tenant).and(typeNoPackage);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .build();
        final Integer count = repository.findCountOnly(user, query, MtOptionSet.class);
        int maxCount = AppFrameworkConfig.getOptionSetMaxCountGray(user.getTenantId(), MAX_COUNT);
        if (count >= maxCount) {
            throw new ValidateException(I18N.text(I18NKey.OPTION_SET_MAX_LIMIT, maxCount));
        }
    }

    @Override
    public OptionReference findReference(User user, String optionApiName) {
        Map<String, String> headerMap = ImmutableMap.of("tenantId", user.getTenantId());
        FindReferenceByTarget.Result result = referenceServiceProxy.findByTarget(headerMap, OPTION_REFERENCE_TARGET_TYPE, optionApiName,
                String.valueOf(false), String.valueOf(REFERENCE_LIMIT));

        List<Tuple<String, String>> values = result.getValues().stream()
                .filter(it -> OPTION_REFERENCE_SOURCE_TYPE.equals(it.getSourceType()))
                .map(ReferenceData::getSourceValue)
                .map(it -> Tuple.of(StringUtils.substringBefore(it, "."), StringUtils.substringAfter(it, ".")))
                .collect(Collectors.toList());

        List<String> describeApiNames = values.stream()
                .map(Tuple::getKey)
                .distinct()
                .collect(Collectors.toList());

        Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), describeApiNames);

        List<OptionReference.SimpleReference> simpleReferences = values.stream()
                .map(it -> {
                    IObjectDescribe describe = objectDescribeMap.get(it.getKey());
                    if (Objects.isNull(describe)) {
                        return null;
                    }
                    return OptionReference.SimpleReference.from(describe, describe.getFieldDescribe(it.getValue()));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return OptionReference.of(simpleReferences, objectDescribeMap);
    }

    @Override
    public void saveReference(User user, String describeApiName, String fieldApiName, String optionApiName) {
        Map<String, String> headerMap = ImmutableMap.of("tenantId", user.getTenantId());
        String sourceValue = getOptionReferenceSourceValue(describeApiName, fieldApiName);
        ReferenceData referenceData = ReferenceData.builder()
                .sourceType(OPTION_REFERENCE_SOURCE_TYPE)
                .sourceValue(sourceValue)
                .sourceLabel(sourceValue)
                .targetType(OPTION_REFERENCE_TARGET_TYPE)
                .targetValue(optionApiName)
                .build();
        DeleteAndCreateReference.Arg arg = DeleteAndCreateReference.Arg.builder()
                .items(Lists.newArrayList(referenceData))
                .build();
        DeleteAndCreateReference.Result result = referenceServiceProxy.deleteAndCreate(headerMap, arg);
        if (Objects.equals(0, result.getCode())) {
            log.warn("saveReference fail, result:{}, ei:{}, describeApiName:{}, fieldApiName:{}, optionApiName:{}",
                    result, user.getTenantId(), describeApiName, fieldApiName, optionApiName);
        }
    }

    @Override
    public void deleteReference(User user, String describeApiName, String fieldApiName, String optionApiName) {
        if (StringUtils.isAnyEmpty(describeApiName, fieldApiName, optionApiName)) {
            return;
        }
        DeleteReference.Result result = referenceServiceProxy.deleteReference(user.getTenantId(), OPTION_REFERENCE_SOURCE_TYPE,
                getOptionReferenceSourceValue(describeApiName, fieldApiName), optionApiName, OPTION_REFERENCE_TARGET_TYPE);
        if (Objects.equals(0, result.getCode())) {
            log.warn("deleteReference fail, result:{}, ei:{}, describeApiName:{}, fieldApiName:{}, optionApiName:{}",
                    result, user.getTenantId(), describeApiName, fieldApiName, optionApiName);
        }
    }

    @Override
    public void deleteReferenceByField(User user, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe) {
        String optionApiName = SelectOneExt.of(((SelectOne) fieldDescribe)).getOptionApiName();
        deleteReference(user, objectDescribe.getApiName(), fieldDescribe.getApiName(), optionApiName);
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CREATE_OPTION_REFERENCE_BY_CHILD_DESCRIBE_GRAY, user.getTenantId())) {
            return;
        }
        List<String> childApiNames = objectDescribe.getChildApiNames();
        if (CollectionUtils.empty(childApiNames) || !FieldDescribeExt.of(fieldDescribe).isPrimaryInheritField()) {
            return;
        }
        for (String childApiName : childApiNames) {
            deleteReference(user, childApiName, fieldDescribe.getApiName(), optionApiName);
        }
    }

    private String getOptionReferenceSourceValue(String describeApiName, String fieldApiName) {
        return String.format("%s.%s", describeApiName, fieldApiName);
    }

    @Override
    public void validateAndUpdateOptionReference(User user, IObjectDescribe describe, List<IFieldDescribe> fieldDescribeList) {
        fieldDescribeList.forEach(fieldDescribe -> createOptionReferenceByDescribe(user, describe, fieldDescribe));
        createOptionReferenceByChildDescribe(user, describe, fieldDescribeList);
    }

    @Override
    public void validateAndUpdateOptionReference(User user, IObjectDescribe oldDescribe, IObjectDescribe newDescribe) {
        List<IFieldDescribe> fields = ObjectDescribeExt.of(newDescribe).stream()
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isGeneralOptions)
                .map(FieldDescribeExt::<IFieldDescribe>getFieldDescribe)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(fields)) {
            return;
        }
        // 新建描述的逻辑
        if (Objects.isNull(oldDescribe)) {
            fields.forEach(field -> createOptionReferenceByField(user, newDescribe.getApiName(), field));
            createOptionReferenceByChildDescribe(user, newDescribe, fields);
            return;
        }
        // 更新描述的逻辑
        fields.forEach(fieldDescribe -> createOptionReferenceByDescribe(user, oldDescribe, fieldDescribe));
        createOptionReferenceByChildDescribe(user, newDescribe, fields);
    }

    /**
     * 对于客户、客户主数据这种管控的主子对象
     * 在更新主对象字段后，需要更新字段的依赖关系
     *
     * @param user
     * @param objectDescribe
     * @param fields
     */
    private void createOptionReferenceByChildDescribe(User user, IObjectDescribe objectDescribe, List<IFieldDescribe> fields) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CREATE_OPTION_REFERENCE_BY_CHILD_DESCRIBE_GRAY, user.getTenantId())) {
            return;
        }
        List<String> childApiNames = objectDescribe.getChildApiNames();
        if (CollectionUtils.empty(childApiNames)) {
            return;
        }
        List<IFieldDescribe> primaryInheritFields = fields.stream()
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isPrimaryInheritField)
                .map(FieldDescribeExt::<IFieldDescribe>getFieldDescribe)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(primaryInheritFields)) {
            return;
        }

        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId(), childApiNames);
        for (String childApiName : describeMap.keySet()) {
            primaryInheritFields.forEach(field -> createOptionReferenceByField(user, childApiName, field));
        }
    }

    private void createOptionReferenceByDescribe(User user, IObjectDescribe describeDraft, IFieldDescribe fieldDescribe) {

        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (!fieldDescribeExt.isSelectOne() && !fieldDescribeExt.isSelectMany()) {
            return;
        }

        IFieldDescribe oldField = describeDraft.getFieldDescribe(fieldDescribe.getApiName());
        // 更新字段字段
        if (Objects.nonNull(oldField)) {
            FieldDescribeExt oldFieldExt = FieldDescribeExt.of(oldField);
            if (oldFieldExt.isGeneralOptions()) {
                if (!fieldDescribeExt.isGeneralOptions()) {
                    throw new ValidateException(I18N.text(I18NKey.FIELD_CANNOT_REPLACE_OPTION_SET, fieldDescribeExt.getLabel()));
                }
                if (!Objects.equals(SelectOneExt.of(((SelectOne) oldField)).getOptionApiName(),
                        SelectOneExt.of(((SelectOne) fieldDescribe)).getOptionApiName())) {
                    throw new ValidateException(I18N.text(I18NKey.FIELD_CANNOT_REPLACE_OPTION_SET, fieldDescribeExt.getLabel()));
                }
                return;
            }
        }

        // 新增选项集依赖
        createOptionReferenceByField(user, describeDraft.getApiName(), fieldDescribe);
    }

    private void createOptionReferenceByField(User user, String describeApiName, IFieldDescribe fieldDescribe) {
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        // 新增选项集依赖
        if (!fieldDescribeExt.isGeneralOptions()) {
            return;
        }
        String optionApiName = SelectOneExt.of(((SelectOne) fieldDescribe)).getOptionApiName();
        if (Strings.isNullOrEmpty(optionApiName)) {
            return;
        }
        saveReference(user, describeApiName, fieldDescribe.getApiName(), optionApiName);
    }

    @Override
    public void triggerCalculate(User user, String optionApiName, boolean onlyTouch) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OPTION_SET_GRAY_EI, user.getTenantId())) {
            return;
        }
        OptionReference optionReference = findReference(user, optionApiName);
        Map<IObjectDescribe, List<IFieldDescribe>> referenceMap = optionReference.getReferenceMap(false);
        if (CollectionUtils.empty(referenceMap)) {
            return;
        }
        // 更新描述，升级一下版本号，保证后续计算服务拿到最新的描述信息
        referenceMap.keySet().forEach(describe -> describeLogicService.touchDescribe(describe));
        if (onlyTouch) {
            return;
        }

        optionReference.getReferenceMap(true)
                .forEach(((describe, fieldDescribes) -> fieldRelationCalculateService.checkSelectOneChangeOfFields(describe, describe, fieldDescribes, false)));
    }

    public void asyncTriggerCalculate(User user, Set<String> optionApiNames, boolean onlyTouch) {
        if (CollectionUtils.empty(optionApiNames)) {
            return;
        }
        optionApiNames.forEach(optionApiName -> sendOptionChangeMessage(user, optionApiName, !onlyTouch));
    }

    @Override
    public IFieldDescribe fillSelectOption(User user, IFieldDescribe fieldDescribe) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OPTION_SET_GRAY_EI, user.getTenantId())) {
            return fieldDescribe;
        }
        if (!FieldDescribeExt.of(fieldDescribe).isGeneralOptions()) {
            return fieldDescribe;
        }
        SelectOne selectOne = (SelectOne) fieldDescribe;
        String optionApiName = SelectOneExt.of(selectOne).getOptionApiName();
        find(user, optionApiName).ifPresent(it -> {
            List<ISelectOption> selectOption = it.toSelectOption();
            selectOne.setSelectOptions(selectOption);
        });
        return selectOne;
    }
}
