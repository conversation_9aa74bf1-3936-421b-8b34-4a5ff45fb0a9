package com.facishare.paas.appframework.metadata.dto.tools;

import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.appframework.metadata.repository.model.HandlerRuntimeConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface ValidateHandler {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String objectApiName;
        private String interfaceCode;
        private String tenantId;
        private List<HandlerDescribe> handlerDescribes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<HandlerDefinition> definitions;
        private List<HandlerRuntimeConfig> runtimeConfigs;
    }
}
