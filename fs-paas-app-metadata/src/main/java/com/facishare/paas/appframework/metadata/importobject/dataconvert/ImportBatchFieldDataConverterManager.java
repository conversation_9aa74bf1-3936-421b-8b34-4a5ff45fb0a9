package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2019/07/27
 */
@Slf4j
@Component
public class ImportBatchFieldDataConverterManager implements ApplicationContextAware {
    private Map<String, ImportBatchFieldDataConverter> batchDataConverterMap = Maps.newHashMap();
    @Autowired
    private ImportBatchFieldDataConverterAdapter importBatchFieldDataConverterAdapter;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            Map<String, ImportBatchFieldDataConverter> batchSpringMap = applicationContext.getBeansOfType(ImportBatchFieldDataConverter.class);
            if (CollectionUtils.notEmpty(batchSpringMap)) {
                batchSpringMap.forEach((x, y) -> y.getSupportedFieldTypes().forEach(t -> batchDataConverterMap.put(t, y)));
            }
        } catch (BeansException e) {
            log.error("init BatchFieldDataConverter error", e);
        }
    }

    public ImportBatchFieldDataConverter getBatchFieldDataConverter(String fieldType) {
        return batchDataConverterMap.getOrDefault(fieldType, importBatchFieldDataConverterAdapter);
    }
}
