package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.SearchTemplateCode;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by luxin on 2018/5/23.
 */
@Component
@Slf4j
public class GoalValueInitSceneProvider extends DefaultSceneProvider {
    @Override
    public String getApiName() {
        return Utils.GOAL_VALUE_API_NAME;
    }

    @Override
    public List<ISearchTemplate> getDefaultSearchTemplateList(User user, String apiName,String extendAttribute) {
        try {
            return searchTemplateService.findByObjectDescribeAPINameAndCode(user.getTenantId(), apiName, Sets.newHashSet(SearchTemplateCode.ALL));
        } catch (MetadataServiceException e) {
            log.error("Error in init DefaultSearchTemplate, ei:{}, apiName:{}", user.getTenantId(), apiName);
        }
        return Lists.newArrayList();
    }
}
