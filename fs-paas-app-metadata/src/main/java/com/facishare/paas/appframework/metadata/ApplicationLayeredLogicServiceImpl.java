package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.config.ApplicationLayeredGrayService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.ObjectDescribeFinder;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2024/5/17
 */
@Service
public class ApplicationLayeredLogicServiceImpl implements ApplicationLayeredLogicService {
    private final ApplicationLayeredGrayService applicationLayeredGrayService;
    private final DescribeLogicService describeLogicService;
    private final LayoutLogicService layoutLogicService;

    public ApplicationLayeredLogicServiceImpl(ApplicationLayeredGrayService applicationLayeredGrayService,
                                              DescribeLogicService describeLogicService,
                                              LayoutLogicService layoutLogicService) {
        this.applicationLayeredGrayService = applicationLayeredGrayService;
        this.describeLogicService = describeLogicService;
        this.layoutLogicService = layoutLogicService;
    }

    @Override
    @Transactional
    public void enableApplicationLayer(User user, String appId, String describeApiName, boolean needCopyLayout) {
        boolean canEnable = applicationLayeredGrayService.canOpenApplicationLayer(user, appId, describeApiName);
        if (!canEnable) {
            return;
        }
        applicationLayeredGrayService.openApplicationLayer(user, appId, describeApiName);
        if (needCopyLayout) {
            IObjectDescribe objectDescribe = describeLogicService.findObject(user.getTenantId(), describeApiName);
            layoutLogicService.enableApplicationLayerLayoutByAppId(user, appId, objectDescribe);
        }
    }

    @Override
    public void closeApplicationLayer(User user, String appId, String describeApiName, boolean needDeletedLayout) {
        boolean supportAppLayered = applicationLayeredGrayService.supportAppLayered(user, appId, describeApiName);
        if (!supportAppLayered) {
            return;
        }
        if (needDeletedLayout) {
            IObjectDescribe objectDescribe = describeLogicService.findObject(user.getTenantId(), describeApiName);
            layoutLogicService.disableApplicationLayerLayoutByAppId(user, appId, objectDescribe);
        }
        applicationLayeredGrayService.closeApplicationLayer(user, appId, describeApiName);
    }

    @Override
    public List<ILayout> findLayoutByAppId(User user, String appId) {
        List<ILayout> result = Lists.newArrayList();
        if (!applicationLayeredGrayService.supportAppLayered(user, appId)) {
            return result;
        }
        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder.builder()
                .user(user)
                .includeChangeOrderObject(false)
                .build();
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(objectDescribeFinder);

        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, appId);
        ArrayList<String> layoutTypes = Lists.newArrayList(LayoutTypes.EDIT, LayoutTypes.DETAIL);
        for (IObjectDescribe objectDescribe : describeList) {
            String describeApiName = objectDescribe.getApiName();
            boolean supportAppLayered = applicationLayeredGrayService.supportAppLayered(user, appId, describeApiName);
            if (!supportAppLayered) {
                continue;
            }
            List<ILayout> layouts = layoutLogicService.findByTypes(layoutContext, describeApiName, layoutTypes);
            result.addAll(layouts);
        }
        result.sort(Comparator.comparing(ILayout::getCreateTime));
        return result;
    }
}
