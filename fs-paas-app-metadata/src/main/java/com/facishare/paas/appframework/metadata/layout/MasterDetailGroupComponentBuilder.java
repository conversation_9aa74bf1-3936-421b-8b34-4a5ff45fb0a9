package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.appframework.metadata.layout.component.IComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.Lists;
import lombok.Builder;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/11/14.
 */
@Builder
public class MasterDetailGroupComponentBuilder {

    private List<RelatedObjectDescribeStructure> detailObjectsDescribeList;

    private ObjectDescribeExt objectDescribeExt;

    private IObjectData objectData;

    private LayoutExt layoutExt;

    private User user;

    private Map<String, ILayout> listLayoutMap;

    private Map<String, List<IRecordTypeOption>> recordTypeOptionMap;

    private Map<String, List<String>> matchRecordTypeMap;

    private Map<String, List<String>> hasDataRecordTypeMap;

    private Map<String, Object> relatedListComponent;

    private ButtonLogicService buttonLogicService;

    private ChangeOrderLogicService changeOrderLogicService;

    public List<GroupComponent> getComponentList() {
        List<GroupComponent> masterDetailGroupComponentList = detailObjectsDescribeList.stream()
                .map(x -> generateMultiTableGroupComponent(x)).collect(Collectors.toList());
        //过滤已设置隐藏的tab
        layoutExt.removeHiddenComponents(masterDetailGroupComponentList);

        return masterDetailGroupComponentList;
    }

    public List<IMultiTableComponent> getMultiTableList() {
        List<IMultiTableComponent> masterDetailComponentList = detailObjectsDescribeList.stream()
                .map(x -> {
                    IMultiTableComponent detailComponent = generateMultiTableComponent(x, false);
                    syncDetailComponentInfo(detailComponent);
                    return detailComponent;
                }).collect(Collectors.toList());
        //过滤已设置隐藏的tab
        layoutExt.removeHiddenComponents(masterDetailComponentList);

        return masterDetailComponentList;
    }

    public List<IComponent> getComponentListForDesigner() {
        List<IComponent> masterDetailGroupComponentList = Lists.newArrayList();
        for (RelatedObjectDescribeStructure detailObject : detailObjectsDescribeList) {
            masterDetailGroupComponentList.add(buildDetailGroupComponentWithoutChildComponents(detailObject));
        }

        masterDetailGroupComponentList.forEach(x -> ComponentExt.of(x).setDefaultValue());

        return masterDetailGroupComponentList;
    }

    public List<IComponent> getComponentListForNewDesigner() {
        return getComponentListForNewDesigner(true);
    }

    public List<IComponent> getComponentListForNewDesigner(boolean needSyncDetailComponentInfo) {
        List<IComponent> masterDetailGroupComponentList = Lists.newArrayList();
        for (RelatedObjectDescribeStructure detailObject : detailObjectsDescribeList) {
            IMultiTableComponent detailComponent = generateMultiTableComponent(detailObject, false);
            if (needSyncDetailComponentInfo) {
                syncDetailComponentInfo(detailComponent);
            }
            masterDetailGroupComponentList.add(detailComponent);
        }
        return masterDetailGroupComponentList;
    }

    private void syncDetailComponentInfo(IMultiTableComponent detailComponent) {
        Optional.ofNullable(layoutExt)
                .map(LayoutExt::getComponentsSilently)
                .ifPresent(components -> ComponentExt.of(detailComponent).syncProps(components));
    }

    private GroupComponent generateMultiTableGroupComponent(RelatedObjectDescribeStructure detailObject) {
        GroupComponent groupComponent = buildDetailGroupComponentWithoutChildComponents(detailObject);
        IMultiTableComponent multiTableComponent = generateMultiTableComponent(detailObject, true);
        groupComponent.setChildComponents(Lists.newArrayList(multiTableComponent));

        return groupComponent;
    }

    public IMultiTableComponent generateMultiTableComponent(RelatedObjectDescribeStructure detailObject, boolean needButton) {
        IObjectDescribe detailObjectDescribe = detailObject.getRelatedObjectDescribe();
        String describeApiName = detailObjectDescribe.getApiName();

        IMultiTableComponent multiTableComponent = LayoutComponents.buildDetailObjectComponent(detailObjectDescribe, apiName -> getOriginalApiNameByChangeOrder(user, apiName));
        List<ITableComponent> tableComponentList = Lists.newArrayList();
        List<IButton> buttons = needButton ? getButtons(detailObjectDescribe) : Lists.newArrayList();

        CollectionUtils.nullToEmpty(CollectionUtils.nullToEmpty(recordTypeOptionMap).get(describeApiName)).forEach(recordTypeOption -> {
            List<IButton> validButtons;
            List<String> matchRecordTypes = CollectionUtils.nullToEmpty(matchRecordTypeMap).get(describeApiName);
            if (matchRecordTypes != null && !matchRecordTypes.contains(recordTypeOption.getApiName())) {
                List<String> hasDataRecordTypes = CollectionUtils.nullToEmpty(hasDataRecordTypeMap).get(describeApiName);
                //没有数据且不匹配的业务类型不下发布局
                if (hasDataRecordTypes != null && !hasDataRecordTypes.contains(recordTypeOption.getApiName())) {
                    return;
                }
                //有数据但是不匹配的业务类型不下发按钮
                validButtons = Lists.newArrayList();
            } else {
                validButtons = filterByComponent(Lists.newArrayList(buttons));
            }
            TableComponent tableComponent = getTableComponentByRecordType(detailObject, recordTypeOption);
            tableComponent.setButtons(validButtons);
            tableComponentList.add(tableComponent);
        });

        multiTableComponent.setChildComponents(tableComponentList);
        multiTableComponent.setButtons(buttons);

        return multiTableComponent;
    }

    private String getOriginalApiNameByChangeOrder(User user, String describeApiName) {
        if (Objects.nonNull(objectDescribeExt) && objectDescribeExt.isChangeOrderObject()) {
            return changeOrderLogicService.findOriginalApiNameByChangeOrder(user, describeApiName);
        }
        return null;
    }

    private List<IButton> filterByComponent(List<IButton> buttons) {
        if (CollectionUtils.empty(buttons) || CollectionUtils.empty(relatedListComponent)) {
            return buttons;
        }
        return ListComponentExt.of(relatedListComponent).fillButtonInfoPageType(IComponentInfo.PAGE_TYPE_RELATED)
                .filterButtonByUsePage(buttons, ButtonUsePageType.ListNormal, IComponentInfo.PAGE_TYPE_RELATED);
    }

    public List<IButton> getButtons(IObjectDescribe detailDescribe) {
        //主对象已作废数据的子对象列表不展示按钮
        if (Objects.nonNull(objectData) && ObjectDataExt.of(objectData).isInvalid()) {
            return Lists.newArrayList();
        }
        //主已锁定并且是主从一起新建的从不展示按钮
        if (Objects.nonNull(objectData) && ObjectDataExt.of(objectData).isLock() && ObjectDescribeExt.of(detailDescribe).isSlaveObjectCreateWithMaster()) {
            return Lists.newArrayList();
        }
        List<IButton> buttons = buttonLogicService.getButtonByComponentActions(user, ComponentActions.SLAVE_OBJECT,
                ObjectDescribeExt.of(detailDescribe), null, false);
        //根据配置屏蔽相关对象的按钮
        String key = detailDescribe.getApiName() + "|" + objectDescribeExt.getApiName() + "|"
                + ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldDescribe()
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR))).getApiName();
        List<String> removeButtons = ButtonConfig.RELATED_LIST_HEADER_REMOVE_BUTTONS.get(key);
        if (CollectionUtils.notEmpty(removeButtons)) {
            buttons.removeIf(x -> removeButtons.contains(x.getAction()));
        }

        return buttons;
    }

    private TableComponent getTableComponentByRecordType(RelatedObjectDescribeStructure detailObject, IRecordTypeOption recordTypeOption) {
        ILayout listLayout = listLayoutMap == null ? null : listLayoutMap.get(detailObject.getRelatedObjectDescribe().getApiName());
        return LayoutExt.of(listLayout).getTableComponentByRecordType(detailObject, recordTypeOption);
    }

    public GroupComponent buildDetailGroupComponentWithoutChildComponents(RelatedObjectDescribeStructure detailObject) {
        GroupComponent groupComponent = new GroupComponent();
        groupComponent.setName(ComponentExt.getDetailComponentName(detailObject.getRelatedObjectDescribe().getApiName()));
        groupComponent.setHeader(detailObject.getRelatedListLabel());
        ComponentExt.of(groupComponent).setLimit(1);

        return groupComponent;
    }
}
