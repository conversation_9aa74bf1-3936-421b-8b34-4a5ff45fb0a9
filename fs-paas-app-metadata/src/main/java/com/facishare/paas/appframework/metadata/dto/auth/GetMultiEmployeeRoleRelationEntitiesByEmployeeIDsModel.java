package com.facishare.paas.appframework.metadata.dto.auth;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface GetMultiEmployeeRoleRelationEntitiesByEmployeeIDsModel {
    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends BaseAuthArg {
        private List<String> users;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BaseAuthResult {
        private List<UserRolePojo> result;
    }
}
