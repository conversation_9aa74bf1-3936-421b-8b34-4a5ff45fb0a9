package com.facishare.paas.appframework.metadata.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.app.VelocityEngine;

import java.io.StringWriter;
import java.util.Map;
import java.util.Properties;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/2/4
 */
public class VelocityUtil {
    private static Properties props = new Properties();

    static {
        props.setProperty(Velocity.INPUT_ENCODING, "UTF-8");
        props.setProperty(Velocity.RESOURCE_LOADER, "class");
        props.setProperty("class.resource.loader.class",
                "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
//        props.setProperty(Velocity.SET_NULL_ALLOWED, "true");
    }

    public static String replacePlaceholder(String template, Map contextData) {
        if (StringUtils.isBlank(template)) {
            return "";
        }
        //为了兼容contextData里不存在的key，这样可以显示为为空串
        template = StringUtils.replace(template, "${", "$!{");
        // 初始化并取得Velocity引擎
        VelocityEngine engine = new VelocityEngine(props);
        // 取得velocity的上下文context
        VelocityContext context = new VelocityContext(contextData);
        StringWriter writer = new StringWriter();
        engine.evaluate(context, writer, "", template);
        return writer.toString();
    }

}
