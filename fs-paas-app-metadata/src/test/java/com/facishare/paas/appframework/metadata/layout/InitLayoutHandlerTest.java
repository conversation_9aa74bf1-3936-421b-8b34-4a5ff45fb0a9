package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.FormTable;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * InitLayoutHandler 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("InitLayoutHandler 测试")
class InitLayoutHandlerTest {

  @Mock
  private ILayout layout;

  @Mock
  private IComponent component;

  private InitLayoutHandler initLayoutHandler;

  @BeforeEach
  void setUp() {
    initLayoutHandler = new InitLayoutHandler();
  }

  @Test
  @DisplayName("测试复制布局 - 转换为编辑布局")
  void testCopyLayout_ConvertToEdit() {
    // 准备测试数据
    when(layout.getDisplayName()).thenReturn("Test Layout");
    when(layout.getEvents()).thenReturn(Lists.newArrayList());

    try (MockedStatic<LayoutExt> layoutExtMock = mockStatic(LayoutExt.class);
         MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class)) {

      LayoutExt mockLayoutExt = mock(LayoutExt.class);
      ILayout mockNewLayout = mock(ILayout.class);

      layoutExtMock.when(() -> LayoutExt.of(layout)).thenReturn(mockLayoutExt);
      when(mockLayoutExt.copy()).thenReturn(mockNewLayout);
      when(mockLayoutExt.getName()).thenReturn("test_layout");
      when(mockLayoutExt.getDefaultEditLayoutName()).thenReturn("test_layout_edit");
      when(mockLayoutExt.toMap()).thenReturn(Maps.newHashMap());

      layoutExtMock.when(() -> LayoutExt.of(mockNewLayout)).thenReturn(mockLayoutExt);
      i18nExtMock.when(() -> I18NExt.getOrDefault(anyString(), anyString())).thenReturn(" - 编辑");

      // 执行测试
      ILayout result = initLayoutHandler.copyLayout(layout, LayoutTypes.EDIT);

      // 验证结果
      assertNotNull(result);
      verify(mockNewLayout).setLayoutType(LayoutTypes.EDIT);
      verify(mockNewLayout).setName("test_layout_edit");
      verify(mockNewLayout).setDisplayName("Test Layout - 编辑");
      verify(mockNewLayout).setEnableMobileLayout(false);
      verify(mockNewLayout).setMobileLayout(null);
      verify(mockNewLayout).setHiddenComponents(any(List.class));
      verify(mockNewLayout).setHiddenButtons(any(List.class));
      verify(mockNewLayout).setEvents(any(List.class));
      verify(mockLayoutExt).retainComponentsWithField();
      verify(mockLayoutExt).removeLayoutStructure();
    }
  }

  @Test
  @DisplayName("测试复制布局 - 转换为详情布局")
  void testCopyLayout_ConvertToDetail() {
    // 准备测试数据
    when(layout.getDisplayName()).thenReturn("Test Layout");
    when(layout.getEvents()).thenReturn(Lists.newArrayList());

    try (MockedStatic<LayoutExt> layoutExtMock = mockStatic(LayoutExt.class)) {
      LayoutExt mockLayoutExt = mock(LayoutExt.class);
      ILayout mockNewLayout = mock(ILayout.class);

      layoutExtMock.when(() -> LayoutExt.of(layout)).thenReturn(mockLayoutExt);
      when(mockLayoutExt.copy()).thenReturn(mockNewLayout);
      when(mockLayoutExt.getName()).thenReturn("test_layout");
      when(mockLayoutExt.toMap()).thenReturn(Maps.newHashMap());

      layoutExtMock.when(() -> LayoutExt.of(mockNewLayout)).thenReturn(mockLayoutExt);

      // 执行测试
      ILayout result = initLayoutHandler.copyLayout(layout, LayoutTypes.DETAIL);

      // 验证结果
      assertNotNull(result);
      verify(mockNewLayout).setLayoutType(LayoutTypes.DETAIL);
      verify(mockNewLayout).setName("test_layout");
      verify(mockNewLayout).setDisplayName("Test Layout");
      verify(mockNewLayout).setEnableMobileLayout(false);
      verify(mockNewLayout).setMobileLayout(null);
      verify(mockNewLayout).setHiddenComponents(any(List.class));
      verify(mockNewLayout).setHiddenButtons(any(List.class));
      verify(mockNewLayout).setEvents(any(List.class));
      verify(mockLayoutExt).removeLayoutStructure();
      verify(mockLayoutExt, never()).retainComponentsWithField();
    }
  }

  @Test
  @DisplayName("测试构建页签组件 - 转换为详情页")
  void testBuildTabComponent_ToDetail() {
    // 准备测试数据
    List<IComponent> components = Lists.newArrayList();
    List<IComponent> copyComponents = Lists.newArrayList();

    IComponent formComponent = mock(IComponent.class);
    when(formComponent.getName()).thenReturn("form_component");
    when(formComponent.getHeader()).thenReturn("Form Component");

    IComponent relatedComponent = mock(IComponent.class);

    components.add(formComponent);
    components.add(relatedComponent);

    IComponent copyComponent = mock(IComponent.class);
    when(copyComponent.getName()).thenReturn("copy_component");
    copyComponents.add(copyComponent);

    try (MockedStatic<ComponentExt> componentExtMock = mockStatic(ComponentExt.class);
         MockedStatic<LayoutComponents> layoutComponentsMock = mockStatic(LayoutComponents.class)) {

      ComponentExt mockComponentExt = mock(ComponentExt.class);
      componentExtMock.when(() -> ComponentExt.of(any())).thenReturn(mockComponentExt);
      when(mockComponentExt.isFormType()).thenReturn(true, false);
      when(mockComponentExt.isRelatedList()).thenReturn(false, true);
      when(mockComponentExt.isMasterDetailComponent()).thenReturn(false);
      when(mockComponentExt.isOperationLog()).thenReturn(false);

      TabsComponent mockTabsComponent = mock(TabsComponent.class);
      layoutComponentsMock.when(LayoutComponents::buildTabsComponent).thenReturn(mockTabsComponent);

      // 执行测试
      IComponent result = initLayoutHandler.buildTabComponent(components, true, copyComponents);

      // 验证结果
      assertNotNull(result);
      verify(mockTabsComponent).setComponents(any(List.class));
      verify(mockTabsComponent).setTabs(any(List.class));
    }
  }

  @Test
  @DisplayName("测试构建页签组件 - 非详情页转换")
  void testBuildTabComponent_NotToDetail() {
    // 准备测试数据
    List<IComponent> components = Lists.newArrayList();
    List<IComponent> copyComponents = Lists.newArrayList();

    IComponent testComponent = mock(IComponent.class);
    when(testComponent.getName()).thenReturn("test_component");
    when(testComponent.getHeader()).thenReturn("Test Component");
    components.add(testComponent);

    try (MockedStatic<LayoutComponents> layoutComponentsMock = mockStatic(LayoutComponents.class)) {
      TabsComponent mockTabsComponent = mock(TabsComponent.class);
      layoutComponentsMock.when(LayoutComponents::buildTabsComponent).thenReturn(mockTabsComponent);

      // 执行测试
      IComponent result = initLayoutHandler.buildTabComponent(components, false, copyComponents);

      // 验证结果
      assertNotNull(result);
      verify(mockTabsComponent).setComponents(any(List.class));
      verify(mockTabsComponent).setTabs(any(List.class));
    }
  }

  @Test
  @DisplayName("测试排除表格组件中的文本框组件")
  void testGetExcludeFormTableTextComponents_WithFormTable() {
    // 准备测试数据
    List<IComponent> components = Lists.newArrayList();

    IComponent formTableComponent = mock(IComponent.class);

    IComponent textComponent = mock(IComponent.class);
    when(textComponent.getName()).thenReturn("text_component");
    when(textComponent.getType()).thenReturn(ComponentExt.TYPE_TEXT_COMPONENT);

    IComponent otherComponent = mock(IComponent.class);
    when(otherComponent.getType()).thenReturn("other_type");

    components.add(formTableComponent);
    components.add(textComponent);
    components.add(otherComponent);

    try (MockedStatic<ComponentExt> componentExtMock = mockStatic(ComponentExt.class);
         MockedStatic<FormTable> formTableMock = mockStatic(FormTable.class)) {

      ComponentExt mockComponentExt1 = mock(ComponentExt.class);
      ComponentExt mockComponentExt2 = mock(ComponentExt.class);
      ComponentExt mockComponentExt3 = mock(ComponentExt.class);

      componentExtMock.when(() -> ComponentExt.of(formTableComponent)).thenReturn(mockComponentExt1);
      componentExtMock.when(() -> ComponentExt.of(textComponent)).thenReturn(mockComponentExt2);
      componentExtMock.when(() -> ComponentExt.of(otherComponent)).thenReturn(mockComponentExt3);

      when(mockComponentExt1.isFormTable()).thenReturn(true);
      when(mockComponentExt2.isFormTable()).thenReturn(false);
      when(mockComponentExt3.isFormTable()).thenReturn(false);

      FormTable mockFormTable = mock(FormTable.class);
      formTableMock.when(() -> FormTable.of(formTableComponent)).thenReturn(mockFormTable);
      when(mockFormTable.getTextComponentApiNameList()).thenReturn(Lists.newArrayList("text_component"));

      // 执行测试
      List<IComponent> result = initLayoutHandler.getExcludeFormTableTextComponents(components);

      // 验证结果
      assertNotNull(result);
      assertEquals(2, result.size()); // 应该排除text_component
      assertTrue(result.contains(formTableComponent));
      assertTrue(result.contains(otherComponent));
      assertFalse(result.contains(textComponent));
    }
  }

  @Test
  @DisplayName("测试排除表格组件中的文本框组件 - 无表格组件")
  void testGetExcludeFormTableTextComponents_NoFormTable() {
    // 准备测试数据
    List<IComponent> components = Lists.newArrayList();

    IComponent textComponent = mock(IComponent.class);
    when(textComponent.getType()).thenReturn(ComponentExt.TYPE_TEXT_COMPONENT);

    components.add(textComponent);

    // 执行测试
    List<IComponent> result = initLayoutHandler.getExcludeFormTableTextComponents(components);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertTrue(result.contains(textComponent));
  }
}
