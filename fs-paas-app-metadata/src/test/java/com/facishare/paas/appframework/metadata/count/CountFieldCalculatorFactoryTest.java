package com.facishare.paas.appframework.metadata.count;

import com.facishare.paas.I18N;
import com.facishare.paas.metadata.api.describe.Count;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

/**
 * CountFieldCalculatorFactory的单元测试
 * 测试计数字段计算器工厂的功能
 */
@ExtendWith(MockitoExtension.class)
class CountFieldCalculatorFactoryTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试获取SUM类型计算器
     */
    @Test
    @DisplayName("获取计算器 - SUM类型")
    void testGetCountFieldCalculator_SUM类型() {
        // 执行被测试方法
        CountFieldCalculator calculator = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_SUM);
        
        // 验证结果
        assertNotNull(calculator);
        assertTrue(calculator instanceof SumCalculator);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取AVERAGE类型计算器
     */
    @Test
    @DisplayName("获取计算器 - AVERAGE类型")
    void testGetCountFieldCalculator_AVERAGE类型() {
        // 执行被测试方法
        CountFieldCalculator calculator = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_AVERAGE);
        
        // 验证结果
        assertNotNull(calculator);
        assertTrue(calculator instanceof AverageCalculator);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取MAX类型计算器
     */
    @Test
    @DisplayName("获取计算器 - MAX类型")
    void testGetCountFieldCalculator_MAX类型() {
        // 执行被测试方法
        CountFieldCalculator calculator = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_MAX);
        
        // 验证结果
        assertNotNull(calculator);
        assertTrue(calculator instanceof MaxCalculator);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取MIN类型计算器
     */
    @Test
    @DisplayName("获取计算器 - MIN类型")
    void testGetCountFieldCalculator_MIN类型() {
        // 执行被测试方法
        CountFieldCalculator calculator = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_MIN);
        
        // 验证结果
        assertNotNull(calculator);
        assertTrue(calculator instanceof MinCalculator);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取COUNT类型计算器
     */
    @Test
    @DisplayName("获取计算器 - COUNT类型")
    void testGetCountFieldCalculator_COUNT类型() {
        // 执行被测试方法
        CountFieldCalculator calculator = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_COUNT);
        
        // 验证结果
        assertNotNull(calculator);
        assertTrue(calculator instanceof CountCalculator);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取不支持的计算器类型
     */
    @Test
    @DisplayName("获取计算器 - 不支持的类型")
    void testGetCountFieldCalculator_不支持的类型() {
        // 准备测试数据
        String unsupportedType = "UNSUPPORTED_TYPE";
        
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            mockedI18N.when(() -> I18N.text(anyString(), anyString()))
                    .thenReturn("不支持的计数类型: " + unsupportedType);
            
            // 执行被测试方法并验证异常
            NullPointerException exception = assertThrows(NullPointerException.class, () -> {
                CountFieldCalculatorFactory.getCountFieldCalculator(unsupportedType);
            });
            
            // 验证异常信息
            assertTrue(exception.getMessage().contains("不支持的计数类型"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取计算器时传入null
     */
    @Test
    @DisplayName("获取计算器 - 传入null")
    void testGetCountFieldCalculator_传入null() {
        // 执行被测试方法并验证异常
        assertThrows(NullPointerException.class, () -> {
            CountFieldCalculatorFactory.getCountFieldCalculator(null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取计算器时传入空字符串
     */
    @Test
    @DisplayName("获取计算器 - 传入空字符串")
    void testGetCountFieldCalculator_传入空字符串() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            mockedI18N.when(() -> I18N.text(anyString(), anyString()))
                    .thenReturn("不支持的计数类型: ");
            
            // 执行被测试方法并验证异常
            NullPointerException exception = assertThrows(NullPointerException.class, () -> {
                CountFieldCalculatorFactory.getCountFieldCalculator("");
            });
            
            // 验证异常信息
            assertTrue(exception.getMessage().contains("不支持的计数类型"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试所有支持的计算器类型都能正确返回
     */
    @Test
    @DisplayName("获取计算器 - 所有支持的类型")
    void testGetCountFieldCalculator_所有支持的类型() {
        // 测试所有支持的类型
        String[] supportedTypes = {
            Count.TYPE_SUM,
            Count.TYPE_AVERAGE,
            Count.TYPE_MAX,
            Count.TYPE_MIN,
            Count.TYPE_COUNT
        };
        
        Class<?>[] expectedClasses = {
            SumCalculator.class,
            AverageCalculator.class,
            MaxCalculator.class,
            MinCalculator.class,
            CountCalculator.class
        };
        
        // 验证每种类型都能返回正确的计算器
        for (int i = 0; i < supportedTypes.length; i++) {
            CountFieldCalculator calculator = CountFieldCalculatorFactory.getCountFieldCalculator(supportedTypes[i]);
            assertNotNull(calculator, "计算器不应为null，类型: " + supportedTypes[i]);
            assertTrue(expectedClasses[i].isInstance(calculator), 
                    "计算器类型不匹配，期望: " + expectedClasses[i].getSimpleName() + 
                    "，实际: " + calculator.getClass().getSimpleName());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试计算器工厂的单例特性
     */
    @Test
    @DisplayName("计算器工厂 - 单例特性")
    void testGetCountFieldCalculator_单例特性() {
        // 多次获取相同类型的计算器
        CountFieldCalculator calculator1 = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_SUM);
        CountFieldCalculator calculator2 = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_SUM);
        
        // 验证返回的是同一个实例（静态Map中的同一个对象）
        assertSame(calculator1, calculator2, "相同类型的计算器应该返回同一个实例");
        
        // 验证不同类型返回不同实例
        CountFieldCalculator averageCalculator = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_AVERAGE);
        assertNotSame(calculator1, averageCalculator, "不同类型的计算器应该返回不同实例");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试大小写敏感性
     */
    @Test
    @DisplayName("获取计算器 - 大小写敏感")
    void testGetCountFieldCalculator_大小写敏感() {
        // 测试错误的大写类型
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            mockedI18N.when(() -> I18N.text(anyString(), anyString()))
                    .thenReturn("不支持的计数类型: SUM");

            // 执行被测试方法并验证异常
            assertThrows(NullPointerException.class, () -> {
                CountFieldCalculatorFactory.getCountFieldCalculator("SUM"); // 大写
            });
        }

        // 测试正确的小写类型
        CountFieldCalculator calculator = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_SUM);
        assertNotNull(calculator);
        assertTrue(calculator instanceof SumCalculator);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试工厂方法的线程安全性
     */
    @Test
    @DisplayName("计算器工厂 - 线程安全性")
    void testGetCountFieldCalculator_线程安全性() throws InterruptedException {
        // 创建多个线程同时访问工厂方法
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        CountFieldCalculator[] results = new CountFieldCalculator[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                results[index] = CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_SUM);
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 验证所有线程都获得了相同的实例
        CountFieldCalculator firstResult = results[0];
        assertNotNull(firstResult);
        
        for (int i = 1; i < threadCount; i++) {
            assertSame(firstResult, results[i], "所有线程应该获得相同的计算器实例");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试计算器类型常量的正确性
     */
    @Test
    @DisplayName("计算器类型常量 - 正确性验证")
    void testCountTypeConstants() {
        // 验证Count类中定义的常量值
        assertEquals("sum", Count.TYPE_SUM);
        assertEquals("average", Count.TYPE_AVERAGE);
        assertEquals("max", Count.TYPE_MAX);
        assertEquals("min", Count.TYPE_MIN);
        assertEquals("count", Count.TYPE_COUNT);
        
        // 验证这些常量都能正确获取到计算器
        assertNotNull(CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_SUM));
        assertNotNull(CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_AVERAGE));
        assertNotNull(CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_MAX));
        assertNotNull(CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_MIN));
        assertNotNull(CountFieldCalculatorFactory.getCountFieldCalculator(Count.TYPE_COUNT));
    }
}
