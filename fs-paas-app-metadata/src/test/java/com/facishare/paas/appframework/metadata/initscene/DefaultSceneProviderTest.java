package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.SearchTemplateCode;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DefaultSceneProvider的单元测试
 * 测试默认场景提供器的功能
 */
@ExtendWith(MockitoExtension.class)
class DefaultSceneProviderTest {

    @Mock
    private ISearchTemplateService mockSearchTemplateService;

    @Mock
    private User mockUser;

    @InjectMocks
    private DefaultSceneProvider defaultSceneProvider;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为 - 使用lenient模式避免UnnecessaryStubbing错误
        lenient().when(mockUser.getTenantId()).thenReturn("testTenant");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getApiName方法
     */
    @Test
    @DisplayName("正常场景 - getApiName返回null")
    void testGetApiName_ReturnsNull() {
        // 执行测试
        String result = defaultSceneProvider.getApiName();

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList的正常场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList获取默认搜索模板成功")
    void testGetDefaultSearchTemplateList_Success() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "Account";
        String extendAttribute = "testAttribute";

        // Mock搜索模板
        List<ISearchTemplate> mockTemplates = new ArrayList<>();
        ISearchTemplate template1 = new SearchTemplate();
        ISearchTemplate template2 = new SearchTemplate();
        mockTemplates.add(template1);
        mockTemplates.add(template2);

        when(mockSearchTemplateService.findByObjectDescribeAPINameAndCode(
                eq("testTenant"), eq(apiName), any(Set.class)))
                .thenReturn(mockTemplates);

        // 执行测试
        List<ISearchTemplate> result = defaultSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());


        // 验证调用了正确的方法和参数
        verify(mockSearchTemplateService).findByObjectDescribeAPINameAndCode(
                eq("testTenant"), eq(apiName), any(Set.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList时服务返回空列表
     */
    @Test
    @DisplayName("边界场景 - getDefaultSearchTemplateList时服务返回空列表")
    void testGetDefaultSearchTemplateList_EmptyResult() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "Account";
        String extendAttribute = "testAttribute";

        when(mockSearchTemplateService.findByObjectDescribeAPINameAndCode(
                eq("testTenant"), eq(apiName), any(Set.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<ISearchTemplate> result = defaultSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList时传入null参数
     */
    @Test
    @DisplayName("边界场景 - getDefaultSearchTemplateList传入null参数")
    void testGetDefaultSearchTemplateList_NullParameters() throws MetadataServiceException {
        // 准备测试数据
        List<ISearchTemplate> mockTemplates = new ArrayList<>();
        ISearchTemplate template = new SearchTemplate();
        mockTemplates.add(template);

        when(mockSearchTemplateService.findByObjectDescribeAPINameAndCode(
                anyString(), anyString(), any(Set.class)))
                .thenReturn(mockTemplates);

        // 测试传入null用户
        List<ISearchTemplate> result1 = defaultSceneProvider.getDefaultSearchTemplateList(null, "Account", "attr");
        assertNotNull(result1);
        assertTrue(result1.isEmpty()); // 因为user.getTenantId()会抛出NullPointerException，被catch处理

        // 测试传入null apiName
        List<ISearchTemplate> result2 = defaultSceneProvider.getDefaultSearchTemplateList(mockUser, null, "attr");
        assertNotNull(result2);
        assertEquals(1, result2.size());

        // 测试传入null extendAttribute
        List<ISearchTemplate> result3 = defaultSceneProvider.getDefaultSearchTemplateList(mockUser, "Account", null);
        assertNotNull(result3);
        assertEquals(1, result3.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList时服务抛出异常
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList时服务抛出MetadataServiceException")
    void testGetDefaultSearchTemplateList_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "Account";
        String extendAttribute = "testAttribute";

        when(mockSearchTemplateService.findByObjectDescribeAPINameAndCode(
                eq("testTenant"), eq(apiName), any(Set.class)))
                .thenThrow(new MetadataServiceException(ErrorCode.FS_PAAS_ES_AUTH_CODE_QUERY_ERROR, "Service error"));

        // 执行测试
        List<ISearchTemplate> result = defaultSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);

        // 验证结果 - 异常被捕获，返回空列表
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList时服务抛出运行时异常
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList时服务抛出RuntimeException")
    void testGetDefaultSearchTemplateList_RuntimeException() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "Account";
        String extendAttribute = "testAttribute";

        when(mockSearchTemplateService.findByObjectDescribeAPINameAndCode(
                eq("testTenant"), eq(apiName), any(Set.class)))
                .thenThrow(new RuntimeException("Runtime error"));

        // 执行测试 - RuntimeException不会被catch，应该向外抛出
        assertThrows(RuntimeException.class, () -> {
            defaultSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList验证搜索模板代码集合
     */
    @Test
    @DisplayName("功能验证 - getDefaultSearchTemplateList验证搜索模板代码集合")
    void testGetDefaultSearchTemplateList_VerifySearchTemplateCodes() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "Account";
        String extendAttribute = "testAttribute";

        // 使用ArgumentCaptor捕获传入的Set参数
        when(mockSearchTemplateService.findByObjectDescribeAPINameAndCode(
                eq("testTenant"), eq(apiName), any(Set.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        defaultSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);

        // 验证调用参数
        verify(mockSearchTemplateService).findByObjectDescribeAPINameAndCode(
                eq("testTenant"), eq(apiName), argThat(codeSet -> {
                    // 验证包含所有预期的搜索模板代码
                    return codeSet.contains(SearchTemplateCode.ALL) &&
                            codeSet.contains(SearchTemplateCode.IN_CHARGE) &&
                            codeSet.contains(SearchTemplateCode.INVOLVED) &&
                            codeSet.contains(SearchTemplateCode.IN_CHARGE_DEPT) &&
                            codeSet.contains(SearchTemplateCode.SUB_IN_CHARGE) &&
                            codeSet.contains(SearchTemplateCode.SUB_INVOLVED) &&
                            codeSet.contains(SearchTemplateCode.SHARED) &&
                            codeSet.contains(SearchTemplateCode.FOLLOW) &&
                            codeSet.contains(SearchTemplateCode.RECENT_VISITS) &&
                            codeSet.size() == 9; // 确保只有这9个代码
                }));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList的复杂场景
     */
    @Test
    @DisplayName("复杂场景 - getDefaultSearchTemplateList处理大量搜索模板")
    void testGetDefaultSearchTemplateList_LargeTemplateSet() throws MetadataServiceException {
        // 准备测试数据 - 创建大量搜索模板
        String apiName = "Account";
        String extendAttribute = "testAttribute";

        List<ISearchTemplate> mockTemplates = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            ISearchTemplate template = new SearchTemplate();
            mockTemplates.add(template);
        }

        when(mockSearchTemplateService.findByObjectDescribeAPINameAndCode(
                eq("testTenant"), eq(apiName), any(Set.class)))
                .thenReturn(mockTemplates);

        // 执行测试
        List<ISearchTemplate> result = defaultSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(100, result.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList的性能
     */
    @Test
    @DisplayName("性能场景 - getDefaultSearchTemplateList执行性能")
    void testGetDefaultSearchTemplateList_Performance() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "Account";
        String extendAttribute = "testAttribute";

        List<ISearchTemplate> mockTemplates = new ArrayList<>();
        ISearchTemplate template = new SearchTemplate();
        mockTemplates.add(template);

        when(mockSearchTemplateService.findByObjectDescribeAPINameAndCode(
                eq("testTenant"), eq(apiName), any(Set.class)))
                .thenReturn(mockTemplates);

        // 测试执行时间
        long startTime = System.currentTimeMillis();

        List<ISearchTemplate> result = defaultSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // 验证结果和性能
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(executionTime < 100, "方法执行时间过长: " + executionTime + "ms");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(defaultSceneProvider);
        assertNotNull(mockSearchTemplateService);
        assertNotNull(mockUser);
    }
}
