package com.facishare.paas.appframework.metadata.options;

import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.service.ReferenceServiceProxy;
import com.facishare.paas.appframework.common.service.dto.DeleteReference;
import com.facishare.paas.appframework.common.service.dto.FindReferenceByTarget;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.SelectOneExt;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.reflect.Whitebox;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OptionSetLogicServiceImpl的JUnit5测试类
 * 
 * GenerateByAI
 * 
 * 功能：
 * - 迁移自Groovy测试，保持原有测试覆盖
 * - 使用JUnit5+Mockito技术栈
 * - 覆盖create、update、find等核心方法
 * 
 * 测试覆盖：
 * - create方法的正常场景和异常场景
 * - update方法的正常场景和异常场景  
 * - findAll方法的基础功能
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class OptionSetLogicServiceImplTest extends OptionsTestBase {
    
    @Mock
    private IRepository<MtOptionSet> repository;

    @Mock
    private LicenseService licenseService;

    @Mock
    private ReferenceServiceProxy referenceServiceProxy;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Mock
    private AppDefaultRocketMQProducer optionChangeProducer;

    @InjectMocks
    private OptionSetLogicServiceImpl optionSetLogicService;
    
    // 测试数据常量
    private static final String TENANT_ID = "78057";
    private static final String CUSTOM_OPTION_JSON =
        "{\"label\":\"通用选项集\",\"api_name\":\"option__c\",\"description\":\"\",\"is_active\":true,\"tenant_id\":\"78057\",\"version\":99,\"define_type\":\"custom\",\"options\":[{\"label\":\"示例选项\",\"font_color\":\"#181C25\",\"value\":\"option1\"}]}";
    private static final String PACKAGE_OPTION_JSON =
        "{\"label\":\"通用选项集\",\"api_name\":\"option__c\",\"description\":\"\",\"is_active\":true,\"tenant_id\":\"78057\",\"version\":99,\"define_type\":\"package\",\"options\":[{\"label\":\"示例选项\",\"font_color\":\"#181C25\",\"value\":\"option1\"}]}";
    private static final String EMPTY_DEFINE_TYPE_JSON =
        "{\"label\":\"通用选项集\",\"api_name\":\"option__c\",\"description\":\"\",\"is_active\":true,\"tenant_id\":\"78057\",\"version\":99,\"define_type\":\"\",\"options\":[{\"label\":\"示例选项\",\"value\":\"option1\"}]}";
    private static final String NO_DEFINE_TYPE_JSON =
        "{\"label\":\"通用选项集\",\"api_name\":\"option__c\",\"description\":\"\",\"is_active\":true,\"tenant_id\":\"78057\",\"version\":99,\"options\":[{\"label\":\"示例选项\",\"value\":\"option1\"}]}";
    private static final String INVALID_DEFINE_TYPE_JSON =
        "{\"label\":\"通用选项集\",\"api_name\":\"option__c\",\"description\":\"\",\"is_active\":true,\"tenant_id\":\"78057\",\"version\":99,\"define_type\":\"invalid\",\"options\":[{\"label\":\"示例选项\",\"value\":\"option1\"}]}";
    
    @BeforeEach
    void setUp() {
        // 设置I18n相关Mock
        setupI18nMocks();
        
        // 设置AppFrameworkConfig
        Whitebox.setInternalState(AppFrameworkConfig.class, "optionSetMaxCountGray", Collections.emptyMap());
    }
    
    /**
     * 设置I18n相关的Mock对象
     */
    private void setupI18nMocks() {
        try {
            I18nClient mockI18nClient = mock(I18nClient.class);
            I18nServiceImpl mockI18nServiceImpl = mock(I18nServiceImpl.class);
            
            when(mockI18nClient.getAllLanguage()).thenReturn(Collections.emptyList());
            Whitebox.setInternalState(mockI18nClient, "impl", mockI18nServiceImpl);
            Whitebox.setInternalState(I18nClient.class, "SINGLETON", mockI18nClient);
        } catch (Exception e) {
            System.err.println("Warning: Failed to setup I18n mocks: " + e.getMessage());
        }
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试create方法的正常场景，包括不同的define_type
     */
    @ParameterizedTest
    @MethodSource("provideCreateSuccessTestData")
    @DisplayName("测试create方法 - 正常场景")
    void testCreate_Success(String testCase, String json, int expectedCheckCount) {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        MtOptionSet option = MtOptionSet.fromJson(json);
        
        // 配置Mock行为
        when(repository.findCountOnly(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(1);
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Collections.emptyList());
        when(repository.bulkCreate(any(User.class), anyList())).thenReturn(Arrays.asList(option));
        
        // Act: 执行被测试方法
        MtOptionSet result = optionSetLogicService.create(user, option);
        
        // Assert: 验证结果
        assertNotNull(result, "创建结果不应为null");
        
        // 验证Mock交互
        if (expectedCheckCount > 0) {
            verify(repository, times(expectedCheckCount)).findCountOnly(any(User.class), any(), eq(MtOptionSet.class));
        }
        verify(licenseService, times(2)).existModule(anyString(), anySet());
        verify(repository, times(1)).findBy(any(User.class), any(), eq(MtOptionSet.class));
        verify(repository, times(1)).bulkCreate(any(User.class), anyList());
    }
    
    /**
     * 提供create方法成功场景的测试数据
     */
    private static Stream<Arguments> provideCreateSuccessTestData() {
        return Stream.of(
            Arguments.of("custom类型", CUSTOM_OPTION_JSON, 1),
            Arguments.of("package类型", PACKAGE_OPTION_JSON, 0),
            Arguments.of("空define_type", EMPTY_DEFINE_TYPE_JSON, 1),
            Arguments.of("无define_type", NO_DEFINE_TYPE_JSON, 1)
        );
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试create方法的异常场景，验证无效define_type抛出异常
     */
    @Test
    @DisplayName("测试create方法 - 异常场景")
    void testCreate_ThrowsValidateException() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        MtOptionSet option = MtOptionSet.fromJson(INVALID_DEFINE_TYPE_JSON);
        
        // Act & Assert: 验证异常抛出
        assertThrows(ValidateException.class, () -> {
            optionSetLogicService.create(user, option);
        }, "无效的define_type应该抛出ValidateException");
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试update方法的正常场景，包括不同的define_type
     */
    @ParameterizedTest
    @MethodSource("provideUpdateSuccessTestData")
    @DisplayName("测试update方法 - 正常场景")
    void testUpdate_Success(String testCase, String json) {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        MtOptionSet option = MtOptionSet.fromJson(json);
        
        // 配置Mock行为
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Arrays.asList(option));
        when(repository.update(any(User.class), any(MtOptionSet.class))).thenReturn(option);
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());
        
        // Act: 执行被测试方法
        MtOptionSet result = optionSetLogicService.update(user, option, false);
        
        // Assert: 验证结果
        assertNotNull(result, "更新结果不应为null");
        
        // 验证Mock交互
        verify(repository, times(1)).findBy(any(User.class), any(), eq(MtOptionSet.class));
        verify(repository, times(1)).update(any(User.class), any(MtOptionSet.class));
        verify(licenseService, atLeastOnce()).existModule(anyString(), anySet());
    }
    
    /**
     * 提供update方法成功场景的测试数据
     */
    private static Stream<Arguments> provideUpdateSuccessTestData() {
        return Stream.of(
            Arguments.of("custom类型", CUSTOM_OPTION_JSON),
            Arguments.of("custom类型2", CUSTOM_OPTION_JSON),
            Arguments.of("空define_type", EMPTY_DEFINE_TYPE_JSON),
            Arguments.of("无define_type", NO_DEFINE_TYPE_JSON)
        );
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试update方法的异常场景，验证无效define_type抛出异常
     */
    @Test
    @DisplayName("测试update方法 - 异常场景")
    void testUpdate_ThrowsValidateException() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        MtOptionSet option = MtOptionSet.fromJson(INVALID_DEFINE_TYPE_JSON);
        
        // Act & Assert: 验证异常抛出
        assertThrows(ValidateException.class, () -> {
            optionSetLogicService.update(user, option, false);
        }, "无效的define_type应该抛出ValidateException");
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试findAll方法的基础功能
     */
    @Test
    @DisplayName("测试findAll方法 - 基础功能")
    void testFindAll_Success() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        
        // 配置Mock行为
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Collections.emptyList());
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());
        
        // Act: 执行被测试方法
        List<MtOptionSet> result = optionSetLogicService.findAll(user);
        
        // Assert: 验证结果
        assertNotNull(result, "查询结果不应为null");
        assertTrue(result.isEmpty(), "查询结果应为空列表");
        
        // 验证Mock交互
        verify(repository, times(1)).findBy(any(User.class), any(), eq(MtOptionSet.class));
        verify(licenseService, times(1)).existModule(anyString(), anySet());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试find方法的正常场景
     */
    @Test
    @DisplayName("测试find方法 - 正常场景")
    void testFind_Success() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        String optionApiName = "test_option__c";
        MtOptionSet expectedOption = createTestOptionSet();

        // 配置Mock行为
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Arrays.asList(expectedOption));
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());

        // Act: 执行被测试方法
        Optional<MtOptionSet> result = optionSetLogicService.find(user, optionApiName);

        // Assert: 验证结果
        assertTrue(result.isPresent(), "查询结果应该存在");
        assertEquals(expectedOption, result.get(), "查询结果应该匹配");

        // 验证Mock交互
        verify(repository, times(1)).findBy(any(User.class), any(), eq(MtOptionSet.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试find方法的空结果场景
     */
    @Test
    @DisplayName("测试find方法 - 空结果场景")
    void testFind_NotFound() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        String optionApiName = "non_existent_option__c";

        // 配置Mock行为
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Collections.emptyList());
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());

        // Act: 执行被测试方法
        Optional<MtOptionSet> result = optionSetLogicService.find(user, optionApiName);

        // Assert: 验证结果
        assertFalse(result.isPresent(), "查询结果应该为空");

        // 验证Mock交互
        verify(repository, times(1)).findBy(any(User.class), any(), eq(MtOptionSet.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enable方法的正常场景
     */
    @Test
    @DisplayName("测试enable方法 - 正常场景")
    void testEnable_Success() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        String optionApiName = "test_option__c";
        MtOptionSet disabledOption = createTestOptionSet();
        disabledOption.setActive(false);
        MtOptionSet enabledOption = createTestOptionSet();
        enabledOption.setActive(true);

        // 配置Mock行为
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Arrays.asList(disabledOption));
        when(repository.update(any(User.class), any(MtOptionSet.class))).thenReturn(enabledOption);
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());

        // Act: 执行被测试方法
        MtOptionSet result = optionSetLogicService.enable(user, optionApiName);

        // Assert: 验证结果
        assertNotNull(result, "启用结果不应为null");
        assertTrue(result.getActive(), "选项集应该被启用");

        // 验证Mock交互
        verify(repository, times(1)).findBy(any(User.class), any(), eq(MtOptionSet.class));
        verify(repository, times(1)).update(any(User.class), any(MtOptionSet.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试disable方法的正常场景
     */
    @Test
    @DisplayName("测试disable方法 - 正常场景")
    void testDisable_Success() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        String optionApiName = "test_option__c";
        MtOptionSet enabledOption = createTestOptionSet();
        enabledOption.setActive(true);
        MtOptionSet disabledOption = createTestOptionSet();
        disabledOption.setActive(false);

        // 配置Mock行为
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Arrays.asList(enabledOption));
        when(repository.update(any(User.class), any(MtOptionSet.class))).thenReturn(disabledOption);
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());

        // Act: 执行被测试方法
        MtOptionSet result = optionSetLogicService.disable(user, optionApiName);

        // Assert: 验证结果
        assertNotNull(result, "禁用结果不应为null");
        assertFalse(result.getActive(), "选项集应该被禁用");

        // 验证Mock交互
        verify(repository, times(1)).findBy(any(User.class), any(), eq(MtOptionSet.class));
        verify(repository, times(1)).update(any(User.class), any(MtOptionSet.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkCount方法的正常场景
     */
    @Test
    @DisplayName("测试checkCount方法 - 正常场景")
    void testCheckCount_Success() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);

        // 配置Mock行为 - 返回小于最大限制的数量
        when(repository.findCountOnly(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(5);

        // Act & Assert: 执行被测试方法，不应抛出异常
        assertDoesNotThrow(() -> {
            optionSetLogicService.checkCount(user);
        }, "正常情况下不应抛出异常");

        // 验证Mock交互
        verify(repository, times(1)).findCountOnly(any(User.class), any(), eq(MtOptionSet.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkCount方法的异常场景，验证超过限制时抛出异常
     */
    @Test
    @DisplayName("测试checkCount方法 - 超过限制异常")
    void testCheckCount_ThrowsValidateException() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);

        // 配置Mock行为 - 返回超过最大限制的数量
        when(repository.findCountOnly(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(25);

        // Act & Assert: 验证异常抛出
        assertThrows(ValidateException.class, () -> {
            optionSetLogicService.checkCount(user);
        }, "超过限制时应该抛出ValidateException");

        // 验证Mock交互
        verify(repository, times(1)).findCountOnly(any(User.class), any(), eq(MtOptionSet.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleted方法的正常场景
     */
    @Test
    @DisplayName("测试deleted方法 - 正常场景")
    void testDeleted_Success() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        String optionApiName = "test_option__c";
        MtOptionSet customOption = createTestOptionSet();
        customOption.setDefineType("custom");

        // 配置Mock行为
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Arrays.asList(customOption));
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());

        // Mock findReference返回空引用
        OptionReference emptyReference = OptionsMockFactory.createMockOptionReference();
        when(referenceServiceProxy.findByTarget(any(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(new FindReferenceByTarget.Result());
        when(describeLogicService.findObjectsWithoutCopyIfGray(anyString(), any())).thenReturn(Collections.emptyMap());

        // Act & Assert: 执行被测试方法，不应抛出异常
        assertDoesNotThrow(() -> {
            optionSetLogicService.deleted(user, optionApiName);
        }, "删除自定义选项集不应抛出异常");

        // 验证Mock交互
        verify(repository, times(1)).bulkInvalidAndDelete(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleted方法的异常场景，验证删除不存在的选项集抛出异常
     */
    @Test
    @DisplayName("测试deleted方法 - 选项集不存在异常")
    void testDeleted_ThrowsValidateExceptionWhenNotFound() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        String optionApiName = "non_existent_option__c";

        // 配置Mock行为 - 返回空结果
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Collections.emptyList());
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());

        // Act & Assert: 验证异常抛出
        assertThrows(ValidateException.class, () -> {
            optionSetLogicService.deleted(user, optionApiName);
        }, "删除不存在的选项集应该抛出ValidateException");

        // 验证Mock交互
        verify(repository, times(1)).findBy(any(User.class), any(), eq(MtOptionSet.class));
        verify(repository, never()).bulkInvalidAndDelete(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findByApiNames方法的正常场景
     */
    @Test
    @DisplayName("测试findByApiNames方法 - 正常场景")
    void testFindByApiNames_Success() {
        // Arrange: 准备测试数据
        User user = User.systemUser(TENANT_ID);
        List<String> apiNames = Arrays.asList("option1__c", "option2__c");
        MtOptionSet option1 = createTestOptionSet();
        option1.setApiName("option1__c");
        MtOptionSet option2 = createTestOptionSet();
        option2.setApiName("option2__c");

        // 配置Mock行为
        when(repository.findBy(any(User.class), any(), eq(MtOptionSet.class))).thenReturn(Arrays.asList(option1, option2));
        when(licenseService.existModule(anyString(), anySet())).thenReturn(Collections.emptyMap());

        // Act: 执行被测试方法
        Map<String, MtOptionSet> result = optionSetLogicService.findByApiNames(user, apiNames);

        // Assert: 验证结果
        assertNotNull(result, "查询结果不应为null");
        assertEquals(2, result.size(), "应该返回2个选项集");
        assertTrue(result.containsKey("option1__c"), "应该包含option1__c");
        assertTrue(result.containsKey("option2__c"), "应该包含option2__c");

        // 验证Mock交互
        verify(repository, times(1)).findBy(any(User.class), any(), eq(MtOptionSet.class));
    }
}
