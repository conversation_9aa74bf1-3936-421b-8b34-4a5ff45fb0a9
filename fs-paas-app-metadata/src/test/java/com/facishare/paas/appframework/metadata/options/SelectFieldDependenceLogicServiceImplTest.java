package com.facishare.paas.appframework.metadata.options;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.ref.RefMessage;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import com.facishare.paas.appframework.metadata.reference.RefFieldService;
import com.facishare.paas.appframework.metadata.repository.model.MtFieldDependence;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.service.IDistributedLockService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.MetadataTransactionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SelectFieldDependenceLogicServiceImpl的JUnit5测试类
 * 
 * GenerateByAI
 * 
 * 功能：
 * - 测试选择字段依赖关系的所有业务逻辑
 * - 覆盖8个核心方法的完整测试
 * - 验证字段依赖关系的创建、更新、删除流程
 * 
 * 测试覆盖：
 * - findFieldDependenceWitchObjectApiName - 按对象查找字段依赖
 * - find - 查找特定字段依赖
 * - create - 创建字段依赖
 * - copy - 复制字段依赖
 * - update - 更新字段依赖
 * - deleted - 删除字段依赖
 * - findChildFields - 查找子字段
 * - findAll - 查找所有依赖
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class SelectFieldDependenceLogicServiceImplTest extends OptionsTestBase {
    
    @Mock
    private FieldDependenceLogicService fieldDependenceLogicService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private RefFieldService refFieldService;
    
    @Mock
    private IDistributedLockService distributedLockService;
    
    @Mock
    private MetadataTransactionService metadataTransactionService;
    
    @InjectMocks
    private SelectFieldDependenceLogicServiceImpl selectFieldDependenceLogicService;
    
    // 测试数据常量
    private static final String TENANT_ID = "test-tenant-123";
    private static final String DESCRIBE_API_NAME = "TestObject__c";
    private static final String PARENT_FIELD_API_NAME = "parent_field__c";
    private static final String CHILD_FIELD_API_NAME = "child_field__c";
    private static final String UPSTREAM_TENANT_ID = "upstream-tenant-456";
    
    private User testUser;
    private IObjectDescribe mockObjectDescribe;
    private ObjectDescribeExt mockObjectDescribeExt;
    private IFieldDescribe mockParentField;
    private IFieldDescribe mockChildField;
    private SelectOne mockSelectOne;
    private SelectFieldDependence testSelectFieldDependence;
    
    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = User.systemUser(TENANT_ID);
        
        // 创建Mock对象
        setupMockObjects();
        
        // 创建测试数据
        testSelectFieldDependence = OptionsMockFactory.createMockSelectFieldDependence(
                DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
    }
    
    /**
     * 设置Mock对象的基础配置
     */
    private void setupMockObjects() {
        // Mock IObjectDescribe
        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn(DESCRIBE_API_NAME);
        when(mockObjectDescribe.getTenantId()).thenReturn(TENANT_ID);
        when(mockObjectDescribe.getUpstreamTenantId()).thenReturn(UPSTREAM_TENANT_ID);
        
        // Mock IFieldDescribe
        mockParentField = mock(IFieldDescribe.class);
        when(mockParentField.getApiName()).thenReturn(PARENT_FIELD_API_NAME);
        when(mockParentField.getDescribeApiName()).thenReturn(DESCRIBE_API_NAME);
        when(mockParentField.getType()).thenReturn("SelectOne");
        
        mockChildField = mock(IFieldDescribe.class);
        when(mockChildField.getApiName()).thenReturn(CHILD_FIELD_API_NAME);
        when(mockChildField.getDescribeApiName()).thenReturn(DESCRIBE_API_NAME);
        when(mockChildField.getType()).thenReturn("SelectOne");
        
        // Mock SelectOne
        mockSelectOne = mock(SelectOne.class);
        when(mockSelectOne.getApiName()).thenReturn(CHILD_FIELD_API_NAME);
        when(mockSelectOne.getCascadeParentApiName()).thenReturn(PARENT_FIELD_API_NAME);
        when(mockSelectOne.getSelectOptions()).thenReturn(Collections.emptyList());
        
        // 配置ObjectDescribe的字段查找
        when(mockObjectDescribe.getFieldDescribe(PARENT_FIELD_API_NAME)).thenReturn(mockParentField);
        when(mockObjectDescribe.getFieldDescribe(CHILD_FIELD_API_NAME)).thenReturn(mockChildField);
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试findFieldDependenceWitchObjectApiName方法的正常场景
     */
    @Test
    @DisplayName("测试findFieldDependenceWitchObjectApiName方法 - 正常场景")
    void testFindFieldDependenceWitchObjectApiName_Success() {
        // Arrange: 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(mockObjectDescribe);
        
        // Mock ObjectDescribeExt的静态方法调用
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockDescribeExt);
            
            // 配置getSelectOneFields返回包含子字段的列表
            when(mockDescribeExt.getSelectOneFields()).thenReturn(Arrays.asList(mockSelectOne));
            when(mockDescribeExt.getFieldDescribeSilently(PARENT_FIELD_API_NAME))
                    .thenReturn(Optional.of(mockParentField));
            
            // Mock FieldDescribeExt
            try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
                FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
                mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockParentField)).thenReturn(mockFieldDescribeExt);
                when(mockFieldDescribeExt.isCascadeParentField()).thenReturn(true);
                when(mockFieldDescribeExt.getFieldDescribe()).thenReturn(mockParentField);
                
                // Mock SelectFieldDependence.createWithOutDependence
                try (MockedStatic<SelectFieldDependence> mockedSelectFieldDependence = mockStatic(SelectFieldDependence.class)) {
                    SelectFieldDependence expectedResult = testSelectFieldDependence;
                    mockedSelectFieldDependence.when(() -> SelectFieldDependence.createWithOutDependence(mockParentField, mockSelectOne))
                            .thenReturn(expectedResult);
                    
                    // Act: 执行被测试方法
                    List<SelectFieldDependence> result = selectFieldDependenceLogicService
                            .findFieldDependenceWitchObjectApiName(testUser, DESCRIBE_API_NAME);
                    
                    // Assert: 验证结果
                    assertNotNull(result, "查询结果不应为null");
                    assertEquals(1, result.size(), "应该返回1个字段依赖");
                    assertEquals(expectedResult, result.get(0), "返回的字段依赖应该匹配");
                    
                    // 验证Mock交互
                    verify(describeLogicService, times(1))
                            .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
                }
            }
        }
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试findFieldDependenceWitchObjectApiName方法的空结果场景
     */
    @Test
    @DisplayName("测试findFieldDependenceWitchObjectApiName方法 - 空结果场景")
    void testFindFieldDependenceWitchObjectApiName_EmptyResult() {
        // Arrange: 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(mockObjectDescribe);
        
        // Mock ObjectDescribeExt返回空的SelectOne字段列表
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.getSelectOneFields()).thenReturn(Collections.emptyList());
            
            // Act: 执行被测试方法
            List<SelectFieldDependence> result = selectFieldDependenceLogicService
                    .findFieldDependenceWitchObjectApiName(testUser, DESCRIBE_API_NAME);
            
            // Assert: 验证结果
            assertNotNull(result, "查询结果不应为null");
            assertTrue(result.isEmpty(), "查询结果应为空列表");
            
            // 验证Mock交互
            verify(describeLogicService, times(1))
                    .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
        }
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试find方法的正常场景
     */
    @Test
    @DisplayName("测试find方法 - 正常场景")
    void testFind_Success() {
        // Arrange: 准备测试数据
        MtFieldDependence mockMtFieldDependence = OptionsMockFactory.createMockFieldDependence(
                DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
        
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(mockObjectDescribe);
        when(fieldDependenceLogicService.find(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME))
                .thenReturn(Optional.of(mockMtFieldDependence));
        
        // Mock SelectFieldDependence.verifyAndFindOne
        try (MockedStatic<SelectFieldDependence> mockedSelectFieldDependence = mockStatic(SelectFieldDependence.class)) {
            SelectFieldDependence expectedResult = testSelectFieldDependence;
            mockedSelectFieldDependence.when(() -> SelectFieldDependence.verifyAndFindOne(
                    eq(mockObjectDescribe), eq(PARENT_FIELD_API_NAME), eq(CHILD_FIELD_API_NAME), any()))
                    .thenReturn(expectedResult);
            
            // Act: 执行被测试方法
            Optional<SelectFieldDependence> result = selectFieldDependenceLogicService
                    .find(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
            
            // Assert: 验证结果
            assertTrue(result.isPresent(), "查询结果应该存在");
            assertEquals(expectedResult, result.get(), "查询结果应该匹配");
            
            // 验证Mock交互
            verify(describeLogicService, times(1))
                    .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试find方法的空结果场景
     */
    @Test
    @DisplayName("测试find方法 - 空结果场景")
    void testFind_NotFound() {
        // Arrange: 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(mockObjectDescribe);
        when(fieldDependenceLogicService.find(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME))
                .thenReturn(Optional.empty());

        // Mock SelectFieldDependence.verifyAndFindOne返回null
        try (MockedStatic<SelectFieldDependence> mockedSelectFieldDependence = mockStatic(SelectFieldDependence.class)) {
            mockedSelectFieldDependence.when(() -> SelectFieldDependence.verifyAndFindOne(
                    eq(mockObjectDescribe), eq(PARENT_FIELD_API_NAME), eq(CHILD_FIELD_API_NAME), any()))
                    .thenReturn(null);

            // Act: 执行被测试方法
            Optional<SelectFieldDependence> result = selectFieldDependenceLogicService
                    .find(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);

            // Assert: 验证结果
            assertFalse(result.isPresent(), "查询结果应该为空");

            // 验证Mock交互
            verify(describeLogicService, times(1))
                    .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试create方法的正常场景
     */
    @Test
    @DisplayName("测试create方法 - 正常场景")
    void testCreate_Success() {
        // Arrange: 准备测试数据
        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectDescribe);

        // Mock ObjectDescribeExt.of
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isDownstreamTenantWithPublicObject()).thenReturn(false);

            // Mock SelectFieldDependence.convert2Create
            SelectFieldDependence.ConvertResultInfo mockConvertInfo = mock(SelectFieldDependence.ConvertResultInfo.class);
            when(testSelectFieldDependence.convert2Create(mockObjectDescribe)).thenReturn(mockConvertInfo);
            when(mockConvertInfo.getFieldDescribes()).thenReturn(Arrays.asList(mockParentField, mockChildField));
            when(mockConvertInfo.getFieldDependence()).thenReturn(Optional.empty());

            // Mock UdobjGrayConfig
            try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
                mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(anyString(), anyString())).thenReturn(false);

                // Act: 执行被测试方法
                assertDoesNotThrow(() -> {
                    selectFieldDependenceLogicService.create(testUser, testSelectFieldDependence);
                }, "创建字段依赖不应抛出异常");

                // 验证Mock交互
                verify(distributedLockService, times(1))
                        .advisoryTransactionalLock(eq(TENANT_ID), anyString());
                verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
                verify(describeLogicService, times(1))
                        .updateFieldDescribe(eq(mockObjectDescribe), anyList());
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试create方法的公共对象异常场景
     */
    @Test
    @DisplayName("测试create方法 - 公共对象异常")
    void testCreate_ThrowsValidateExceptionForPublicObject() {
        // Arrange: 准备测试数据
        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectDescribe);

        // Mock ObjectDescribeExt.of返回下游租户的公共对象
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isDownstreamTenantWithPublicObject()).thenReturn(true);

            // Act & Assert: 验证异常抛出
            assertThrows(ValidateException.class, () -> {
                selectFieldDependenceLogicService.create(testUser, testSelectFieldDependence);
            }, "公共对象不支持修改字段依赖应该抛出ValidateException");

            // 验证Mock交互
            verify(distributedLockService, times(1))
                    .advisoryTransactionalLock(eq(TENANT_ID), anyString());
            verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试copy方法的正常场景
     */
    @Test
    @DisplayName("测试copy方法 - 正常场景")
    void testCopy_Success() {
        // Arrange: 准备测试数据
        SelectFieldDependence.ConvertResultInfo mockConvertInfo = mock(SelectFieldDependence.ConvertResultInfo.class);
        when(testSelectFieldDependence.convert2Copy(mockObjectDescribe)).thenReturn(mockConvertInfo);
        when(mockConvertInfo.getFieldDescribes()).thenReturn(Arrays.asList(mockParentField, mockChildField));
        when(mockConvertInfo.getFieldDependence()).thenReturn(Optional.empty());

        // Act: 执行被测试方法
        assertDoesNotThrow(() -> {
            selectFieldDependenceLogicService.copy(testUser, mockObjectDescribe, testSelectFieldDependence);
        }, "复制字段依赖不应抛出异常");

        // 验证Mock交互
        verify(describeLogicService, times(1))
                .updateFieldDescribe(eq(mockObjectDescribe), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试update方法的正常场景
     */
    @Test
    @DisplayName("测试update方法 - 正常场景")
    void testUpdate_Success() {
        // Arrange: 准备测试数据
        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectDescribe);

        // Mock ObjectDescribeExt.of
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isDownstreamTenantWithPublicObject()).thenReturn(false);

            // Mock SelectFieldDependence.convert2Update
            SelectFieldDependence.ConvertResultInfo mockConvertInfo = mock(SelectFieldDependence.ConvertResultInfo.class);
            when(testSelectFieldDependence.convert2Update(mockObjectDescribe)).thenReturn(mockConvertInfo);
            when(mockConvertInfo.getFieldDescribes()).thenReturn(Arrays.asList(mockParentField, mockChildField));

            MtFieldDependence mockFieldDependence = OptionsMockFactory.createMockFieldDependence(
                    DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
            when(mockConvertInfo.getFieldDependence()).thenReturn(Optional.of(mockFieldDependence));

            // Mock UdobjGrayConfig
            try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
                mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(anyString(), anyString())).thenReturn(false);

                // Act: 执行被测试方法
                assertDoesNotThrow(() -> {
                    selectFieldDependenceLogicService.update(testUser, testSelectFieldDependence);
                }, "更新字段依赖不应抛出异常");

                // 验证Mock交互
                verify(distributedLockService, times(1))
                        .advisoryTransactionalLock(eq(TENANT_ID), anyString());
                verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
                verify(describeLogicService, times(1))
                        .updateFieldDescribe(eq(mockObjectDescribe), anyList());
                verify(fieldDependenceLogicService, times(1)).update(testUser, mockFieldDependence);
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleted方法的正常场景
     */
    @Test
    @DisplayName("测试deleted方法 - 正常场景")
    void testDeleted_Success() {
        // Arrange: 准备测试数据
        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectDescribe);

        // Mock ObjectDescribeExt.of
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isDownstreamTenantWithPublicObject()).thenReturn(false);

            // Mock SelectFieldDependence.convert2Deleted
            try (MockedStatic<SelectFieldDependence> mockedSelectFieldDependence = mockStatic(SelectFieldDependence.class)) {
                SelectFieldDependence.ConvertResultInfo mockConvertInfo = mock(SelectFieldDependence.ConvertResultInfo.class);
                mockedSelectFieldDependence.when(() -> SelectFieldDependence.convert2Deleted(
                        mockObjectDescribe, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME))
                        .thenReturn(mockConvertInfo);
                when(mockConvertInfo.getFieldDescribes()).thenReturn(Arrays.asList(mockParentField, mockChildField));

                MtFieldDependence mockFieldDependence = OptionsMockFactory.createMockFieldDependence(
                        DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
                when(mockConvertInfo.getFieldDependence()).thenReturn(Optional.of(mockFieldDependence));

                // Mock UdobjGrayConfig
                try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
                    mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(anyString(), anyString())).thenReturn(false);

                    // Act: 执行被测试方法
                    assertDoesNotThrow(() -> {
                        selectFieldDependenceLogicService.deleted(testUser, DESCRIBE_API_NAME,
                                PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
                    }, "删除字段依赖不应抛出异常");

                    // 验证Mock交互
                    verify(distributedLockService, times(1))
                            .advisoryTransactionalLock(eq(TENANT_ID), anyString());
                    verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
                    verify(describeLogicService, times(1))
                            .updateFieldDescribe(eq(mockObjectDescribe), anyList());
                    verify(fieldDependenceLogicService, times(1))
                            .deleted(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
                }
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findChildFields方法的正常场景
     */
    @Test
    @DisplayName("测试findChildFields方法 - 正常场景")
    void testFindChildFields_Success() {
        // Arrange: 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(mockObjectDescribe);

        // Mock ObjectDescribeExt
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockDescribeExt);

            when(mockDescribeExt.getFieldDescribeSilently(PARENT_FIELD_API_NAME))
                    .thenReturn(Optional.of(mockParentField));
            when(mockDescribeExt.stream()).thenReturn(Arrays.asList(mockChildField).stream());

            // Mock FieldDescribeExt
            try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
                FieldDescribeExt mockParentFieldExt = mock(FieldDescribeExt.class);
                FieldDescribeExt mockChildFieldExt = mock(FieldDescribeExt.class);

                mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockParentField)).thenReturn(mockParentFieldExt);
                mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockChildField)).thenReturn(mockChildFieldExt);

                when(mockParentFieldExt.isCascadeParentField()).thenReturn(true);
                when(mockChildFieldExt.isCascadeChildField()).thenReturn(true);
                when(mockChildFieldExt.getFieldDescribe()).thenReturn(mockSelectOne);

                // Act: 执行被测试方法
                List<SelectOne> result = selectFieldDependenceLogicService
                        .findChildFields(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME);

                // Assert: 验证结果
                assertNotNull(result, "查询结果不应为null");
                assertEquals(1, result.size(), "应该返回1个子字段");
                assertEquals(mockSelectOne, result.get(0), "返回的子字段应该匹配");

                // 验证Mock交互
                verify(describeLogicService, times(1))
                        .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findChildFields方法的异常场景，验证父字段不存在时抛出异常
     */
    @Test
    @DisplayName("测试findChildFields方法 - 父字段不存在异常")
    void testFindChildFields_ThrowsValidateExceptionWhenParentFieldNotFound() {
        // Arrange: 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(mockObjectDescribe);

        // Mock ObjectDescribeExt
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockDescribeExt);

            // 配置父字段不存在
            when(mockDescribeExt.getFieldDescribeSilently(PARENT_FIELD_API_NAME))
                    .thenReturn(Optional.empty());

            // Act & Assert: 验证异常抛出
            assertThrows(ValidateException.class, () -> {
                selectFieldDependenceLogicService.findChildFields(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME);
            }, "父字段不存在应该抛出ValidateException");

            // 验证Mock交互
            verify(describeLogicService, times(1))
                    .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findAll方法的正常场景
     */
    @Test
    @DisplayName("测试findAll方法 - 正常场景")
    void testFindAll_Success() {
        // Arrange: 准备测试数据

        // Mock UdobjGrayConfig - 禁用缓存
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(anyString(), anyString())).thenReturn(false);

            // 由于缓存被禁用，会直接调用findAllInDB方法
            // 我们需要Mock findAllInDB的逻辑
            when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                    .thenReturn(mockObjectDescribe);

            MtFieldDependence mockMtFieldDependence = OptionsMockFactory.createMockFieldDependence(
                    DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
            when(fieldDependenceLogicService.findAll(testUser, DESCRIBE_API_NAME))
                    .thenReturn(Arrays.asList(mockMtFieldDependence));

            // Mock ObjectDescribeExt和相关逻辑
            try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
                ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
                mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe)).thenReturn(mockDescribeExt);
                when(mockDescribeExt.getSelectOneFields()).thenReturn(Arrays.asList(mockSelectOne));
                when(mockDescribeExt.getFieldDescribeSilently(PARENT_FIELD_API_NAME))
                        .thenReturn(Optional.of(mockParentField));

                // Mock FieldDescribeExt
                try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
                    FieldDescribeExt mockParentFieldExt = mock(FieldDescribeExt.class);
                    FieldDescribeExt mockChildFieldExt = mock(FieldDescribeExt.class);

                    mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockParentField)).thenReturn(mockParentFieldExt);
                    mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockSelectOne)).thenReturn(mockChildFieldExt);

                    when(mockParentFieldExt.isCascadeParentField()).thenReturn(true);
                    when(mockParentFieldExt.isGeneralOptions()).thenReturn(true);
                    when(mockParentFieldExt.getApiName()).thenReturn(PARENT_FIELD_API_NAME);
                    when(mockChildFieldExt.isGeneralOptions()).thenReturn(false);

                    // Mock SelectFieldDependence.from
                    try (MockedStatic<SelectFieldDependence> mockedSelectFieldDependence = mockStatic(SelectFieldDependence.class)) {
                        mockedSelectFieldDependence.when(() -> SelectFieldDependence.from(mockMtFieldDependence))
                                .thenReturn(testSelectFieldDependence);

                        // Act: 执行被测试方法
                        List<SelectFieldDependence> result = selectFieldDependenceLogicService
                                .findAll(testUser, DESCRIBE_API_NAME);

                        // Assert: 验证结果
                        assertNotNull(result, "查询结果不应为null");
                        assertEquals(1, result.size(), "应该返回1个字段依赖");

                        // 验证Mock交互
                        verify(describeLogicService, times(1))
                                .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
                        verify(fieldDependenceLogicService, times(1)).findAll(testUser, DESCRIBE_API_NAME);
                    }
                }
            }
        }
    }
}
