package com.facishare.paas.appframework.metadata.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VelocityUtil工具类的单元测试
 * 测试Velocity模板引擎的占位符替换功能
 */
@ExtendWith(MockitoExtension.class)
class VelocityUtilTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符的正常场景
     */
    @Test
    @DisplayName("正常场景 - 替换占位符成功")
    void testReplacePlaceholder_Success() {
        // 准备测试数据
        String template = "Hello ${name}, welcome to ${company}!";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "<PERSON>");
        contextData.put("company", "Facishare");

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果
        assertEquals("Hello John, welcome to Facishare!", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，模板包含多个相同占位符的场景
     */
    @Test
    @DisplayName("正常场景 - 替换占位符时模板包含多个相同占位符")
    void testReplacePlaceholder_MultipleIdenticalPlaceholders() {
        // 准备测试数据
        String template = "${name} says hello to ${name} again!";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "Alice");

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果
        assertEquals("Alice says hello to Alice again!", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，上下文数据不存在对应键的场景
     */
    @Test
    @DisplayName("正常场景 - 替换占位符时上下文数据不存在对应键显示为空串")
    void testReplacePlaceholder_MissingKey() {
        // 准备测试数据
        String template = "Hello ${name}, your age is ${age}";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "Bob");
        // 注意：没有提供age键

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果 - 缺失的键应该显示为空串（因为代码中将${替换为$!{）
        assertEquals("Hello Bob, your age is ", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，模板为空的场景
     */
    @Test
    @DisplayName("边界场景 - 替换占位符时模板为空返回空串")
    void testReplacePlaceholder_EmptyTemplate() {
        // 准备测试数据
        String template = "";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "Test");

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果
        assertEquals("", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，模板为null的场景
     */
    @Test
    @DisplayName("边界场景 - 替换占位符时模板为null返回空串")
    void testReplacePlaceholder_NullTemplate() {
        // 准备测试数据
        String template = null;
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "Test");

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果
        assertEquals("", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，模板为空白字符的场景
     */
    @Test
    @DisplayName("边界场景 - 替换占位符时模板为空白字符返回空串")
    void testReplacePlaceholder_BlankTemplate() {
        // 准备测试数据
        String template = "   ";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "Test");

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果
        assertEquals("", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，上下文数据为空的场景
     */
    @Test
    @DisplayName("边界场景 - 替换占位符时上下文数据为空")
    void testReplacePlaceholder_EmptyContextData() {
        // 准备测试数据
        String template = "Hello ${name}!";
        Map<String, Object> contextData = Collections.emptyMap();

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果 - 缺失的键应该显示为空串
        assertEquals("Hello !", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，上下文数据为null的场景
     */
    @Test
    @DisplayName("边界场景 - 替换占位符时上下文数据为null")
    void testReplacePlaceholder_NullContextData() {
        // 准备测试数据
        String template = "Hello ${name}!";
        Map<String, Object> contextData = null;

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果 - 缺失的键应该显示为空串
        assertEquals("Hello !", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，模板不包含占位符的场景
     */
    @Test
    @DisplayName("正常场景 - 替换占位符时模板不包含占位符返回原模板")
    void testReplacePlaceholder_NoPlaceholders() {
        // 准备测试数据
        String template = "This is a simple text without placeholders.";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "Test");

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果
        assertEquals("This is a simple text without placeholders.", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，上下文数据包含不同类型值的场景
     */
    @Test
    @DisplayName("正常场景 - 替换占位符时上下文数据包含不同类型值")
    void testReplacePlaceholder_DifferentValueTypes() {
        // 准备测试数据
        String template = "Name: ${name}, Age: ${age}, Active: ${active}, Score: ${score}";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "John");
        contextData.put("age", 25);
        contextData.put("active", true);
        contextData.put("score", 95.5);

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果
        assertEquals("Name: John, Age: 25, Active: true, Score: 95.5", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，上下文数据包含null值的场景
     */
    @Test
    @DisplayName("正常场景 - 替换占位符时上下文数据包含null值")
    void testReplacePlaceholder_NullValues() {
        // 准备测试数据
        String template = "Name: ${name}, Description: ${description}";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "Test");
        contextData.put("description", null);

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果 - null值应该显示为空串（因为使用了$!{语法）
        assertEquals("Name: Test, Description: ", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，模板包含特殊字符的场景
     */
    @Test
    @DisplayName("正常场景 - 替换占位符时模板包含特殊字符")
    void testReplacePlaceholder_SpecialCharacters() {
        // 准备测试数据
        String template = "Email: ${email}, Path: ${path}, Symbol: ${symbol}";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("email", "<EMAIL>");
        contextData.put("path", "/home/<USER>/documents");
        contextData.put("symbol", "©®™");

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果
        assertEquals("Email: <EMAIL>, Path: /home/<USER>/documents, Symbol: ©®™", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，模板包含中文字符的场景
     */
    @Test
    @DisplayName("正常场景 - 替换占位符时模板包含中文字符")
    void testReplacePlaceholder_ChineseCharacters() {
        // 准备测试数据
        String template = "姓名：${name}，公司：${company}，职位：${position}";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "张三");
        contextData.put("company", "纷享销客");
        contextData.put("position", "软件工程师");

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果
        assertEquals("姓名：张三，公司：纷享销客，职位：软件工程师", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试替换占位符时，模板包含嵌套结构的场景
     */
    @Test
    @DisplayName("正常场景 - 替换占位符时模板包含复杂结构")
    void testReplacePlaceholder_ComplexTemplate() {
        // 准备测试数据
        String template = "User ${user.name} (ID: ${user.id}) works at ${company.name} in ${company.location}";
        Map<String, Object> contextData = new HashMap<>();
        
        // 创建嵌套对象
        Map<String, Object> user = new HashMap<>();
        user.put("name", "Alice");
        user.put("id", "12345");
        
        Map<String, Object> company = new HashMap<>();
        company.put("name", "TechCorp");
        company.put("location", "Beijing");
        
        contextData.put("user", user);
        contextData.put("company", company);

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果 - Velocity支持点号访问嵌套属性
        assertEquals("User Alice (ID: 12345) works at TechCorp in Beijing", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试${}到$!{}的转换功能
     */
    @Test
    @DisplayName("功能验证 - 验证${}到$!{}的转换功能")
    void testPlaceholderConversion() {
        // 准备测试数据 - 故意使用${}语法
        String template = "Hello ${name}, your score is ${score}";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "Test");
        // 故意不提供score键

        // 执行测试
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // 验证结果 - 缺失的键应该显示为空串而不是${score}
        assertEquals("Hello Test, your score is ", result);
        assertFalse(result.contains("${"));
        assertFalse(result.contains("}"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试方法的不可变性
     */
    @Test
    @DisplayName("不可变性验证 - 验证相同输入产生相同输出")
    void testMethodImmutability() {
        // 准备测试数据
        String template = "Hello ${name}!";
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("name", "World");

        // 多次调用并验证结果一致
        String result1 = VelocityUtil.replacePlaceholder(template, contextData);
        String result2 = VelocityUtil.replacePlaceholder(template, contextData);
        String result3 = VelocityUtil.replacePlaceholder(template, contextData);

        assertEquals(result1, result2);
        assertEquals(result2, result3);
        assertEquals("Hello World!", result1);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试静态方法的线程安全性（基本验证）
     */
    @Test
    @DisplayName("线程安全验证 - 基本的并发调用测试")
    void testThreadSafety() {
        // 准备测试数据
        String template = "Thread ${threadName} processed ${count} items";
        
        // 模拟并发调用
        Runnable task = () -> {
            Map<String, Object> contextData = new HashMap<>();
            contextData.put("threadName", Thread.currentThread().getName());
            contextData.put("count", 100);
            
            String result = VelocityUtil.replacePlaceholder(template, contextData);
            assertNotNull(result);
            assertTrue(result.contains("Thread"));
            assertTrue(result.contains("processed 100 items"));
        };

        // 创建多个线程执行任务
        Thread thread1 = new Thread(task, "TestThread1");
        Thread thread2 = new Thread(task, "TestThread2");
        
        thread1.start();
        thread2.start();
        
        // 等待线程完成
        try {
            thread1.join();
            thread2.join();
        } catch (InterruptedException e) {
            fail("Thread interrupted: " + e.getMessage());
        }
    }
}
