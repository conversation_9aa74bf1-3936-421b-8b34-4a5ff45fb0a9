package com.facishare.paas.appframework.metadata.publicobject.verify;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectEnterpriseRelationService;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectGrayConfig;
import com.facishare.paas.appframework.metadata.publicobject.module.*;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectTestBase;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraphService;
import com.facishare.paas.appframework.metadata.relation.ObjectRelationGraph;
import com.facishare.paas.appframework.metadata.relation.ObjectRelationGraphBuilder;
import com.facishare.paas.appframework.metadata.relation.ObjectRelationGraphService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.MetadataTransactionService;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AbstractPublicObjectJobVerify的JUnit5测试类
 * 
 * GenerateByAI
 * 
 * 测试目标：
 * - 覆盖AbstractPublicObjectJobVerify抽象基类的所有public和protected方法
 * - 测试模板方法模式的执行流程
 * - 验证复杂的验证逻辑和依赖关系校验
 * - 确保Mock配置正确，达到80%以上覆盖率
 * 
 * 测试策略：
 * - 创建具体的测试实现类来测试抽象方法
 * - 使用AAA模式组织测试代码
 * - 覆盖正常流程、异常流程、边界条件
 * - 重点测试通用验证逻辑和模板方法模式
 * 
 * 覆盖率目标：80%以上
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AbstractPublicObjectJobVerify JUnit5 测试")
class AbstractPublicObjectJobVerifyTest extends PublicObjectTestBase {

    // 创建测试用的具体实现类
    private TestablePublicObjectJobVerify testableVerify;

    // 额外的Mock对象
    @Mock
    private PublicObjectEnterpriseRelationService mockPublicObjectEnterpriseRelationService;
    @Mock
    private MetadataTransactionService mockMetadataTransactionService;
    @Mock
    private ObjectRelationGraphService mockObjectRelationGraphService;
    @Mock
    private FieldRelationGraphService mockFieldRelationGraphService;
    @Mock
    private PublicObjectFieldDescribeVerifyManager mockPublicObjectFieldDescribeVerifyManager;
    @Mock
    private PublicObjectFieldDescribeVerify mockPublicObjectFieldDescribeVerify;

    private PublicObjectJobParamVerifyInfo testJobParamVerifyInfo;
    private ObjectRelationGraph mockObjectRelationGraph;
    private ObjectRelationGraphBuilder.ObjectNode mockObjectNode;

    /**
     * 测试前的特定设置
     */
    @Override
    protected void specificSetUp() {
        // 创建测试用的具体实现类
        testableVerify = new TestablePublicObjectJobVerify();
        
        // 手动注入Mock依赖
        injectMockDependencies();
        
        // 创建测试用的业务对象
        testJobParamVerifyInfo = createTestJobParamVerifyInfo();
        mockObjectRelationGraph = mock(ObjectRelationGraph.class);
        mockObjectNode = mock(ObjectRelationGraphBuilder.ObjectNode.class);
        
        // 配置常用的Mock行为
        setupCommonMockBehaviors();
    }

    /**
     * 手动注入Mock依赖到测试实现类
     */
    private void injectMockDependencies() {
        testableVerify.publicObjectEnterpriseRelationService = mockPublicObjectEnterpriseRelationService;
        testableVerify.metadataTransactionService = mockMetadataTransactionService;
        testableVerify.describeLogicService = mockDescribeLogicService;
        testableVerify.objectRelationGraphService = mockObjectRelationGraphService;
        testableVerify.fieldRelationGraphService = mockFieldRelationGraphService;
        testableVerify.publicObjectFieldDescribeVerifyManager = mockPublicObjectFieldDescribeVerifyManager;
        testableVerify.enterpriseEditionService = mockEnterpriseEditionService;
    }

    /**
     * 配置常用的Mock行为
     */
    private void setupCommonMockBehaviors() {
        // 配置对象描述的基本属性
        when(mockObjectDescribe.isPublicObject()).thenReturn(true);
        when(mockObjectDescribe.getDisplayName()).thenReturn("测试对象");
        when(mockObjectDescribe.getApiName()).thenReturn(TEST_OBJECT_API_NAME);
        when(mockObjectDescribe.getTenantId()).thenReturn(TEST_TENANT_ID);
        when(mockObjectDescribe.getUpstreamTenantId()).thenReturn(TEST_UPSTREAM_TENANT_ID);
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList());
        
        // 配置MetadataTransactionService
        try {
            when(mockMetadataTransactionService.executeWithOutMetadataTransaction(any()))
                    .thenAnswer(invocation -> {
                        try {
                            return ((java.util.concurrent.Callable<?>) invocation.getArgument(0)).call();
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            // 处理Mock配置异常
            System.err.println("Warning: Failed to setup MetadataTransactionService mock: " + e.getMessage());
        }
        
        // 配置ObjectRelationGraphService
        when(mockObjectRelationGraphService.buildSimpleObjectRelationNetwork(any(IObjectDescribe.class)))
                .thenReturn(mockObjectRelationGraph);
        when(mockObjectRelationGraph.getNode(anyString())).thenReturn(Optional.of(mockObjectNode));
        when(mockObjectRelationGraph.getDescribeMap()).thenReturn(Maps.newHashMap());
        when(mockObjectRelationGraph.inEdges(any())).thenReturn(Sets.newHashSet());
        when(mockObjectRelationGraph.outEdges(any())).thenReturn(Sets.newHashSet());
        
        // 配置PublicObjectFieldDescribeVerifyManager
        when(mockPublicObjectFieldDescribeVerifyManager.getPublicObjectFieldDescribeVerify(anyString()))
                .thenReturn(mockPublicObjectFieldDescribeVerify);
        when(mockPublicObjectFieldDescribeVerify.verify(any(), any()))
                .thenReturn(VerifyResult.buildEmpty());
        
        // 配置企业版本服务
        GetSimpleEnterpriseDataResult mockEnterpriseResult = mock(GetSimpleEnterpriseDataResult.class);
        SimpleEnterpriseData mockEnterpriseData = mock(SimpleEnterpriseData.class);
        when(mockEnterpriseData.getEnterpriseName()).thenReturn("测试企业");
        when(mockEnterpriseResult.getEnterpriseData()).thenReturn(mockEnterpriseData);
        when(mockEnterpriseEditionService.getSimpleEnterpriseData(any(GetSimpleEnterpriseDataArg.class)))
                .thenReturn(mockEnterpriseResult);
        
        // 配置企业关系服务
        when(mockPublicObjectEnterpriseRelationService.findEnterpriseRelationByDownstreamTenantIds(any(User.class), anyList()))
                .thenReturn(Lists.newArrayList());
        when(mockPublicObjectEnterpriseRelationService.findCustodyMangeEnterpriseRelationByTenantGroup(any(User.class), anyList()))
                .thenReturn(Lists.newArrayList());
    }

    // ==================== verifyByJob方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试根据任务验证 - 上游租户场景，验证成功
     */
    @Test
    @DisplayName("正常场景 - 上游租户验证成功")
    void testVerifyByJob_UpstreamTenant_Success() {
        // Arrange
        when(testJobParamVerifyInfo.getUpstreamTenantId()).thenReturn(TEST_TENANT_ID);
        when(mockObjectDescribe.isPublicObject()).thenReturn(true);

        // Act
        PublicObjectJobVerifyResult result = testableVerify.verifyByJob(mockUser, mockObjectDescribe, testJobParamVerifyInfo);

        // Assert
        assertNotNull(result);
        assertTrue(result.success());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据任务验证 - 下游租户场景，验证成功
     */
    @Test
    @DisplayName("正常场景 - 下游租户验证成功")
    void testVerifyByJob_DownstreamTenant_Success() {
        // Arrange
        when(testJobParamVerifyInfo.getUpstreamTenantId()).thenReturn(TEST_UPSTREAM_TENANT_ID);
        when(mockObjectDescribe.isPublicObject()).thenReturn(false);
        
        // Mock下游验证所需的对象
        when(mockDescribeLogicService.findObject(anyString(), anyString())).thenReturn(mockObjectDescribe);

        // Act
        PublicObjectJobVerifyResult result = testableVerify.verifyByJob(mockUser, mockObjectDescribe, testJobParamVerifyInfo);

        // Assert
        assertNotNull(result);
        assertTrue(result.success());
    }

    // ==================== verifyDescribeWithUpstream方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试上游描述验证 - 对象未开启公共对象，验证失败
     */
    @Test
    @DisplayName("异常场景 - 对象未开启公共对象")
    void testVerifyDescribeWithUpstream_NotPublicObject() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(false);

        // Act
        VerifyResult result = testableVerify.verifyDescribeWithUpstream(mockUser, mockObjectDescribe);

        // Assert
        assertNotNull(result);
        assertFalse(result.success());
        assertFalse(result.getMessages().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试上游描述验证 - 对象已开启公共对象，验证成功
     */
    @Test
    @DisplayName("正常场景 - 对象已开启公共对象")
    void testVerifyDescribeWithUpstream_PublicObject() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(true);

        // Act
        VerifyResult result = testableVerify.verifyDescribeWithUpstream(mockUser, mockObjectDescribe);

        // Assert
        assertNotNull(result);
        assertTrue(result.success());
        assertTrue(result.getMessages().isEmpty());
    }

    // ==================== verifyByMasterDetail方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试主从关系验证 - 非公共对象且非从对象，验证成功
     */
    @Test
    @DisplayName("正常场景 - 非公共对象且非从对象")
    void testVerifyByMasterDetail_NotPublicNotSlave_Success() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(false);
        
        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(false);

            // Act
            VerifyResult result = testableVerify.verifyByMasterDetail(mockUser, mockObjectDescribe);

            // Assert
            assertNotNull(result);
            assertTrue(result.success());
        }
    }

    /**
     * 创建测试用的具体实现类
     */
    private static class TestablePublicObjectJobVerify extends AbstractPublicObjectJobVerify {
        // 暴露protected字段以便测试注入
        public PublicObjectEnterpriseRelationService publicObjectEnterpriseRelationService;
        public MetadataTransactionService metadataTransactionService;
        public DescribeLogicService describeLogicService;
        public ObjectRelationGraphService objectRelationGraphService;
        public FieldRelationGraphService fieldRelationGraphService;
        public PublicObjectFieldDescribeVerifyManager publicObjectFieldDescribeVerifyManager;
        public com.facishare.uc.api.service.EnterpriseEditionService enterpriseEditionService;

        @Override
        protected VerifyResult initAndVerify(User user, IObjectDescribe describe, PublicObjectJobParamVerifyInfo jobParam) {
            return VerifyResult.buildEmpty();
        }

        @Override
        protected VerifyResult verifyByJobParam(User user, PublicObjectJobParamVerifyInfo jobParam) {
            return VerifyResult.buildEmpty();
        }

        @Override
        public List<PublicObjectJobType> getSupportedJobTypes() {
            return Lists.newArrayList(PublicObjectJobType.OPEN_JOB);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试主从关系验证 - 公共对象且为从对象，验证失败
     */
    @Test
    @DisplayName("异常场景 - 公共对象且为从对象")
    void testVerifyByMasterDetail_PublicSlaveObject() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(true);

        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(true);

            // Act
            VerifyResult result = testableVerify.verifyByMasterDetail(mockUser, mockObjectDescribe);

            // Assert
            assertNotNull(result);
            assertFalse(result.success());
            assertFalse(result.getMessages().isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试主从关系验证 - 公共对象且为主对象，验证失败
     */
    @Test
    @DisplayName("异常场景 - 公共对象且为主对象")
    void testVerifyByMasterDetail_PublicMasterObject() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(true);
        when(mockDescribeLogicService.isMasterObject(anyString(), anyString())).thenReturn(true);

        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(false);

            // Act
            VerifyResult result = testableVerify.verifyByMasterDetail(mockUser, mockObjectDescribe);

            // Assert
            assertNotNull(result);
            assertFalse(result.success());
            assertFalse(result.getMessages().isEmpty());
        }
    }

    // ==================== verifyByObjectRelation方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试对象关系验证 - 非公共对象，验证成功
     */
    @Test
    @DisplayName("正常场景 - 非公共对象关系验证成功")
    void testVerifyByObjectRelation_NotPublicObject_Success() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(false);

        // Act
        VerifyResult result = testableVerify.verifyByObjectRelation(mockUser, mockObjectDescribe, TEST_UPSTREAM_TENANT_ID);

        // Assert
        assertNotNull(result);
        assertTrue(result.success());

        verify(mockObjectRelationGraphService).buildSimpleObjectRelationNetwork(mockObjectDescribe);
        verify(mockObjectRelationGraph).getNode(TEST_OBJECT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象关系验证 - 公共对象，验证成功
     */
    @Test
    @DisplayName("正常场景 - 公共对象关系验证成功")
    void testVerifyByObjectRelation_PublicObject_Success() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(true);

        // Act
        VerifyResult result = testableVerify.verifyByObjectRelation(mockUser, mockObjectDescribe, TEST_UPSTREAM_TENANT_ID);

        // Assert
        assertNotNull(result);
        assertTrue(result.success());

        verify(mockObjectRelationGraphService).buildSimpleObjectRelationNetwork(mockObjectDescribe);
        verify(mockObjectRelationGraph).getNode(TEST_OBJECT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象关系验证 - 对象节点不存在，抛出异常
     */
    @Test
    @DisplayName("异常场景 - 对象节点不存在")
    void testVerifyByObjectRelation_ObjectNodeNotFound() {
        // Arrange
        when(mockObjectRelationGraph.getNode(anyString())).thenReturn(Optional.empty());

        // Act & Assert
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            testableVerify.verifyByObjectRelation(mockUser, mockObjectDescribe, TEST_UPSTREAM_TENANT_ID);
        });

        assertTrue(exception.getMessage().contains("PARAM_ERROR"));
        verify(mockObjectRelationGraphService).buildSimpleObjectRelationNetwork(mockObjectDescribe);
    }

    // ==================== verifyByDesignerResourceResult方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试设计器资源验证 - 正常场景，验证成功
     */
    @Test
    @DisplayName("正常场景 - 设计器资源验证成功")
    void testVerifyByDesignerResourceResult_Success() {
        // Arrange
        DesignerResourceResult designerResource = mock(DesignerResourceResult.class);
        IFieldDescribe mockField = mock(IFieldDescribe.class);
        when(mockField.getApiName()).thenReturn("test_field");
        when(mockField.getType()).thenReturn("text");
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList(mockField));

        try (MockedStatic<PublicObjectGrayConfig> mockedGrayConfig = mockStatic(PublicObjectGrayConfig.class)) {
            mockedGrayConfig.when(() -> PublicObjectGrayConfig.isPublicObjectVerifyWhiteField(anyString(), anyString()))
                    .thenReturn(false);

            // Act
            VerifyResult result = testableVerify.verifyByDesignerResourceResult(mockUser, mockObjectDescribe, designerResource);

            // Assert
            assertNotNull(result);
            assertTrue(result.success());

            verify(mockPublicObjectFieldDescribeVerifyManager).getPublicObjectFieldDescribeVerify("text");
            verify(mockPublicObjectFieldDescribeVerify).verify(any(), eq(mockField));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设计器资源验证 - 白名单字段跳过验证
     */
    @Test
    @DisplayName("边界场景 - 白名单字段跳过验证")
    void testVerifyByDesignerResourceResult_WhiteFieldSkipped() {
        // Arrange
        DesignerResourceResult designerResource = mock(DesignerResourceResult.class);
        IFieldDescribe mockField = mock(IFieldDescribe.class);
        when(mockField.getApiName()).thenReturn("white_field");
        when(mockField.getType()).thenReturn("text");
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList(mockField));

        try (MockedStatic<PublicObjectGrayConfig> mockedGrayConfig = mockStatic(PublicObjectGrayConfig.class)) {
            mockedGrayConfig.when(() -> PublicObjectGrayConfig.isPublicObjectVerifyWhiteField(anyString(), anyString()))
                    .thenReturn(true);

            // Act
            VerifyResult result = testableVerify.verifyByDesignerResourceResult(mockUser, mockObjectDescribe, designerResource);

            // Assert
            assertNotNull(result);
            assertTrue(result.success());

            verify(mockPublicObjectFieldDescribeVerifyManager, never()).getPublicObjectFieldDescribeVerify(anyString());
            verify(mockPublicObjectFieldDescribeVerify, never()).verify(any(), any());
        }
    }

    // ==================== verifyDescribeWithDownstream方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试下游描述验证 - 对象已是公共对象，验证失败
     */
    @Test
    @DisplayName("异常场景 - 对象已是公共对象")
    void testVerifyDescribeWithDownstream_AlreadyPublicObject() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(true);
        when(testJobParamVerifyInfo.getUpstreamTenantId()).thenReturn(TEST_UPSTREAM_TENANT_ID);

        // Act
        VerifyResult result = testableVerify.verifyDescribeWithDownstream(mockUser, mockObjectDescribe, testJobParamVerifyInfo);

        // Assert
        assertNotNull(result);
        assertFalse(result.success());
        assertFalse(result.getMessages().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试下游描述验证 - 同租户，验证成功
     */
    @Test
    @DisplayName("正常场景 - 同租户验证成功")
    void testVerifyDescribeWithDownstream_SameTenant_Success() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(false);
        when(testJobParamVerifyInfo.getUpstreamTenantId()).thenReturn(TEST_TENANT_ID);

        // Act
        VerifyResult result = testableVerify.verifyDescribeWithDownstream(mockUser, mockObjectDescribe, testJobParamVerifyInfo);

        // Assert
        assertNotNull(result);
        assertTrue(result.success());
        assertTrue(result.getMessages().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试下游描述验证 - 包含自定义字段，验证失败
     */
    @Test
    @DisplayName("异常场景 - 包含自定义字段")
    void testVerifyDescribeWithDownstream_HasCustomField() {
        // Arrange
        when(mockObjectDescribe.isPublicObject()).thenReturn(false);
        when(testJobParamVerifyInfo.getUpstreamTenantId()).thenReturn(TEST_UPSTREAM_TENANT_ID);

        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.stream()).thenReturn(Lists.newArrayList(mock(IFieldDescribe.class)).stream());
            when(mockDescribeExt.stream().anyMatch(any())).thenReturn(true);

            // Act
            VerifyResult result = testableVerify.verifyDescribeWithDownstream(mockUser, mockObjectDescribe, testJobParamVerifyInfo);

            // Assert
            assertNotNull(result);
            assertFalse(result.success());
            assertFalse(result.getMessages().isEmpty());
        }
    }

    // ==================== verifyWithDescribe方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试描述验证 - 旧描述为空，验证成功
     */
    @Test
    @DisplayName("正常场景 - 旧描述为空，验证成功")
    void testVerifyWithDescribe_OldDescribeNull_Success() {
        // Arrange
        IObjectDescribe newDescribe = mock(IObjectDescribe.class);
        when(newDescribe.isPublicObject()).thenReturn(false);

        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(false);

            // Act
            VerifyResult result = testableVerify.verifyWithDescribe(mockUser, newDescribe, null);

            // Assert
            assertNotNull(result);
            assertTrue(result.success());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试描述验证 - 旧描述非公共对象，验证成功
     */
    @Test
    @DisplayName("正常场景 - 旧描述非公共对象，验证成功")
    void testVerifyWithDescribe_OldDescribeNotPublic_Success() {
        // Arrange
        IObjectDescribe newDescribe = mock(IObjectDescribe.class);
        IObjectDescribe oldDescribe = mock(IObjectDescribe.class);
        when(newDescribe.isPublicObject()).thenReturn(false);
        when(oldDescribe.isPublicObject()).thenReturn(false);

        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(false);

            // Act
            VerifyResult result = testableVerify.verifyWithDescribe(mockUser, newDescribe, oldDescribe);

            // Assert
            assertNotNull(result);
            assertTrue(result.success());
        }
    }
}
