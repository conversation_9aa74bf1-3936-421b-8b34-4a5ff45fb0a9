package com.facishare.paas.appframework.metadata.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TranslateI18nUtils工具类的单元测试
 * 测试国际化翻译键值生成功能
 */
@ExtendWith(MockitoExtension.class)
class TranslateI18nUtilsTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试生成标签管理器标签组翻译键的正常场景
     */
    @Test
    @DisplayName("正常场景 - 生成标签管理器标签组翻译键成功")
    void testGetTransTagManagerTagGroup_Success() {
        // 准备测试数据
        String groupApiName = "testGroup";

        // 执行测试
        String result = TranslateI18nUtils.getTransTagManagerTagGroup(groupApiName);

        // 验证结果
        assertEquals(TranslateI18nUtils.TRANS_TAG_MANAGER_TAG_GROUP + groupApiName, result);
        assertEquals("trans_tag_manager_tag_group_testGroup", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成标签管理器标签组翻译键时，组名包含特殊字符的场景
     */
    @Test
    @DisplayName("正常场景 - 生成标签管理器标签组翻译键时组名包含特殊字符")
    void testGetTransTagManagerTagGroup_SpecialCharacters() {
        // 准备测试数据
        String groupApiName = "test-group_v1.0";

        // 执行测试
        String result = TranslateI18nUtils.getTransTagManagerTagGroup(groupApiName);

        // 验证结果
        assertEquals(TranslateI18nUtils.TRANS_TAG_MANAGER_TAG_GROUP + groupApiName, result);
        assertEquals("trans_tag_manager_tag_group_test-group_v1.0", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成标签管理器标签组翻译键时，组名为空的场景
     */
    @Test
    @DisplayName("边界场景 - 生成标签管理器标签组翻译键时组名为空")
    void testGetTransTagManagerTagGroup_EmptyGroupName() {
        // 准备测试数据
        String groupApiName = "";

        // 执行测试
        String result = TranslateI18nUtils.getTransTagManagerTagGroup(groupApiName);

        // 验证结果
        assertEquals(TranslateI18nUtils.TRANS_TAG_MANAGER_TAG_GROUP + "", result);
        assertEquals("trans_tag_manager_tag_group_", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成标签管理器标签组翻译键时，组名为null的场景
     */
    @Test
    @DisplayName("边界场景 - 生成标签管理器标签组翻译键时组名为null")
    void testGetTransTagManagerTagGroup_NullGroupName() {
        // 执行测试
        String result = TranslateI18nUtils.getTransTagManagerTagGroup(null);

        // 验证结果
        assertEquals(TranslateI18nUtils.TRANS_TAG_MANAGER_TAG_GROUP + "null", result);
        assertEquals("trans_tag_manager_tag_group_null", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成标签管理器子标签翻译键的正常场景
     */
    @Test
    @DisplayName("正常场景 - 生成标签管理器子标签翻译键成功")
    void testGetTransTagManagerSubTag_Success() {
        // 准备测试数据
        String tagApiName = "testTag";

        // 执行测试
        String result = TranslateI18nUtils.getTransTagManagerSubTag(tagApiName);

        // 验证结果
        assertEquals(TranslateI18nUtils.TRANS_TAG_MANAGER_SUB_TAG + tagApiName, result);
        assertEquals("trans_tag_manager_sub_tag_testTag", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成标签管理器子标签翻译键时，标签名包含数字的场景
     */
    @Test
    @DisplayName("正常场景 - 生成标签管理器子标签翻译键时标签名包含数字")
    void testGetTransTagManagerSubTag_WithNumbers() {
        // 准备测试数据
        String tagApiName = "tag123";

        // 执行测试
        String result = TranslateI18nUtils.getTransTagManagerSubTag(tagApiName);

        // 验证结果
        assertEquals(TranslateI18nUtils.TRANS_TAG_MANAGER_SUB_TAG + tagApiName, result);
        assertEquals("trans_tag_manager_sub_tag_tag123", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成标签管理器子标签翻译键时，标签名为空的场景
     */
    @Test
    @DisplayName("边界场景 - 生成标签管理器子标签翻译键时标签名为空")
    void testGetTransTagManagerSubTag_EmptyTagName() {
        // 准备测试数据
        String tagApiName = "";

        // 执行测试
        String result = TranslateI18nUtils.getTransTagManagerSubTag(tagApiName);

        // 验证结果
        assertEquals(TranslateI18nUtils.TRANS_TAG_MANAGER_SUB_TAG + "", result);
        assertEquals("trans_tag_manager_sub_tag_", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成标签管理器子标签翻译键时，标签名为null的场景
     */
    @Test
    @DisplayName("边界场景 - 生成标签管理器子标签翻译键时标签名为null")
    void testGetTransTagManagerSubTag_NullTagName() {
        // 执行测试
        String result = TranslateI18nUtils.getTransTagManagerSubTag(null);

        // 验证结果
        assertEquals(TranslateI18nUtils.TRANS_TAG_MANAGER_SUB_TAG + "null", result);
        assertEquals("trans_tag_manager_sub_tag_null", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成按钮参数键的正常场景
     */
    @Test
    @DisplayName("正常场景 - 生成按钮参数键成功")
    void testGetButtonParamKey_Success() {
        // 准备测试数据
        String tenantId = "tenant123";
        String objApi = "Account";
        String buttonApi = "createButton";
        String paramKey = "label";

        // 执行测试
        String result = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);

        // 验证结果
        String expected = TranslateI18nUtils.NEW_BUTTON_FIELD + "." + tenantId + "." + objApi + "." + buttonApi + "." + paramKey;
        assertEquals(expected, result);
        assertEquals("ButtonFormVariable.tenant123.Account.createButton.label", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成按钮参数键时，参数包含特殊字符的场景
     */
    @Test
    @DisplayName("正常场景 - 生成按钮参数键时参数包含特殊字符")
    void testGetButtonParamKey_SpecialCharacters() {
        // 准备测试数据
        String tenantId = "tenant-123";
        String objApi = "Custom_Object__c";
        String buttonApi = "custom-button_v1";
        String paramKey = "display_label";

        // 执行测试
        String result = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);

        // 验证结果
        String expected = TranslateI18nUtils.NEW_BUTTON_FIELD + "." + tenantId + "." + objApi + "." + buttonApi + "." + paramKey;
        assertEquals(expected, result);
        assertEquals("ButtonFormVariable.tenant-123.Custom_Object__c.custom-button_v1.display_label", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成按钮参数键时，参数为空的场景
     */
    @Test
    @DisplayName("边界场景 - 生成按钮参数键时参数为空")
    void testGetButtonParamKey_EmptyParameters() {
        // 准备测试数据
        String tenantId = "";
        String objApi = "";
        String buttonApi = "";
        String paramKey = "";

        // 执行测试
        String result = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);

        // 验证结果
        String expected = TranslateI18nUtils.NEW_BUTTON_FIELD + "." + "" + "." + "" + "." + "" + "." + "";
        assertEquals(expected, result);
        assertEquals("ButtonFormVariable....", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成按钮参数键时，参数为null的场景
     */
    @Test
    @DisplayName("边界场景 - 生成按钮参数键时参数为null")
    void testGetButtonParamKey_NullParameters() {
        // 执行测试
        String result = TranslateI18nUtils.getButtonParamKey(null, null, null, null);

        // 验证结果
        String expected = TranslateI18nUtils.NEW_BUTTON_FIELD + "." + "null" + "." + "null" + "." + "null" + "." + "null";
        assertEquals(expected, result);
        assertEquals("ButtonFormVariable.null.null.null.null", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证常量值
     */
    @Test
    @DisplayName("常量验证 - 验证所有常量值")
    void testConstants() {
        // 验证TRANS_TAG_MANAGER_TAG_GROUP常量
        assertEquals("trans_tag_manager_tag_group_", TranslateI18nUtils.TRANS_TAG_MANAGER_TAG_GROUP);
        assertNotNull(TranslateI18nUtils.TRANS_TAG_MANAGER_TAG_GROUP);
        assertFalse(TranslateI18nUtils.TRANS_TAG_MANAGER_TAG_GROUP.isEmpty());

        // 验证TRANS_TAG_MANAGER_SUB_TAG常量
        assertEquals("trans_tag_manager_sub_tag_", TranslateI18nUtils.TRANS_TAG_MANAGER_SUB_TAG);
        assertNotNull(TranslateI18nUtils.TRANS_TAG_MANAGER_SUB_TAG);
        assertFalse(TranslateI18nUtils.TRANS_TAG_MANAGER_SUB_TAG.isEmpty());

        // 验证NEW_BUTTON_FIELD常量
        assertEquals("ButtonFormVariable", TranslateI18nUtils.NEW_BUTTON_FIELD);
        assertNotNull(TranslateI18nUtils.NEW_BUTTON_FIELD);
        assertFalse(TranslateI18nUtils.NEW_BUTTON_FIELD.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试方法的静态特性和一致性
     */
    @Test
    @DisplayName("静态方法验证 - 验证所有方法的静态特性和一致性")
    void testStaticMethodConsistency() {
        // 测试getTransTagManagerTagGroup方法的一致性
        String groupName = "testGroup";
        String result1 = TranslateI18nUtils.getTransTagManagerTagGroup(groupName);
        String result2 = TranslateI18nUtils.getTransTagManagerTagGroup(groupName);
        assertEquals(result1, result2);

        // 测试getTransTagManagerSubTag方法的一致性
        String tagName = "testTag";
        String result3 = TranslateI18nUtils.getTransTagManagerSubTag(tagName);
        String result4 = TranslateI18nUtils.getTransTagManagerSubTag(tagName);
        assertEquals(result3, result4);

        // 测试getButtonParamKey方法的一致性
        String tenantId = "tenant";
        String objApi = "Object";
        String buttonApi = "button";
        String paramKey = "param";
        String result5 = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);
        String result6 = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);
        assertEquals(result5, result6);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试键格式的一致性
     */
    @Test
    @DisplayName("格式验证 - 验证生成的键格式一致性")
    void testKeyFormatConsistency() {
        // 验证标签组键格式
        String groupKey = TranslateI18nUtils.getTransTagManagerTagGroup("test");
        assertTrue(groupKey.startsWith(TranslateI18nUtils.TRANS_TAG_MANAGER_TAG_GROUP));
        assertTrue(groupKey.endsWith("test"));

        // 验证子标签键格式
        String subTagKey = TranslateI18nUtils.getTransTagManagerSubTag("test");
        assertTrue(subTagKey.startsWith(TranslateI18nUtils.TRANS_TAG_MANAGER_SUB_TAG));
        assertTrue(subTagKey.endsWith("test"));

        // 验证按钮参数键格式
        String buttonKey = TranslateI18nUtils.getButtonParamKey("t", "o", "b", "p");
        assertTrue(buttonKey.startsWith(TranslateI18nUtils.NEW_BUTTON_FIELD));
        assertTrue(buttonKey.contains(".t."));
        assertTrue(buttonKey.contains(".o."));
        assertTrue(buttonKey.contains(".b."));
        assertTrue(buttonKey.endsWith(".p"));
        
        // 验证点号分隔符的数量
        long dotCount = buttonKey.chars().filter(ch -> ch == '.').count();
        assertEquals(4, dotCount); // 应该有4个点号分隔5个部分
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试方法的不可变性
     */
    @Test
    @DisplayName("不可变性验证 - 验证相同输入产生相同输出")
    void testMethodImmutability() {
        // 测试多次调用相同方法返回相同结果
        String groupName = "immutableTest";
        String tagName = "immutableTag";
        String tenantId = "tenant";
        String objApi = "Object";
        String buttonApi = "button";
        String paramKey = "param";

        // 多次调用并验证结果一致
        for (int i = 0; i < 5; i++) {
            assertEquals("trans_tag_manager_tag_group_immutableTest", 
                        TranslateI18nUtils.getTransTagManagerTagGroup(groupName));
            assertEquals("trans_tag_manager_sub_tag_immutableTag", 
                        TranslateI18nUtils.getTransTagManagerSubTag(tagName));
            assertEquals("ButtonFormVariable.tenant.Object.button.param", 
                        TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey));
        }
    }
}
