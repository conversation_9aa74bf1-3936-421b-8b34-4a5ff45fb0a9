package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.IFilter;import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Arrays;import java.util.stream.Stream;
import java.util.stream.Collectors;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ApprovalTaskInitSceneProvider的单元测试 测试审批任务场景初始化提供者的功能
 */
@ExtendWith(MockitoExtension.class)
class ApprovalTaskInitSceneProviderTest {

    @InjectMocks
    private ApprovalTaskInitSceneProvider approvalTaskInitSceneProvider;

    private User testUser;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User("74255", "1000");
    }

    /**
     * GenerateByAI 测试内容描述：测试getApiName方法返回正确的API名称
     */
    @Test
    @DisplayName("正常场景 - getApiName返回正确的API名称")
    void testGetApiName_ReturnsCorrectApiName() {
        // 执行测试
        String result = approvalTaskInitSceneProvider.getApiName();

        // 验证结果
        assertNotNull(result);
        assertEquals(Utils.APPROVAL_TASK_API_NAME, result);
        assertEquals("ApprovalTaskObj", result);
    }

    /**
     * GenerateByAI 测试内容描述：测试getDefaultSearchTemplateList方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList返回正确的搜索模板列表")
    void testGetDefaultSearchTemplateList_ReturnsCorrectTemplates() {
        // 准备测试数据
        String apiName = "ApprovalTaskObj";
        String extendAttribute = null;

        // 执行测试
        List<ISearchTemplate> result = approvalTaskInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个模板（待办）
        ISearchTemplate inProgressTemplate = result.get(0);
        assertNotNull(inProgressTemplate);
        assertEquals("in_progress", inProgressTemplate.getApiName());
        assertNotNull(inProgressTemplate.getLabel());

        // 验证第二个模板（已办）
        ISearchTemplate passTemplate = result.get(1);
        assertNotNull(passTemplate);
        assertEquals("pass", passTemplate.getApiName());
        assertNotNull(passTemplate.getLabel());
    }

    /**
     * GenerateByAI 测试内容描述：测试getDefaultSearchTemplateList方法处理不同用户的场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList处理不同租户用户")
    void testGetDefaultSearchTemplateList_DifferentTenantUser() {
        // 准备测试数据 - 不同租户的用户
        User differentTenantUser = new User("78057", "2000");
        String apiName = "ApprovalTaskObj";

        // 执行测试
        List<ISearchTemplate> result = approvalTaskInitSceneProvider.getDefaultSearchTemplateList(differentTenantUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证模板的租户ID正确设置
        for (ISearchTemplate template : result) {
            assertNotNull(template);
            assertEquals("78057", template.getTenantId());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试getDefaultSearchTemplateList方法的过滤器JSON解析
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList正确解析过滤器JSON")
    void testGetDefaultSearchTemplateList_ParsesFiltersCorrectly() {
        // 准备测试数据
        String apiName = "ApprovalTaskObj";

        // 执行测试
        List<ISearchTemplate> result = approvalTaskInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证每个模板都有过滤器
        for (ISearchTemplate template : result) {
            assertNotNull(template.getFilters());
            assertFalse(template.getFilters().isEmpty());

            // 验证过滤器类型
            assertTrue(template.getFilters() instanceof List);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试getLabel方法的参数化测试
     */
    @ParameterizedTest
    @MethodSource("provideLabelTestData")
    @DisplayName("参数化测试 - getLabel返回正确的标签")
    void testGetLabel_ReturnsCorrectLabels(String apiName, String expectedLabelKeyword) {
        // 执行测试
        String result = approvalTaskInitSceneProvider.getLabel(apiName);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains(expectedLabelKeyword),
                String.format("Expected label to contain '%s' but was '%s'", expectedLabelKeyword, result));
    }

    /**
     * 提供getLabel测试的参数化数据
     */
    private static Stream<Arguments> provideLabelTestData() {
        return Stream.of(
                Arguments.of("in_progress", "待办"),
                Arguments.of("pass", "已办")
        );
    }

    /**
     * GenerateByAI 测试内容描述：测试getLabel方法处理未知API名称的场景
     */
    @Test
    @DisplayName("边界场景 - getLabel处理未知API名称返回null")
    void testGetLabel_UnknownApiName_ReturnsNull() {
        // 执行测试
        String result = approvalTaskInitSceneProvider.getLabel("unknown_api_name");

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI 测试内容描述：测试getLabel方法处理null参数的场景
     */
    @Test
    @DisplayName("边界场景 - getLabel处理null参数返回null")
    void testGetLabel_NullApiName_ReturnsNull() {
        // 执行测试
        String result = approvalTaskInitSceneProvider.getLabel(null);

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI 测试内容描述：测试getDefaultSearchTemplateList方法处理null用户的场景
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList处理null用户抛出异常")
    void testGetDefaultSearchTemplateListThrowsNullPointerException_NullUser() {
        // 执行并验证异常
        assertThrows(NullPointerException.class, () -> {
            approvalTaskInitSceneProvider.getDefaultSearchTemplateList(null, "ApprovalTaskObj", null);
        });
    }

    /**
     * GenerateByAI 测试内容描述：测试getDefaultSearchTemplateList方法处理null API名称的场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList处理null API名称")
    void testGetDefaultSearchTemplateList_NullApiName() {
        // 执行测试
        List<ISearchTemplate> result = approvalTaskInitSceneProvider.getDefaultSearchTemplateList(testUser, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证模板的API名称设置
        for (ISearchTemplate template : result) {
            assertNotNull(template.getApiName());
            assertTrue(template.getApiName().equals("in_progress") || template.getApiName().equals("pass"));
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(approvalTaskInitSceneProvider);
    }
}
