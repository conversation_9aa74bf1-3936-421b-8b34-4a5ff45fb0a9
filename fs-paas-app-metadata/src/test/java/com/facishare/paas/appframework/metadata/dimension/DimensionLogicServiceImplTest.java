package com.facishare.paas.appframework.metadata.dimension;

import com.facishare.paas.appframework.common.service.dto.DimensionInfo;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.social.base.model.NameLanguage;
import com.facishare.social.dimension.DimensionObjService;
import com.facishare.social.dimension.model.DimensionDto;
import com.facishare.social.dimension.model.GetDimensionListByCodes;
import com.facishare.social.dimension.model.GetDimensionListByIds;
import com.facishare.social.dimension.model.GetDimensionListByNames;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * DimensionLogicServiceImpl的单元测试
 * 测试维度逻辑服务的功能
 */
@ExtendWith(MockitoExtension.class)
class DimensionLogicServiceImplTest {

    @Mock
    private DimensionObjService mockDimensionObjService;

    @InjectMocks
    private DimensionLogicServiceImpl dimensionLogicService;

    private User testUser;
    private List<String> testIds;
    private List<String> testNames;
    private List<String> testCodes;
    private List<DimensionDto> testDimensionDtos;

    @BeforeEach
    void setUp() {
        // 构造测试用户
        testUser = User.systemUser("74255");
        
        // 构造测试数据
        testIds = Lists.newArrayList("dim1", "dim2", "dim3");
        testNames = Lists.newArrayList("维度1", "维度2", "维度3");
        testCodes = Lists.newArrayList("code1", "code2", "code3");
        
        // 构造测试DimensionDto对象
        testDimensionDtos = Lists.newArrayList();
        for (int i = 0; i < 3; i++) {
            DimensionDto dto = new DimensionDto();
            dto.setId(testIds.get(i));
            dto.setName(testNames.get(i));
            dto.setDimCode(testCodes.get(i));
            dto.setDimType("test_type");
            testDimensionDtos.add(dto);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID获取维度信息的正常场景
     */
    @Test
    @DisplayName("根据ID获取维度信息 - 正常场景")
    void testGetDimensionInfoByIds_正常场景() {
        // 准备测试数据
        GetDimensionListByIds.Result mockResult = new GetDimensionListByIds.Result();
        mockResult.setList(testDimensionDtos);
        
        // 配置Mock行为
        when(mockDimensionObjService.getDimensionListByIds(any(GetDimensionListByIds.Argument.class)))
                .thenReturn(mockResult);
        
        try (MockedStatic<NameLanguage> mockedNameLanguage = mockStatic(NameLanguage.class);
             MockedStatic<RequestUtil> mockedRequestUtil = mockStatic(RequestUtil.class)) {
            
            // Mock静态方法
            mockedRequestUtil.when(RequestUtil::getCurrentLang)
                    .thenReturn("zh_CN");
            mockedNameLanguage.when(() -> NameLanguage.getNameByLanguageCode(any(DimensionDto.class), eq("zh_CN")))
                    .thenReturn("中文名称");
            
            // 执行被测试方法
            List<DimensionInfo> result = dimensionLogicService.getDimensionInfoByIds(testIds, testUser);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(3, result.size());
            assertEquals("dim1", result.get(0).getId());
            assertEquals("中文名称", result.get(0).getName());
            assertEquals("code1", result.get(0).getDimCode());
            assertEquals("test_type", result.get(0).getDimType());
        }
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByIds(any(GetDimensionListByIds.Argument.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID获取维度信息时返回结果为空
     */
    @Test
    @DisplayName("根据ID获取维度信息 - 返回结果为空")
    void testGetDimensionInfoByIds_返回结果为空() {
        // 配置Mock行为 - 返回null
        when(mockDimensionObjService.getDimensionListByIds(any(GetDimensionListByIds.Argument.class)))
                .thenReturn(null);
        
        // 执行被测试方法
        List<DimensionInfo> result = dimensionLogicService.getDimensionInfoByIds(testIds, testUser);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByIds(any(GetDimensionListByIds.Argument.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID获取维度信息时列表为空
     */
    @Test
    @DisplayName("根据ID获取维度信息 - 列表为空")
    void testGetDimensionInfoByIds_列表为空() {
        // 准备测试数据
        GetDimensionListByIds.Result mockResult = new GetDimensionListByIds.Result();
        mockResult.setList(Lists.newArrayList());
        
        // 配置Mock行为
        when(mockDimensionObjService.getDimensionListByIds(any(GetDimensionListByIds.Argument.class)))
                .thenReturn(mockResult);
        
        // 执行被测试方法
        List<DimensionInfo> result = dimensionLogicService.getDimensionInfoByIds(testIds, testUser);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByIds(any(GetDimensionListByIds.Argument.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称获取维度信息的正常场景
     */
    @Test
    @DisplayName("根据名称获取维度信息 - 正常场景")
    void testGetDimensionInfoByNames_正常场景() {
        // 准备测试数据
        GetDimensionListByNames.Result mockResult = new GetDimensionListByNames.Result();
        mockResult.setList(testDimensionDtos);
        
        // 配置Mock行为
        when(mockDimensionObjService.getDimensionListByNames(any(GetDimensionListByNames.Argument.class)))
                .thenReturn(mockResult);
        
        try (MockedStatic<NameLanguage> mockedNameLanguage = mockStatic(NameLanguage.class);
             MockedStatic<RequestUtil> mockedRequestUtil = mockStatic(RequestUtil.class)) {
            
            // Mock静态方法
            mockedRequestUtil.when(RequestUtil::getCurrentLang)
                    .thenReturn("zh_CN");
            mockedNameLanguage.when(() -> NameLanguage.getNameByLanguageCode(any(DimensionDto.class), eq("zh_CN")))
                    .thenReturn(null); // 返回null，使用默认名称
            
            // 执行被测试方法
            List<DimensionInfo> result = dimensionLogicService.getDimensionInfoByNames(testNames, testUser);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(3, result.size());
            assertEquals("dim1", result.get(0).getId());
            assertEquals("维度1", result.get(0).getName()); // 使用默认名称
            assertEquals("code1", result.get(0).getDimCode());
        }
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByNames(any(GetDimensionListByNames.Argument.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称获取维度信息时返回结果为空
     */
    @Test
    @DisplayName("根据名称获取维度信息 - 返回结果为空")
    void testGetDimensionInfoByNames_返回结果为空() {
        // 配置Mock行为 - 返回null
        when(mockDimensionObjService.getDimensionListByNames(any(GetDimensionListByNames.Argument.class)))
                .thenReturn(null);
        
        // 执行被测试方法
        List<DimensionInfo> result = dimensionLogicService.getDimensionInfoByNames(testNames, testUser);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByNames(any(GetDimensionListByNames.Argument.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据编码获取维度信息的正常场景
     */
    @Test
    @DisplayName("根据编码获取维度信息 - 正常场景")
    void testGetDimensionInfoByCodes_正常场景() {
        // 准备测试数据
        GetDimensionListByCodes.Result mockResult = new GetDimensionListByCodes.Result();
        mockResult.setList(testDimensionDtos);
        
        // 配置Mock行为
        when(mockDimensionObjService.getDimensionListByCodes(any(GetDimensionListByCodes.Argument.class)))
                .thenReturn(mockResult);
        
        try (MockedStatic<NameLanguage> mockedNameLanguage = mockStatic(NameLanguage.class);
             MockedStatic<RequestUtil> mockedRequestUtil = mockStatic(RequestUtil.class)) {
            
            // Mock静态方法
            mockedRequestUtil.when(RequestUtil::getCurrentLang)
                    .thenReturn("en_US");
            mockedNameLanguage.when(() -> NameLanguage.getNameByLanguageCode(any(DimensionDto.class), eq("en_US")))
                    .thenReturn("English Name");
            
            // 执行被测试方法
            List<DimensionInfo> result = dimensionLogicService.getDimensionInfoByCodes(testCodes, testUser);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(3, result.size());
            assertEquals("dim1", result.get(0).getId());
            assertEquals("English Name", result.get(0).getName());
            assertEquals("code1", result.get(0).getDimCode());
        }
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByCodes(any(GetDimensionListByCodes.Argument.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据编码获取维度信息时返回结果为空
     */
    @Test
    @DisplayName("根据编码获取维度信息 - 返回结果为空")
    void testGetDimensionInfoByCodes_返回结果为空() {
        // 配置Mock行为 - 返回null
        when(mockDimensionObjService.getDimensionListByCodes(any(GetDimensionListByCodes.Argument.class)))
                .thenReturn(null);
        
        // 执行被测试方法
        List<DimensionInfo> result = dimensionLogicService.getDimensionInfoByCodes(testCodes, testUser);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByCodes(any(GetDimensionListByCodes.Argument.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据编码获取维度信息时列表为空
     */
    @Test
    @DisplayName("根据编码获取维度信息 - 列表为空")
    void testGetDimensionInfoByCodes_列表为空() {
        // 准备测试数据
        GetDimensionListByCodes.Result mockResult = new GetDimensionListByCodes.Result();
        mockResult.setList(Lists.newArrayList());
        
        // 配置Mock行为
        when(mockDimensionObjService.getDimensionListByCodes(any(GetDimensionListByCodes.Argument.class)))
                .thenReturn(mockResult);
        
        // 执行被测试方法
        List<DimensionInfo> result = dimensionLogicService.getDimensionInfoByCodes(testCodes, testUser);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByCodes(any(GetDimensionListByCodes.Argument.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多语言名称处理时返回空白字符串
     */
    @Test
    @DisplayName("多语言名称处理 - 返回空白字符串")
    void testGetDimensionInfoByIds_多语言名称为空白() {
        // 准备测试数据
        GetDimensionListByIds.Result mockResult = new GetDimensionListByIds.Result();
        mockResult.setList(testDimensionDtos);
        
        // 配置Mock行为
        when(mockDimensionObjService.getDimensionListByIds(any(GetDimensionListByIds.Argument.class)))
                .thenReturn(mockResult);
        
        try (MockedStatic<NameLanguage> mockedNameLanguage = mockStatic(NameLanguage.class);
             MockedStatic<RequestUtil> mockedRequestUtil = mockStatic(RequestUtil.class)) {
            
            // Mock静态方法
            mockedRequestUtil.when(RequestUtil::getCurrentLang)
                    .thenReturn("zh_CN");
            mockedNameLanguage.when(() -> NameLanguage.getNameByLanguageCode(any(DimensionDto.class), eq("zh_CN")))
                    .thenReturn("   "); // 返回空白字符串
            
            // 执行被测试方法
            List<DimensionInfo> result = dimensionLogicService.getDimensionInfoByIds(testIds, testUser);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(3, result.size());
            assertEquals("维度1", result.get(0).getName()); // 使用默认名称
        }
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByIds(any(GetDimensionListByIds.Argument.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试传入空列表的场景
     */
    @Test
    @DisplayName("传入空列表 - 各种方法")
    void testEmptyInputLists() {
        // 准备测试数据
        List<String> emptyList = Lists.newArrayList();
        
        // 配置Mock行为
        when(mockDimensionObjService.getDimensionListByIds(any(GetDimensionListByIds.Argument.class)))
                .thenReturn(new GetDimensionListByIds.Result());
        when(mockDimensionObjService.getDimensionListByNames(any(GetDimensionListByNames.Argument.class)))
                .thenReturn(new GetDimensionListByNames.Result());
        when(mockDimensionObjService.getDimensionListByCodes(any(GetDimensionListByCodes.Argument.class)))
                .thenReturn(new GetDimensionListByCodes.Result());
        
        // 执行被测试方法
        List<DimensionInfo> resultByIds = dimensionLogicService.getDimensionInfoByIds(emptyList, testUser);
        List<DimensionInfo> resultByNames = dimensionLogicService.getDimensionInfoByNames(emptyList, testUser);
        List<DimensionInfo> resultByCodes = dimensionLogicService.getDimensionInfoByCodes(emptyList, testUser);
        
        // 验证结果
        assertNotNull(resultByIds);
        assertTrue(resultByIds.isEmpty());
        assertNotNull(resultByNames);
        assertTrue(resultByNames.isEmpty());
        assertNotNull(resultByCodes);
        assertTrue(resultByCodes.isEmpty());
        
        // 验证Mock交互
        verify(mockDimensionObjService).getDimensionListByIds(any(GetDimensionListByIds.Argument.class));
        verify(mockDimensionObjService).getDimensionListByNames(any(GetDimensionListByNames.Argument.class));
        verify(mockDimensionObjService).getDimensionListByCodes(any(GetDimensionListByCodes.Argument.class));
    }
}
