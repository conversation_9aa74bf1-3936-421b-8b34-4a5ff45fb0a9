package com.facishare.paas.appframework.metadata.metadatahandle

import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class ObjectDisplayNameHandleImplTest extends Specification {

    ObjectDisplayNameHandleImpl objectDisplayNameHandle
    def describeLogicService = Mock(DescribeLogicService)
    def mockObjectDescribe = Mock(IObjectDescribe)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        objectDisplayNameHandle = new ObjectDisplayNameHandleImpl()
        objectDisplayNameHandle.describeLogicService = describeLogicService
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称的正常场景
     */
    def "findObjectDisplayNameTest - 正常查找对象显示名称"() {
        given:
        def tenantId = "tenant123"
        def apiName = "Account"
        def expectedDisplayName = "客户"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> expectedDisplayName
        
        when:
        def result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result == expectedDisplayName
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时显示名称为空字符串的场景
     */
    def "findObjectDisplayNameTest - 显示名称为空字符串"() {
        given:
        def tenantId = "tenant123"
        def apiName = "Contact"
        def expectedDisplayName = ""
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> expectedDisplayName
        
        when:
        def result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result == expectedDisplayName
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时显示名称为null的场景
     */
    def "findObjectDisplayNameTest - 显示名称为null"() {
        given:
        def tenantId = "tenant123"
        def apiName = "Opportunity"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> null
        
        when:
        def result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result == null
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时包含特殊字符的场景
     */
    def "findObjectDisplayNameTest - 显示名称包含特殊字符"() {
        given:
        def tenantId = "tenant123"
        def apiName = "Custom_Object__c"
        def expectedDisplayName = "自定义对象（测试）"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> expectedDisplayName
        
        when:
        def result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result == expectedDisplayName
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时租户ID包含特殊字符的场景
     */
    def "findObjectDisplayNameTest - 租户ID包含特殊字符"() {
        given:
        def tenantId = "tenant-123_test"
        def apiName = "Account"
        def expectedDisplayName = "客户账户"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> expectedDisplayName
        
        when:
        def result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result == expectedDisplayName
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时API名称包含特殊字符的场景
     */
    def "findObjectDisplayNameTest - API名称包含特殊字符"() {
        given:
        def tenantId = "tenant123"
        def apiName = "Test_Object_v2.0"
        def expectedDisplayName = "测试对象v2.0"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> expectedDisplayName
        
        when:
        def result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result == expectedDisplayName
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时显示名称包含多语言字符的场景
     */
    def "findObjectDisplayNameTest - 显示名称包含多语言字符"() {
        given:
        def tenantId = "tenant123"
        def apiName = "Product"
        def expectedDisplayName = "产品 Product プロダクト"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> expectedDisplayName
        
        when:
        def result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result == expectedDisplayName
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时租户ID为null的异常场景
     */
    def "findObjectDisplayNameError - 租户ID为null"() {
        given:
        String tenantId = null
        def apiName = "Account"
        
        when:
        objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        // 根据@Nonnull注解，应该在调用时就抛出异常或在方法内部处理
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        1 * mockObjectDescribe.getDisplayName() >> "客户"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时API名称为null的异常场景
     */
    def "findObjectDisplayNameError - API名称为null"() {
        given:
        def tenantId = "tenant123"
        String apiName = null
        
        when:
        objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        // 根据@Nonnull注解，应该在调用时就抛出异常或在方法内部处理
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        1 * mockObjectDescribe.getDisplayName() >> "测试对象"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时租户ID为空字符串的场景
     */
    def "findObjectDisplayNameTest - 租户ID为空字符串"() {
        given:
        def tenantId = ""
        def apiName = "Account"
        def expectedDisplayName = "客户"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> expectedDisplayName
        
        when:
        def result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result == expectedDisplayName
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时API名称为空字符串的场景
     */
    def "findObjectDisplayNameTest - API名称为空字符串"() {
        given:
        def tenantId = "tenant123"
        def apiName = ""
        def expectedDisplayName = "默认对象"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> expectedDisplayName
        
        when:
        def result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result == expectedDisplayName
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时DescribeLogicService抛出异常的场景
     */
    def "findObjectDisplayNameError - DescribeLogicService抛出异常"() {
        given:
        def tenantId = "tenant123"
        def apiName = "Account"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> { throw new RuntimeException("Test exception") }
        
        when:
        objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        thrown(RuntimeException)
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        0 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时findObjectWithoutCopyIfGray返回null的场景
     */
    def "findObjectDisplayNameError - findObjectWithoutCopyIfGray返回null"() {
        given:
        def tenantId = "tenant123"
        def apiName = "NonExistentObject"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> null
        
        when:
        objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        thrown(NullPointerException)
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时IObjectDescribe.getDisplayName()抛出异常的场景
     */
    def "findObjectDisplayNameError - getDisplayName抛出异常"() {
        given:
        def tenantId = "tenant123"
        def apiName = "Account"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> { throw new RuntimeException("Test exception") }
        
        when:
        objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        thrown(RuntimeException)
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        1 * mockObjectDescribe.getDisplayName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称的一致性验证
     */
    def "findObjectDisplayNameTest - 一致性验证"() {
        given:
        def tenantId = "tenant123"
        def apiName = "Account"
        def expectedDisplayName = "客户"
        
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName) >> mockObjectDescribe
        mockObjectDescribe.getDisplayName() >> expectedDisplayName
        
        when:
        def result1 = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        def result2 = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName)
        
        then:
        result1 == result2
        result1 == expectedDisplayName
        2 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName)
        2 * mockObjectDescribe.getDisplayName()
    }
} 