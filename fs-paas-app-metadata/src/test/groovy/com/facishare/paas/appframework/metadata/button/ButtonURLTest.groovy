package com.facishare.paas.appframework.metadata.button

import com.alibaba.fastjson.JSON
import spock.lang.Specification

class ButtonURLTest extends Specification {
    def "test toJsonString"() {
        given:
        def str = '''{
    "urlInfos": [
      {
        "clientType": "mobile",
        "url": "https://www.fxiaoke.com/gamma/auth/connect?resourceUrl=fs-sail-order-common&_hash=/visitsales/&source=common"
      },
      {
        "clientType": "web",
        "url": "https://www.fxiaoke.com/gamma/auth/connect?resourceUrl=fs-sail-order-common&_hash=/visitsales/&source=common"
      }
    ]
  }'''
        def url = JSON.parseObject(str, ButtonURL.class);
        when:
        def json = url.toJsonString()
        then:
        json.length() == str.length()
    }

    def "List == \"\""() {
        given:
        def list = ["123"]
        print list == "123"
    }

}
