package com.facishare.paas.appframework.metadata.multicurrency

import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.MetaDataService
import com.facishare.paas.appframework.metadata.MtExchangeRate
import com.facishare.paas.appframework.metadata.dto.QueryExchangeRate
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class ExchangeRateServiceTest extends Specification {

    ExchangeRateService exchangeRateService
    def metaDataService = Mock(MetaDataService)
    def user = Mock(User)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        exchangeRateService = new ExchangeRateService()
        exchangeRateService.metaDataService = metaDataService
        
        user.getTenantId() >> "tenant123"
        user.getUserId() >> "user123"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据的正常场景
     */
    def "queryExchangeRateDataTest - 正常查询"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> 1
        arg.getPageSize() >> 10
        arg.getCurrencyCode() >> "USD"
        arg.getStartTime() >> 1000L
        arg.getEndTime() >> 2000L
        
        def objectData1 = Mock(IObjectData)
        def objectData2 = Mock(IObjectData)
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> [objectData1, objectData2]
        queryResult.getTotalNumber() >> 2
        
        metaDataService.findBySearchQuery(user, ExchangeRateService.MT_EXCHANGE_RATE_API_NAME, _) >> queryResult
        
        when:
        def result = exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        result.data.size() == 2
        result.totalNumber == 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时结果为空的场景
     */
    def "queryExchangeRateDataTest - 结果为空"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> 1
        arg.getPageSize() >> 10
        arg.getCurrencyCode() >> "USD"
        arg.getStartTime() >> null
        arg.getEndTime() >> null
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        queryResult.getTotalNumber() >> 0
        
        metaDataService.findBySearchQuery(user, ExchangeRateService.MT_EXCHANGE_RATE_API_NAME, _) >> queryResult
        
        when:
        def result = exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        result.data.isEmpty()
        result.totalNumber == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时货币代码为空的场景
     */
    def "queryExchangeRateDataTest - 货币代码为空"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> 1
        arg.getPageSize() >> 10
        arg.getCurrencyCode() >> null
        arg.getStartTime() >> 1000L
        arg.getEndTime() >> 2000L
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        queryResult.getTotalNumber() >> 0
        
        metaDataService.findBySearchQuery(user, ExchangeRateService.MT_EXCHANGE_RATE_API_NAME, _) >> queryResult
        
        when:
        def result = exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        result.data.isEmpty()
        result.totalNumber == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时货币代码为空字符串的场景
     */
    def "queryExchangeRateDataTest - 货币代码为空字符串"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> 1
        arg.getPageSize() >> 10
        arg.getCurrencyCode() >> ""
        arg.getStartTime() >> 1000L
        arg.getEndTime() >> 2000L
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        queryResult.getTotalNumber() >> 0
        
        metaDataService.findBySearchQuery(user, ExchangeRateService.MT_EXCHANGE_RATE_API_NAME, _) >> queryResult
        
        when:
        def result = exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        result.data.isEmpty()
        result.totalNumber == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时开始时间为null的场景
     */
    def "queryExchangeRateDataTest - 开始时间为null"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> 1
        arg.getPageSize() >> 10
        arg.getCurrencyCode() >> "USD"
        arg.getStartTime() >> null
        arg.getEndTime() >> 2000L
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        queryResult.getTotalNumber() >> 0
        
        metaDataService.findBySearchQuery(user, ExchangeRateService.MT_EXCHANGE_RATE_API_NAME, _) >> queryResult
        
        when:
        def result = exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        result.data.isEmpty()
        result.totalNumber == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时结束时间为null的场景
     */
    def "queryExchangeRateDataTest - 结束时间为null"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> 1
        arg.getPageSize() >> 10
        arg.getCurrencyCode() >> "USD"
        arg.getStartTime() >> 1000L
        arg.getEndTime() >> null
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        queryResult.getTotalNumber() >> 0
        
        metaDataService.findBySearchQuery(user, ExchangeRateService.MT_EXCHANGE_RATE_API_NAME, _) >> queryResult
        
        when:
        def result = exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        result.data.isEmpty()
        result.totalNumber == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时分页参数为边界值的场景
     */
    def "queryExchangeRateDataTest - 分页参数为边界值"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> pageNumber
        arg.getPageSize() >> pageSize
        arg.getCurrencyCode() >> "USD"
        arg.getStartTime() >> 1000L
        arg.getEndTime() >> 2000L
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        queryResult.getTotalNumber() >> 0
        
        metaDataService.findBySearchQuery(user, ExchangeRateService.MT_EXCHANGE_RATE_API_NAME, _) >> queryResult
        
        when:
        def result = exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        result.data.isEmpty()
        result.totalNumber == 0
        
        where:
        pageNumber | pageSize
        0          | 10
        1          | 0
        1          | 1
        1          | 1000
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量插入汇率的正常场景
     */
    def "batchInsertExchangeRateTest - 正常批量插入"() {
        given:
        def exchangeRate1 = Mock(MtExchangeRate)
        def exchangeRate2 = Mock(MtExchangeRate)
        def exchangeRateList = [exchangeRate1, exchangeRate2]
        
        def objectData1 = Mock(IObjectData)
        def objectData2 = Mock(IObjectData)
        def insertResult = [objectData1, objectData2]
        
        metaDataService.bulkSaveObjectData(_, user) >> insertResult
        
        when:
        def result = exchangeRateService.batchInsertExchangeRate(user, exchangeRateList)
        
        then:
        result.size() == 2
        1 * metaDataService.bulkSaveObjectData(_, user) >> insertResult
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量插入汇率时列表为空的场景
     */
    def "batchInsertExchangeRateTest - 列表为空"() {
        given:
        def exchangeRateList = []
        def insertResult = []
        
        metaDataService.bulkSaveObjectData(_, user) >> insertResult
        
        when:
        def result = exchangeRateService.batchInsertExchangeRate(user, exchangeRateList)
        
        then:
        result.isEmpty()
        1 * metaDataService.bulkSaveObjectData([], user) >> insertResult
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量插入汇率时列表为null的场景
     */
    def "batchInsertExchangeRateError - 列表为null"() {
        given:
        List<MtExchangeRate> exchangeRateList = null
        
        when:
        exchangeRateService.batchInsertExchangeRate(user, exchangeRateList)
        
        then:
        thrown(NullPointerException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量插入汇率时用户为null的异常场景
     */
    def "batchInsertExchangeRateError - 用户为null"() {
        given:
        def exchangeRate = Mock(MtExchangeRate)
        def exchangeRateList = [exchangeRate]
        User nullUser = null
        
        when:
        exchangeRateService.batchInsertExchangeRate(nullUser, exchangeRateList)
        
        then:
        thrown(NullPointerException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时参数为null的异常场景
     */
    def "queryExchangeRateDataError - 参数为null"() {
        given:
        QueryExchangeRate.Arg arg = null
        
        when:
        exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        thrown(NullPointerException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时用户为null的异常场景
     */
    def "queryExchangeRateDataError - 用户为null"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> 1
        arg.getPageSize() >> 10
        arg.getCurrencyCode() >> "USD"
        arg.getStartTime() >> 1000L
        arg.getEndTime() >> 2000L
        
        User nullUser = null
        
        when:
        exchangeRateService.queryExchangeRateData(nullUser, arg)
        
        then:
        thrown(NullPointerException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时数据服务抛出异常的场景
     */
    def "queryExchangeRateDataError - 数据服务异常"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> 1
        arg.getPageSize() >> 10
        arg.getCurrencyCode() >> "USD"
        arg.getStartTime() >> 1000L
        arg.getEndTime() >> 2000L
        
        metaDataService.findBySearchQuery(user, ExchangeRateService.MT_EXCHANGE_RATE_API_NAME, _) >> { throw new RuntimeException("Database error") }
        
        when:
        exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        thrown(RuntimeException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量插入汇率时数据服务抛出异常的场景
     */
    def "batchInsertExchangeRateError - 数据服务异常"() {
        given:
        def exchangeRate = Mock(MtExchangeRate)
        def exchangeRateList = [exchangeRate]
        
        metaDataService.bulkSaveObjectData(_, user) >> { throw new RuntimeException("Insert error") }
        
        when:
        exchangeRateService.batchInsertExchangeRate(user, exchangeRateList)
        
        then:
        thrown(RuntimeException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询汇率数据时包含大量数据的场景
     */
    def "queryExchangeRateDataTest - 大量数据"() {
        given:
        def arg = Mock(QueryExchangeRate.Arg)
        arg.getPageNumber() >> 1
        arg.getPageSize() >> 1000
        arg.getCurrencyCode() >> "USD"
        arg.getStartTime() >> 1000L
        arg.getEndTime() >> 2000L
        
        def objectDataList = []
        (1..1000).each { objectDataList << Mock(IObjectData) }
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> objectDataList
        queryResult.getTotalNumber() >> 1000
        
        metaDataService.findBySearchQuery(user, ExchangeRateService.MT_EXCHANGE_RATE_API_NAME, _) >> queryResult
        
        when:
        def result = exchangeRateService.queryExchangeRateData(user, arg)
        
        then:
        result.data.size() == 1000
        result.totalNumber == 1000
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量插入汇率时包含大量数据的场景
     */
    def "batchInsertExchangeRateTest - 大量数据"() {
        given:
        def exchangeRateList = []
        def insertResult = []
        (1..100).each { 
            exchangeRateList << Mock(MtExchangeRate)
            insertResult << Mock(IObjectData)
        }
        
        metaDataService.bulkSaveObjectData(_, user) >> insertResult
        
        when:
        def result = exchangeRateService.batchInsertExchangeRate(user, exchangeRateList)
        
        then:
        result.size() == 100
        1 * metaDataService.bulkSaveObjectData(_, user) >> insertResult
    }
} 