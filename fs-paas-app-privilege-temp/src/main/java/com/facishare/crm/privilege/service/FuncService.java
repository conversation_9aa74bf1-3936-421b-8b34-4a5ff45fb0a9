package com.facishare.crm.privilege.service;

import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.swagger.ApiException;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2017/4/24.
 */
@Deprecated
public interface FuncService {

  /**
   * 增加新的funCode
   *
   * @param tenantId
   * @param funcCode2DisplayNameMap
   */
  void addFuncCode(String tenantId, Map<String, String> funcCode2DisplayNameMap, Integer funcType) throws CrmCheckedException, ApiException;

  /**
   * 为灰度企业自定义对象添加固定的缺少的权限
   *
   * @param tenantId
   */
  void addUserDefinedFuncCode(String tenantId,Map<String, String> funcCode2DisplayNameMap) throws CrmCheckedException, ApiException;

  /**
   * 增加funcCode而且根据 funcCode之间的关联关系,为有关联关系的funcCode添加状态
   *
   * @param tenantId
   * @param funcCode2DisplayNameMap
   * @throws ApiException
   * @throws CrmCheckedException
   */
  void addAssociatedRelationFuncCode(String tenantId, Map<String, String> funcCode2DisplayNameMap, Integer funcType) throws ApiException, CrmCheckedException;

  /**
   * 批量企业对某个角色增加多个funcCode的权限
   *
   * @param tenantId
   * @param funcCodes
   */
  void addFuncAccess(String tenantId, List<String> funcCodes, String roleCode) throws CrmCheckedException, ApiException;

  void addAllRoleFuncAccess(String tenantId, List<String> funcCodes) throws CrmCheckedException, ApiException;

  /**
   * 根据配置文件更新
   * @param tenantId
   */
  void addAllObjectFuncAccessByFuncInfo(String tenantId,Map<String,List<String>> roleCode2funcCodes);

  void deleteFuncCode();

  void updateFuncCode();

  void createFuncByOtherFunc(String tenantId, String sourceCode, String targetFuncCode, String description);

}
