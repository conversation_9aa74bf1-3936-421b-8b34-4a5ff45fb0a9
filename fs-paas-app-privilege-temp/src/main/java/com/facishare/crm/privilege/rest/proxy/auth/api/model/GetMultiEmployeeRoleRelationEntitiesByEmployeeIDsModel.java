package com.facishare.crm.privilege.rest.proxy.auth.api.model;

import com.facishare.crm.privilege.rest.proxy.auth.model.BaseAuthResult;
import com.facishare.crm.privilege.rest.proxy.auth.api.pojo.UserRolePojo;
import com.facishare.crm.privilege.rest.proxy.auth.model.BaseArg_Auth;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by linqiuying on 17/6/8.
 */
public class GetMultiEmployeeRoleRelationEntitiesByEmployeeIDsModel {
  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Arg extends BaseArg_Auth {
    private List<String> users;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BaseAuthResult {
      private List<UserRolePojo> result;
  }
}
