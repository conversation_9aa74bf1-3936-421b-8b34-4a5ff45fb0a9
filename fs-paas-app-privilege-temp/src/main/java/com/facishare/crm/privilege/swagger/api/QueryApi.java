/**
 * Facishare PAAS Organization
 * This is PAAS organization service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.api;

import com.facishare.crm.privilege.swagger.ApiCallback;
import com.facishare.crm.privilege.swagger.ApiClient;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.ApiResponse;
import com.facishare.crm.privilege.swagger.Configuration;
import com.facishare.crm.privilege.swagger.Pair;
import com.facishare.crm.privilege.swagger.ProgressRequestBody;
import com.facishare.crm.privilege.swagger.ProgressResponseBody;
import com.facishare.crm.privilege.swagger.model.BaseArg;
import com.facishare.crm.privilege.swagger.model.BaseResult;
import com.facishare.crm.privilege.swagger.model.DeptKeyArg;
import com.facishare.crm.privilege.swagger.model.FilterArg;
import com.facishare.crm.privilege.swagger.model.FilterGroupArg;
import com.facishare.crm.privilege.swagger.model.GroupArg;
import com.facishare.crm.privilege.swagger.model.GroupIdListArg;
import com.facishare.crm.privilege.swagger.model.GroupMultiResult;
import com.facishare.crm.privilege.swagger.model.GroupPageResult;
import com.facishare.crm.privilege.swagger.model.GroupResult;
import com.facishare.crm.privilege.swagger.model.IdArg;
import com.facishare.crm.privilege.swagger.model.IdListArg;
import com.facishare.crm.privilege.swagger.model.ListDeptInfoResult;
import com.facishare.crm.privilege.swagger.model.ListMainDeptResult;
import com.facishare.crm.privilege.swagger.model.ListPageResult;
import com.facishare.crm.privilege.swagger.model.ListStringResult;
import com.facishare.crm.privilege.swagger.model.ListUserInfoResult;
import com.facishare.crm.privilege.swagger.model.MapDeptUserInfoListResult;
import com.facishare.crm.privilege.swagger.model.MultiArg;
import com.facishare.crm.privilege.swagger.model.NameArg;
import com.facishare.crm.privilege.swagger.model.OrgNameArg;
import com.facishare.crm.privilege.swagger.model.UserGroupArg;
import com.facishare.crm.privilege.swagger.model.UserIdGroupPageResult;
import com.facishare.crm.privilege.swagger.model.UserInfoResult;
import com.facishare.crm.privilege.swagger.model.UserListArg;
import com.google.gson.reflect.TypeToken;
import okhttp3.Call;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QueryApi {
    private ApiClient apiClient;

    public QueryApi() {
        this(Configuration.getDefaultApiClient());
    }

    public QueryApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /* Build call for getDeptIdsByName */
    private okhttp3.Call getDeptIdsByNameCall(OrgNameArg orgNameArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = orgNameArg;
        
        // verify the required parameter 'orgNameArg' is set
        if (orgNameArg == null) {
            throw new ApiException("Missing the required parameter 'orgNameArg' when calling getDeptIdsByName(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/department/name".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * getDeptIdsByName
     * 2-all, 1-stop, 0-start
     * @param orgNameArg  (required)
     * @return ListStringResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListStringResult getDeptIdsByName(OrgNameArg orgNameArg) throws ApiException {
        ApiResponse<ListStringResult> resp = getDeptIdsByNameWithHttpInfo(orgNameArg);
        return resp.getData();
    }

    /**
     * getDeptIdsByName
     * 2-all, 1-stop, 0-start
     * @param orgNameArg  (required)
     * @return ApiResponse&lt;ListStringResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListStringResult> getDeptIdsByNameWithHttpInfo(OrgNameArg orgNameArg) throws ApiException {
        okhttp3.Call call = getDeptIdsByNameCall(orgNameArg, null, null);
        Type localVarReturnType = new TypeToken<ListStringResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * getDeptIdsByName (asynchronously)
     * 2-all, 1-stop, 0-start
     * @param orgNameArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getDeptIdsByNameAsync(OrgNameArg orgNameArg, final ApiCallback<ListStringResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getDeptIdsByNameCall(orgNameArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListStringResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getDeptInfoByDeptIds */
    private okhttp3.Call getDeptInfoByDeptIdsCall(IdListArg idListArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idListArg;
        
        // verify the required parameter 'idListArg' is set
        if (idListArg == null) {
            throw new ApiException("Missing the required parameter 'idListArg' when calling getDeptInfoByDeptIds(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/department/info/batch".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * getDeptInfoByDeptIds
     * query  department&#39;s  information
     * @param idListArg  (required)
     * @return ListDeptInfoResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListDeptInfoResult getDeptInfoByDeptIds(IdListArg idListArg) throws ApiException {
        ApiResponse<ListDeptInfoResult> resp = getDeptInfoByDeptIdsWithHttpInfo(idListArg);
        return resp.getData();
    }

    /**
     * getDeptInfoByDeptIds
     * query  department&#39;s  information
     * @param idListArg  (required)
     * @return ApiResponse&lt;ListDeptInfoResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListDeptInfoResult> getDeptInfoByDeptIdsWithHttpInfo(IdListArg idListArg) throws ApiException {
        okhttp3.Call call = getDeptInfoByDeptIdsCall(idListArg, null, null);
        Type localVarReturnType = new TypeToken<ListDeptInfoResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * getDeptInfoByDeptIds (asynchronously)
     * query  department&#39;s  information
     * @param idListArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getDeptInfoByDeptIdsAsync(IdListArg idListArg, final ApiCallback<ListDeptInfoResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getDeptInfoByDeptIdsCall(idListArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListDeptInfoResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getDeptsByUserId */
    private okhttp3.Call getDeptsByUserIdCall(IdArg idArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idArg;
        
        // verify the required parameter 'idArg' is set
        if (idArg == null) {
            throw new ApiException("Missing the required parameter 'idArg' when calling getDeptsByUserId(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/employee/departments".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * getDeptsByUserId
     * query  employee&#39;s  departments,no leader info
     * @param idArg  (required)
     * @return ListDeptInfoResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListDeptInfoResult getDeptsByUserId(IdArg idArg) throws ApiException {
        ApiResponse<ListDeptInfoResult> resp = getDeptsByUserIdWithHttpInfo(idArg);
        return resp.getData();
    }

    /**
     * getDeptsByUserId
     * query  employee&#39;s  departments,no leader info
     * @param idArg  (required)
     * @return ApiResponse&lt;ListDeptInfoResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListDeptInfoResult> getDeptsByUserIdWithHttpInfo(IdArg idArg) throws ApiException {
        okhttp3.Call call = getDeptsByUserIdCall(idArg, null, null);
        Type localVarReturnType = new TypeToken<ListDeptInfoResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * getDeptsByUserId (asynchronously)
     * query  employee&#39;s  departments,no leader info
     * @param idArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getDeptsByUserIdAsync(IdArg idArg, final ApiCallback<ListDeptInfoResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getDeptsByUserIdCall(idArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListDeptInfoResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getEmployeesByDeptId */
    private okhttp3.Call getEmployeesByDeptIdCall(IdArg idArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idArg;
        
        // verify the required parameter 'idArg' is set
        if (idArg == null) {
            throw new ApiException("Missing the required parameter 'idArg' when calling getEmployeesByDeptId(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/department/employee".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * getEmployeesByDeptId
     * query  departments&#39;  employees
     * @param idArg  (required)
     * @return ListUserInfoResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListUserInfoResult getEmployeesByDeptId(IdArg idArg) throws ApiException {
        ApiResponse<ListUserInfoResult> resp = getEmployeesByDeptIdWithHttpInfo(idArg);
        return resp.getData();
    }

    /**
     * getEmployeesByDeptId
     * query  departments&#39;  employees
     * @param idArg  (required)
     * @return ApiResponse&lt;ListUserInfoResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListUserInfoResult> getEmployeesByDeptIdWithHttpInfo(IdArg idArg) throws ApiException {
        okhttp3.Call call = getEmployeesByDeptIdCall(idArg, null, null);
        Type localVarReturnType = new TypeToken<ListUserInfoResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * getEmployeesByDeptId (asynchronously)
     * query  departments&#39;  employees
     * @param idArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getEmployeesByDeptIdAsync(IdArg idArg, final ApiCallback<ListUserInfoResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getEmployeesByDeptIdCall(idArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListUserInfoResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getEmployeesByDeptIdList */
    private okhttp3.Call getEmployeesByDeptIdListCall(IdListArg idListArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idListArg;
        
        // verify the required parameter 'idListArg' is set
        if (idListArg == null) {
            throw new ApiException("Missing the required parameter 'idListArg' when calling getEmployeesByDeptIdList(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/department/employee/batch".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * getEmployeesByDeptIdList
     * query  departments&#39;  employees batch
     * @param idListArg  (required)
     * @return MapDeptUserInfoListResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public MapDeptUserInfoListResult getEmployeesByDeptIdList(IdListArg idListArg) throws ApiException {
        ApiResponse<MapDeptUserInfoListResult> resp = getEmployeesByDeptIdListWithHttpInfo(idListArg);
        return resp.getData();
    }

    /**
     * getEmployeesByDeptIdList
     * query  departments&#39;  employees batch
     * @param idListArg  (required)
     * @return ApiResponse&lt;MapDeptUserInfoListResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<MapDeptUserInfoListResult> getEmployeesByDeptIdListWithHttpInfo(IdListArg idListArg) throws ApiException {
        okhttp3.Call call = getEmployeesByDeptIdListCall(idListArg, null, null);
        Type localVarReturnType = new TypeToken<MapDeptUserInfoListResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * getEmployeesByDeptIdList (asynchronously)
     * query  departments&#39;  employees batch
     * @param idListArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getEmployeesByDeptIdListAsync(IdListArg idListArg, final ApiCallback<MapDeptUserInfoListResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getEmployeesByDeptIdListCall(idListArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<MapDeptUserInfoListResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getUserIdsByName */
    private okhttp3.Call getUserIdsByNameCall(OrgNameArg orgNameArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = orgNameArg;
        
        // verify the required parameter 'orgNameArg' is set
        if (orgNameArg == null) {
            throw new ApiException("Missing the required parameter 'orgNameArg' when calling getUserIdsByName(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/employee/name".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * getUserIdsByName
     * 2-all, 1-stop, 0-start
     * @param orgNameArg  (required)
     * @return ListStringResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListStringResult getUserIdsByName(OrgNameArg orgNameArg) throws ApiException {
        ApiResponse<ListStringResult> resp = getUserIdsByNameWithHttpInfo(orgNameArg);
        return resp.getData();
    }

    /**
     * getUserIdsByName
     * 2-all, 1-stop, 0-start
     * @param orgNameArg  (required)
     * @return ApiResponse&lt;ListStringResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListStringResult> getUserIdsByNameWithHttpInfo(OrgNameArg orgNameArg) throws ApiException {
        okhttp3.Call call = getUserIdsByNameCall(orgNameArg, null, null);
        Type localVarReturnType = new TypeToken<ListStringResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * getUserIdsByName (asynchronously)
     * 2-all, 1-stop, 0-start
     * @param orgNameArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getUserIdsByNameAsync(OrgNameArg orgNameArg, final ApiCallback<ListStringResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getUserIdsByNameCall(orgNameArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListStringResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getUserInfoBySupervisorId */
    private okhttp3.Call getUserInfoBySupervisorIdCall(IdArg idPageArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idPageArg;
        
        // verify the required parameter 'idPageArg' is set
        if (idPageArg == null) {
            throw new ApiException("Missing the required parameter 'idPageArg' when calling getUserInfoBySupervisorId(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/leader/employee".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * getUserInfoBySupervisorId
     * query  leader&#39;s  employees
     * @param idPageArg  (required)
     * @return ListUserInfoResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListUserInfoResult getUserInfoBySupervisorId(IdArg idPageArg) throws ApiException {
        ApiResponse<ListUserInfoResult> resp = getUserInfoBySupervisorIdWithHttpInfo(idPageArg);
        return resp.getData();
    }

    /**
     * getUserInfoBySupervisorId
     * query  leader&#39;s  employees
     * @param idPageArg  (required)
     * @return ApiResponse&lt;ListUserInfoResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListUserInfoResult> getUserInfoBySupervisorIdWithHttpInfo(IdArg idPageArg) throws ApiException {
        okhttp3.Call call = getUserInfoBySupervisorIdCall(idPageArg, null, null);
        Type localVarReturnType = new TypeToken<ListUserInfoResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * getUserInfoBySupervisorId (asynchronously)
     * query  leader&#39;s  employees
     * @param idPageArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getUserInfoBySupervisorIdAsync(IdArg idPageArg, final ApiCallback<ListUserInfoResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getUserInfoBySupervisorIdCall(idPageArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListUserInfoResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getUserInfoByUserIds */
    private okhttp3.Call getUserInfoByUserIdsCall(IdListArg idListArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idListArg;
        
        // verify the required parameter 'idListArg' is set
        if (idListArg == null) {
            throw new ApiException("Missing the required parameter 'idListArg' when calling getUserInfoByUserIds(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/employees/info/batch".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * getUserInfoByUserIds
     * 查询汇报对象接口；当没有上级时，leaderId&#x3D;-1，leaderName&#x3D;null
     * @param idListArg  (required)
     * @return ListUserInfoResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListUserInfoResult getUserInfoByUserIds(IdListArg idListArg) throws ApiException {
        ApiResponse<ListUserInfoResult> resp = getUserInfoByUserIdsWithHttpInfo(idListArg);
        return resp.getData();
    }

    /**
     * getUserInfoByUserIds
     * 查询汇报对象接口；当没有上级时，leaderId&#x3D;-1，leaderName&#x3D;null
     * @param idListArg  (required)
     * @return ApiResponse&lt;ListUserInfoResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListUserInfoResult> getUserInfoByUserIdsWithHttpInfo(IdListArg idListArg) throws ApiException {
        okhttp3.Call call = getUserInfoByUserIdsCall(idListArg, null, null);
        Type localVarReturnType = new TypeToken<ListUserInfoResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * getUserInfoByUserIds (asynchronously)
     * 查询汇报对象接口；当没有上级时，leaderId&#x3D;-1，leaderName&#x3D;null
     * @param idListArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getUserInfoByUserIdsAsync(IdListArg idListArg, final ApiCallback<ListUserInfoResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getUserInfoByUserIdsCall(idListArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListUserInfoResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryAllGroup */
    private okhttp3.Call queryAllGroupCall(FilterArg filterArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = filterArg;
        
        // verify the required parameter 'filterArg' is set
        if (filterArg == null) {
            throw new ApiException("Missing the required parameter 'filterArg' when calling queryAllGroup(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryAllGroup
     * query  all  group  info  with  all  fields
     * @param filterArg  (required)
     * @return GroupPageResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public GroupPageResult queryAllGroup(FilterArg filterArg) throws ApiException {
        ApiResponse<GroupPageResult> resp = queryAllGroupWithHttpInfo(filterArg);
        return resp.getData();
    }

    /**
     * queryAllGroup
     * query  all  group  info  with  all  fields
     * @param filterArg  (required)
     * @return ApiResponse&lt;GroupPageResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<GroupPageResult> queryAllGroupWithHttpInfo(FilterArg filterArg) throws ApiException {
        okhttp3.Call call = queryAllGroupCall(filterArg, null, null);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryAllGroup (asynchronously)
     * query  all  group  info  with  all  fields
     * @param filterArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryAllGroupAsync(FilterArg filterArg, final ApiCallback<GroupPageResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryAllGroupCall(filterArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryAllSuperDeptByUserId */
    private okhttp3.Call queryAllSuperDeptByUserIdCall(IdArg idArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idArg;
        
        // verify the required parameter 'idArg' is set
        if (idArg == null) {
            throw new ApiException("Missing the required parameter 'idArg' when calling queryAllSuperDeptByUserId(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/employee/departments/all/id".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryAllSuperDeptByUserId
     * query  employee&#39;s  departments
     * @param idArg  (required)
     * @return ListDeptInfoResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListStringResult queryAllSuperDeptByUserId(IdArg idArg) throws ApiException {
        ApiResponse<ListStringResult> resp = queryAllSuperDeptByUserIdWithHttpInfo(idArg);
        return resp.getData();
    }

    /**
     * queryAllSuperDeptByUserId
     * query  employee&#39;s  departments
     * @param idArg  (required)
     * @return ApiResponse&lt;ListDeptInfoResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListStringResult> queryAllSuperDeptByUserIdWithHttpInfo(IdArg idArg) throws ApiException {
        okhttp3.Call call = queryAllSuperDeptByUserIdCall(idArg, null, null);
        Type localVarReturnType = new TypeToken<ListStringResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryAllSuperDeptByUserId (asynchronously)
     * query  employee&#39;s  departments
     * @param idArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryAllSuperDeptByUserIdAsync(IdArg idArg, final ApiCallback<ListDeptInfoResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryAllSuperDeptByUserIdCall(idArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListDeptInfoResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryDeptByUserBatch */
    private okhttp3.Call queryDeptByUserBatchCall(IdListArg idListArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idListArg;
        
        // verify the required parameter 'idListArg' is set
        if (idListArg == null) {
            throw new ApiException("Missing the required parameter 'idListArg' when calling queryDeptByUserBatch(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/main/dept/batch".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryDeptByUserBatch
     * query main dept  by  userIdList
     * @param idListArg  (required)
     * @return ListMainDeptResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListMainDeptResult queryDeptByUserBatch(IdListArg idListArg) throws ApiException {
        ApiResponse<ListMainDeptResult> resp = queryDeptByUserBatchWithHttpInfo(idListArg);
        return resp.getData();
    }

    /**
     * queryDeptByUserBatch
     * query main dept  by  userIdList
     * @param idListArg  (required)
     * @return ApiResponse&lt;ListMainDeptResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListMainDeptResult> queryDeptByUserBatchWithHttpInfo(IdListArg idListArg) throws ApiException {
        okhttp3.Call call = queryDeptByUserBatchCall(idListArg, null, null);
        Type localVarReturnType = new TypeToken<ListMainDeptResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryDeptByUserBatch (asynchronously)
     * query main dept  by  userIdList
     * @param idListArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryDeptByUserBatchAsync(IdListArg idListArg, final ApiCallback<ListMainDeptResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryDeptByUserBatchCall(idListArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListMainDeptResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryGroupById */
    private okhttp3.Call queryGroupByIdCall(IdArg idArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idArg;
        
        // verify the required parameter 'idArg' is set
        if (idArg == null) {
            throw new ApiException("Missing the required parameter 'idArg' when calling queryGroupById(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/id".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryGroupById
     * query  group info  by  id 
     * @param idArg  (required)
     * @return GroupResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public GroupResult queryGroupById(IdArg idArg) throws ApiException {
        ApiResponse<GroupResult> resp = queryGroupByIdWithHttpInfo(idArg);
        return resp.getData();
    }

    /**
     * queryGroupById
     * query  group info  by  id 
     * @param idArg  (required)
     * @return ApiResponse&lt;GroupResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<GroupResult> queryGroupByIdWithHttpInfo(IdArg idArg) throws ApiException {
        okhttp3.Call call = queryGroupByIdCall(idArg, null, null);
        Type localVarReturnType = new TypeToken<GroupResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryGroupById (asynchronously)
     * query  group info  by  id 
     * @param idArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryGroupByIdAsync(IdArg idArg, final ApiCallback<GroupResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryGroupByIdCall(idArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<GroupResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryGroupByIdList */
    private okhttp3.Call queryGroupByIdListCall(FilterGroupArg filterGroupArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = filterGroupArg;
        
        // verify the required parameter 'filterGroupArg' is set
        if (filterGroupArg == null) {
            throw new ApiException("Missing the required parameter 'filterGroupArg' when calling queryGroupByIdList(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/list".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryGroupByIdList
     * query  group info  by  idList 
     * @param filterGroupArg  (required)
     * @return GroupPageResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public GroupPageResult queryGroupByIdList(FilterGroupArg filterGroupArg) throws ApiException {
        ApiResponse<GroupPageResult> resp = queryGroupByIdListWithHttpInfo(filterGroupArg);
        return resp.getData();
    }

    /**
     * queryGroupByIdList
     * query  group info  by  idList 
     * @param filterGroupArg  (required)
     * @return ApiResponse&lt;GroupPageResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<GroupPageResult> queryGroupByIdListWithHttpInfo(FilterGroupArg filterGroupArg) throws ApiException {
        okhttp3.Call call = queryGroupByIdListCall(filterGroupArg, null, null);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryGroupByIdList (asynchronously)
     * query  group info  by  idList 
     * @param filterGroupArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryGroupByIdListAsync(FilterGroupArg filterGroupArg, final ApiCallback<GroupPageResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryGroupByIdListCall(filterGroupArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryGroupByName */
    private okhttp3.Call queryGroupByNameCall(NameArg nameArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = nameArg;
        
        // verify the required parameter 'nameArg' is set
        if (nameArg == null) {
            throw new ApiException("Missing the required parameter 'nameArg' when calling queryGroupByName(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/name".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryGroupByName
     * query  groups  by  name
     * @param nameArg  (required)
     * @return GroupPageResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public GroupPageResult queryGroupByName(NameArg nameArg) throws ApiException {
        ApiResponse<GroupPageResult> resp = queryGroupByNameWithHttpInfo(nameArg);
        return resp.getData();
    }

    /**
     * queryGroupByName
     * query  groups  by  name
     * @param nameArg  (required)
     * @return ApiResponse&lt;GroupPageResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<GroupPageResult> queryGroupByNameWithHttpInfo(NameArg nameArg) throws ApiException {
        okhttp3.Call call = queryGroupByNameCall(nameArg, null, null);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryGroupByName (asynchronously)
     * query  groups  by  name
     * @param nameArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryGroupByNameAsync(NameArg nameArg, final ApiCallback<GroupPageResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryGroupByNameCall(nameArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryGroupByUserIds */
    private okhttp3.Call queryGroupByUserIdsCall(UserListArg userListArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = userListArg;
        
        // verify the required parameter 'userListArg' is set
        if (userListArg == null) {
            throw new ApiException("Missing the required parameter 'userListArg' when calling queryGroupByUserIds(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/own".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryGroupByUserId
     * query  groups  which  a user  belongs to
     * @param userListArg  (required)
     * @return ListPageResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListPageResult queryGroupByUserIds(UserListArg userListArg) throws ApiException {
        ApiResponse<ListPageResult> resp = queryGroupByUserIdsWithHttpInfo(userListArg);
        return resp.getData();
    }

    /**
     * queryGroupByUserId
     * query  groups  which  a user  belongs to
     * @param userListArg  (required)
     * @return ApiResponse&lt;ListPageResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListPageResult> queryGroupByUserIdsWithHttpInfo(UserListArg userListArg) throws ApiException {
        okhttp3.Call call = queryGroupByUserIdsCall(userListArg, null, null);
        Type localVarReturnType = new TypeToken<ListPageResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryGroupByUserId (asynchronously)
     * query  groups  which  a user  belongs to
     * @param userListArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryGroupByUserIdsAsync(UserListArg userListArg, final ApiCallback<ListPageResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryGroupByUserIdsCall(userListArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListPageResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryGroupByUserKey */
    private okhttp3.Call queryGroupByUserKeyCall(NameArg nameArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = nameArg;
        
        // verify the required parameter 'nameArg' is set
        if (nameArg == null) {
            throw new ApiException("Missing the required parameter 'nameArg' when calling queryGroupByUserKey(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/user/key".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryGroupByUserKey
     * query  groups  by  user  key
     * @param nameArg  (required)
     * @return GroupPageResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public GroupPageResult queryGroupByUserKey(NameArg nameArg) throws ApiException {
        ApiResponse<GroupPageResult> resp = queryGroupByUserKeyWithHttpInfo(nameArg);
        return resp.getData();
    }

    /**
     * queryGroupByUserKey
     * query  groups  by  user  key
     * @param nameArg  (required)
     * @return ApiResponse&lt;GroupPageResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<GroupPageResult> queryGroupByUserKeyWithHttpInfo(NameArg nameArg) throws ApiException {
        okhttp3.Call call = queryGroupByUserKeyCall(nameArg, null, null);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryGroupByUserKey (asynchronously)
     * query  groups  by  user  key
     * @param nameArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryGroupByUserKeyAsync(NameArg nameArg, final ApiCallback<GroupPageResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryGroupByUserKeyCall(nameArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryGroupMem */
    private okhttp3.Call queryGroupMemCall(GroupArg groupArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = groupArg;
        
        // verify the required parameter 'groupArg' is set
        if (groupArg == null) {
            throw new ApiException("Missing the required parameter 'groupArg' when calling queryGroupMem(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/mem".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryGroupMem
     * query  a  group&#39;s  members
     * @param groupArg  (required)
     * @return ListPageResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListPageResult queryGroupMem(GroupArg groupArg) throws ApiException {
        ApiResponse<ListPageResult> resp = queryGroupMemWithHttpInfo(groupArg);
        return resp.getData();
    }

    /**
     * queryGroupMem
     * query  a  group&#39;s  members
     * @param groupArg  (required)
     * @return ApiResponse&lt;ListPageResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListPageResult> queryGroupMemWithHttpInfo(GroupArg groupArg) throws ApiException {
        okhttp3.Call call = queryGroupMemCall(groupArg, null, null);
        Type localVarReturnType = new TypeToken<ListPageResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryGroupMem (asynchronously)
     * query  a  group&#39;s  members
     * @param groupArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryGroupMemAsync(GroupArg groupArg, final ApiCallback<ListPageResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryGroupMemCall(groupArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListPageResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryGroupMemBatch */
    private okhttp3.Call queryGroupMemBatchCall(GroupIdListArg groupIdListArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = groupIdListArg;
        
        // verify the required parameter 'groupIdListArg' is set
        if (groupIdListArg == null) {
            throw new ApiException("Missing the required parameter 'groupIdListArg' when calling queryGroupMemBatch(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/mem/batch".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryGroupMemBatch
     * query groups&#39;  member  batch
     * @param groupIdListArg  (required)
     * @return ListStringResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListStringResult queryGroupMemBatch(GroupIdListArg groupIdListArg) throws ApiException {
        ApiResponse<ListStringResult> resp = queryGroupMemBatchWithHttpInfo(groupIdListArg);
        return resp.getData();
    }

    /**
     * queryGroupMemBatch
     * query groups&#39;  member  batch
     * @param groupIdListArg  (required)
     * @return ApiResponse&lt;ListStringResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListStringResult> queryGroupMemBatchWithHttpInfo(GroupIdListArg groupIdListArg) throws ApiException {
        okhttp3.Call call = queryGroupMemBatchCall(groupIdListArg, null, null);
        Type localVarReturnType = new TypeToken<ListStringResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryGroupMemBatch (asynchronously)
     * query groups&#39;  member  batch
     * @param groupIdListArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryGroupMemBatchAsync(GroupIdListArg groupIdListArg, final ApiCallback<ListStringResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryGroupMemBatchCall(groupIdListArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListStringResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryLeaderByUserIdBatch */
    private okhttp3.Call queryLeaderByUserIdBatchCall(IdListArg idListArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = idListArg;
        
        // verify the required parameter 'idListArg' is set
        if (idListArg == null) {
            throw new ApiException("Missing the required parameter 'idListArg' when calling queryLeaderByUserIdBatch(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/leader/batch".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryLeaderByUserIdBatch
     * query  employees&#39;s  leader
     * @param idListArg  (required)
     * @return UserInfoResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public UserInfoResult queryLeaderByUserIdBatch(IdListArg idListArg) throws ApiException {
        ApiResponse<UserInfoResult> resp = queryLeaderByUserIdBatchWithHttpInfo(idListArg);
        return resp.getData();
    }

    /**
     * queryLeaderByUserIdBatch
     * query  employees&#39;s  leader
     * @param idListArg  (required)
     * @return ApiResponse&lt;UserInfoResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<UserInfoResult> queryLeaderByUserIdBatchWithHttpInfo(IdListArg idListArg) throws ApiException {
        okhttp3.Call call = queryLeaderByUserIdBatchCall(idListArg, null, null);
        Type localVarReturnType = new TypeToken<UserInfoResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryLeaderByUserIdBatch (asynchronously)
     * query  employees&#39;s  leader
     * @param idListArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryLeaderByUserIdBatchAsync(IdListArg idListArg, final ApiCallback<UserInfoResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryLeaderByUserIdBatchCall(idListArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<UserInfoResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryStartGroup */
    private okhttp3.Call queryStartGroupCall(BaseArg baseArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = baseArg;
        
        // verify the required parameter 'baseArg' is set
        if (baseArg == null) {
            throw new ApiException("Missing the required parameter 'baseArg' when calling queryStartGroup(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/start".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryStartGroup
     * query  start  group id 
     * @param baseArg  (required)
     * @return ListPageResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListPageResult queryStartGroup(BaseArg baseArg) throws ApiException {
        ApiResponse<ListPageResult> resp = queryStartGroupWithHttpInfo(baseArg);
        return resp.getData();
    }

    /**
     * queryStartGroup
     * query  start  group id 
     * @param baseArg  (required)
     * @return ApiResponse&lt;ListPageResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListPageResult> queryStartGroupWithHttpInfo(BaseArg baseArg) throws ApiException {
        okhttp3.Call call = queryStartGroupCall(baseArg, null, null);
        Type localVarReturnType = new TypeToken<ListPageResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryStartGroup (asynchronously)
     * query  start  group id 
     * @param baseArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryStartGroupAsync(BaseArg baseArg, final ApiCallback<ListPageResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryStartGroupCall(baseArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListPageResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryStartGroupInfo */
    private okhttp3.Call queryStartGroupInfoCall(BaseArg baseArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = baseArg;
        
        // verify the required parameter 'baseArg' is set
        if (baseArg == null) {
            throw new ApiException("Missing the required parameter 'baseArg' when calling queryStartGroupInfo(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/start/list".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryStartGroupInfo
     * query  start  group info  with  all  fields 
     * @param baseArg  (required)
     * @return GroupPageResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public GroupPageResult queryStartGroupInfo(BaseArg baseArg) throws ApiException {
        ApiResponse<GroupPageResult> resp = queryStartGroupInfoWithHttpInfo(baseArg);
        return resp.getData();
    }

    /**
     * queryStartGroupInfo
     * query  start  group info  with  all  fields 
     * @param baseArg  (required)
     * @return ApiResponse&lt;GroupPageResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<GroupPageResult> queryStartGroupInfoWithHttpInfo(BaseArg baseArg) throws ApiException {
        okhttp3.Call call = queryStartGroupInfoCall(baseArg, null, null);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryStartGroupInfo (asynchronously)
     * query  start  group info  with  all  fields 
     * @param baseArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryStartGroupInfoAsync(BaseArg baseArg, final ApiCallback<GroupPageResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryStartGroupInfoCall(baseArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<GroupPageResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryUserIdByDeptKey */
    private okhttp3.Call queryUserIdByDeptKeyCall(DeptKeyArg deptKeyArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = deptKeyArg;
        
        // verify the required parameter 'deptKeyArg' is set
        if (deptKeyArg == null) {
            throw new ApiException("Missing the required parameter 'deptKeyArg' when calling queryUserIdByDeptKey(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/user/dept/key".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryUserIdByDeptKey
     * query userId  in  the  depts  by  key
     * @param deptKeyArg  (required)
     * @return ListStringResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ListStringResult queryUserIdByDeptKey(DeptKeyArg deptKeyArg) throws ApiException {
        ApiResponse<ListStringResult> resp = queryUserIdByDeptKeyWithHttpInfo(deptKeyArg);
        return resp.getData();
    }

    /**
     * queryUserIdByDeptKey
     * query userId  in  the  depts  by  key
     * @param deptKeyArg  (required)
     * @return ApiResponse&lt;ListStringResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<ListStringResult> queryUserIdByDeptKeyWithHttpInfo(DeptKeyArg deptKeyArg) throws ApiException {
        okhttp3.Call call = queryUserIdByDeptKeyCall(deptKeyArg, null, null);
        Type localVarReturnType = new TypeToken<ListStringResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryUserIdByDeptKey (asynchronously)
     * query userId  in  the  depts  by  key
     * @param deptKeyArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryUserIdByDeptKeyAsync(DeptKeyArg deptKeyArg, final ApiCallback<ListStringResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryUserIdByDeptKeyCall(deptKeyArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<ListStringResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryUserIsInGroup */
    private okhttp3.Call queryUserIsInGroupCall(IdArg groupArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = groupArg;
        
        // verify the required parameter 'groupArg' is set
        if (groupArg == null) {
            throw new ApiException("Missing the required parameter 'groupArg' when calling queryUserIsInGroup(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/belong".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * queryUserIsInGroup
     * whether  a user  belongs to  a  group
     * @param groupArg  (required)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult queryUserIsInGroup(IdArg groupArg) throws ApiException {
        ApiResponse<BaseResult> resp = queryUserIsInGroupWithHttpInfo(groupArg);
        return resp.getData();
    }

    /**
     * queryUserIsInGroup
     * whether  a user  belongs to  a  group
     * @param groupArg  (required)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> queryUserIsInGroupWithHttpInfo(IdArg groupArg) throws ApiException {
        okhttp3.Call call = queryUserIsInGroupCall(groupArg, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * queryUserIsInGroup (asynchronously)
     * whether  a user  belongs to  a  group
     * @param groupArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryUserIsInGroupAsync(IdArg groupArg, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryUserIsInGroupCall(groupArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }

    /**
     * queryGroupMultiById
     * query  group multi info by id
     * @param MultiArg  (required)
     * @return GroupResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult queryGroupMultiById(MultiArg MultiArg) throws ApiException {
        ApiResponse<BaseResult> resp = queryGroupMultiByIdWithHttpInfo(MultiArg);
        return resp.getData();
    }

    private ApiResponse<BaseResult> queryGroupMultiByIdWithHttpInfo(MultiArg MultiArg) throws ApiException {
        okhttp3.Call call = queryGroupMultiByIdCall(MultiArg, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    private Call queryGroupMultiByIdCall(MultiArg MultiArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = MultiArg;

        // verify the required parameter 'idArg' is set
        if (MultiArg == null) {
            throw new ApiException("Missing the required parameter 'MultiArg' when calling queryGroupMultiByIdCall(Async)");
        }


        // create path and map variables
        String localVarPath = "/org/group/multi".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                                           .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                                           .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    public UserIdGroupPageResult queryUserGroupByUserId(UserGroupArg userGroupArg) throws ApiException {
        ApiResponse<UserIdGroupPageResult> resp = queryUserGroupByUserIdWithHttpInfo(userGroupArg);
        return resp.getData();
    }

    private ApiResponse<UserIdGroupPageResult> queryUserGroupByUserIdWithHttpInfo(UserGroupArg userGroupArg) throws ApiException {
        okhttp3.Call call = queryUserGroupByUserIdCall(userGroupArg, null, null);
        Type localVarReturnType = new TypeToken<UserIdGroupPageResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    private Call queryUserGroupByUserIdCall(UserGroupArg userGroupArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = userGroupArg;

        // verify the required parameter 'idArg' is set
        if (userGroupArg == null) {
            throw new ApiException("Missing the required parameter 'userGroupArg' when calling queryUserGroupByUserIdCall(Async)");
        }


        // create path and map variables
        String localVarPath = "/org/group/userId".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                                           .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                                           .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }
}
