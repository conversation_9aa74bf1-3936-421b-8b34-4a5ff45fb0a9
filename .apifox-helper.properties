# EasyApi Generic Configuration for Custom Framework
# æ¯æå¤ç§èªå®ä¹æ¡æ¶çéç½®ï¼ObjectValidRuleServiceãStandardListControllerãStandardAddAction

# ========================================
# ç±»çº§å«éç½® (Class Level Configuration)
# ========================================
class.prefix.path=/API/
# å¤æ­ç±»æ¯å¦åå«API - æ¯æå¤ç§æ³¨è§£åç±»åæ¨¡å¼
generic.class.has.api=@com.facishare.paas.appframework.core.annotation.ServiceModule
generic.class.has.api=groovy:```
def className = it.name()
// æ¯æControllerç±»åï¼éç¨æ¨¡å¼ï¼
if(className.contains("Controller") &&
   (className.contains("Standard"))) {
    return true
}
// æ¯æActionç±»åï¼éç¨æ¨¡å¼ï¼
if(className.contains("Action") &&
   (className.contains("Standard"))) {
    return true
}
return false
```

# è·åç±»çè·¯å¾ - æ ¹æ®ä¸åç±»åçæä¸åçè·¯å¾
generic.path[@com.facishare.paas.appframework.core.annotation.ServiceModule]=groovy:```
def serviceModuleAnn = it.annMap("com.facishare.paas.appframework.core.annotation.ServiceModule")
if(serviceModuleAnn && serviceModuleAnn.containsKey("value")){
    return "/v1/rest/object/" + serviceModuleAnn["value"]
}
return ""
```

generic.path=groovy:```
def className = it.name()
def simpleClassName = className.substring(className.lastIndexOf('.') + 1)

// Controllerç±»å - å¯¹åº /API/v1/rest/object/{objectApiName}/controller/{MethodName}
if(className.contains("Controller") &&
   (className.contains("Standard"))) {
    def methodName = simpleClassName
        .replace("Standard", "")
        .replace("Controller", "")
    return "/v1/rest/object/{objectApiName}/controller/" + methodName
}

// Actionç±»å - å¯¹åº /API/v1/rest/object/{objectApiName}/action/{MethodName}
if(className.contains("Action") &&
   (className.contains("Standard"))) {
    def methodName = simpleClassName
        .replace("Standard", "")
        .replace("Action", "")
    return "/v1/rest/object/{objectApiName}/action/" + methodName
}

return ""
```

# ========================================
# æ¹æ³çº§å«éç½® (Method Level Configuration)
# ========================================

# å¤æ­æ¹æ³æ¯å¦åå«API - æ¯æå¤ç§æ¹æ³è¯å«æ¨¡å¼
generic.method.has.api=@com.facishare.paas.appframework.core.annotation.ServiceMethod
generic.method.has.api=groovy:```
def className = it.containingClass().name()
def methodName = it.name()

// ServiceModuleç±»çServiceMethodæ¹æ³
if(it.annMap("com.facishare.paas.appframework.core.annotation.ServiceMethod")) {
    return true
}

// Controllerç±»çä¸»è¦æ¹æ³ï¼éç¨æ¨¡å¼ï¼
if(className.contains("Controller") &&
   (className.contains("Standard"))) {
    // éå¸¸è¿äºç±»åªæä¸ä¸ªä¸»è¦çserviceæ¹æ³ï¼æèç»§æ¿çæ¹æ³
    return methodName.equals("doService")
}

// Actionç±»çä¸»è¦æ¹æ³ï¼éç¨æ¨¡å¼ï¼
if(className.contains("Action") &&
   (className.contains("Standard"))) {
    // éå¸¸è¿äºç±»åªæä¸ä¸ªä¸»è¦çactæ¹æ³ï¼æèç»§æ¿çæ¹æ³
    return methodName.equals("doAct")
}

return false
```

# è·åæ¹æ³çè·¯å¾ - æ ¹æ®ä¸åç±»åçæä¸åçè·¯å¾
generic.path[@com.facishare.paas.appframework.core.annotation.ServiceMethod]=groovy:```
def serviceMethodAnn = it.annMap("com.facishare.paas.appframework.core.annotation.ServiceMethod")
if(serviceMethodAnn && serviceMethodAnn.containsKey("value")){
    return "/service/" + serviceMethodAnn["value"]
}
return ""
```

# HTTPæ¹æ³éç½® - æ ¹æ®ä¸åæ¡æ¶è®¾ç½®ä¸åçHTTPæ¹æ³
generic.http.method[@com.facishare.paas.appframework.core.annotation.ServiceMethod]=POST

generic.http.method=groovy:```
def className = it.containingClass().name()

// ServiceModuleç±»é»è®¤ä½¿ç¨POST
if(it.annMap("com.facishare.paas.appframework.core.annotation.ServiceModule")) {
    return "POST"
}

// ControlleråActionç±»é½ä½¿ç¨POSTï¼éç¨æ¨¡å¼ï¼
if(className.contains("Controller") || className.contains("Action")) {
    return "POST"
}

return "POST"
```

# ========================================
# åæ°çº§å«éç½® (Parameter Level Configuration)
# ========================================

# åæ°åç§°è·å - ä½¿ç¨åæ°çå®éåç§°
generic.param.name=groovy:```
return it.name()
```
generic.param.as.path.var=groovy:```
def className = it.containingClass().name()
if((className.contains("Controller") || className.contains("Action")) &&
   (className.contains("Standard"))) {
    return true
}
return false
```
generic.param.path.var=objectApiName
# åæ°ä½ä¸ºJSON Bodyå¤ç - å¯¹äºå¤æå¯¹è±¡ç±»åçåæ°
generic.param.as.json.body=groovy:```

// é»è®¤å¤æå¯¹è±¡ä½ä¸ºJSON body
return true
```

# ========================================
# ååºéç½® (Response Configuration)
# ========================================

# æ¹æ³è¿åå¼å¤ç - è¯å«Resultç±»åçè¿åå¼
method.return.main=groovy:```
def returnType = it.returnType()
def typeName = returnType.name()

// æ£æ¥æ¯å¦ä¸ºResultç±»å
if(typeName.endsWith(".Result")) {
    return returnType
}

return returnType
```

# ========================================
# ææ¡£å¢å¼ºéç½® (Documentation Enhancement)
# ========================================

# ç±»ææ¡£æè¿°
class.doc=groovy:```
def className = it.name()
def simpleClassName = className.substring(className.lastIndexOf('.') + 1)
def serviceModuleAnn = it.annMap("com.facishare.paas.appframework.core.annotation.ServiceModule")

// ServiceModuleç±»å
if(serviceModuleAnn && serviceModuleAnn.containsKey("value")){
    return "æå¡æ¨¡å: " + serviceModuleAnn["value"]
}

// Controllerç±»åï¼éç¨æ¨¡å¼ï¼
if(className.contains("Controller") &&
   (className.contains("Standard"))) {
    def methodName = simpleClassName
        .replace("Standard", "")
        .replace("Controller", "")
        .replace("Abstract", "")
    return "å¯¹è±¡" + methodName + "æ§å¶å¨"
}

// Actionç±»åï¼éç¨æ¨¡å¼ï¼
if(className.contains("Action") &&
   (className.contains("Standard"))) {
    def methodName = simpleClassName
        .replace("Standard", "")
        .replace("Action", "")
        .replace("Abstract", "")
    return "å¯¹è±¡" + methodName + "æä½"
}

return it.doc()?.text ?: "èªå®ä¹æå¡æ¨¡å"
```

# æ¹æ³ææ¡£æè¿°
method.doc=groovy:```
def className = it.containingClass().name()
def simpleClassName = className.substring(className.lastIndexOf('.') + 1)
def serviceMethodAnn = it.annMap("com.facishare.paas.appframework.core.annotation.ServiceMethod")
// ServiceMethodç±»å
if(serviceMethodAnn && serviceMethodAnn.containsKey("value")){
    def methodName = serviceMethodAnn["value"]
    return methodName
}

// Controllerç±»åï¼éç¨æ¨¡å¼ï¼
if(className.contains("Controller") &&
   (className.contains("Standard"))) {
    def methodName = simpleClassName
        .replace("Standard", "")
        .replace("Controller", "")
        .replace("Abstract", "")

    // æ ¹æ®æ¹æ³åçææè¿°
    switch(methodName.toLowerCase()) {
        case "list":
            return "è·åå¯¹è±¡æ°æ®åè¡¨ - æ¯æåé¡µãç­éãæåºç­åè½"
        case "view":
        case "detail":
            return "è·åå¯¹è±¡è¯¦ææ°æ®"
        case "header":
            return "è·åé¡µé¢å¤´é¨ä¿¡æ¯"
        default:
            return "æ§è¡å¯¹è±¡" + methodName + "æä½"
    }
}

// Actionç±»åï¼éç¨æ¨¡å¼ï¼
if(className.contains("Action") &&
   (className.contains("Standard"))) {
    def methodName = simpleClassName
        .replace("Standard", "")
        .replace("Action", "")
        .replace("Abstract", "")

    // æ ¹æ®æ¹æ³åçææè¿°
    switch(methodName.toLowerCase()) {
        case "add":
        case "create":
            return "åå»ºæ°çå¯¹è±¡æ°æ® - æ¯æä¸»å¯¹è±¡åä»å¯¹è±¡çåå»º"
        case "edit":
        case "update":
            return "æ´æ°å¯¹è±¡æ°æ® - æ¯æä¸»å¯¹è±¡åä»å¯¹è±¡çæ´æ°"
        case "delete":
            return "å é¤å¯¹è±¡æ°æ®"
        case "view":
            return "æ¥çå¯¹è±¡è¯¦æ"
        default:
            return "æ§è¡å¯¹è±¡" + methodName + "æä½"
    }
}

return it.doc()?.text ?: "èªå®ä¹æå¡æ¹æ³"
```

# ========================================
# è·¯å¾åæ°éç½® (Path Variable Configuration)
# ========================================

# å¤æ­åæ°æ¯å¦ä¸ºè·¯å¾åé
generic.param.as.path.var=groovy:```
def paramName = it.name()
def className = it.containingClass().name()

// å¯¹äºControlleråActionç±»ï¼æ£æ¥æ¯å¦æobjectApiNameåæ°
if((className.contains("Controller") || className.contains("Action")) &&
   className.contains("Standard")) {
    // å¦æåæ°ååå«objectApiNameç¸å³çåç§°ï¼åä½ä¸ºè·¯å¾åé
    if(paramName.contains("objectApiName") ||
       paramName.contains("objectDescribeApiName") ||
       paramName.equals("apiName")) {
        return true
    }
}

return false
```

# è·åè·¯å¾åéçåç§°
generic.param.path.var=groovy:```
def paramName = it.name()
def className = it.containingClass().name()

// å¯¹äºControlleråActionç±»çè·¯å¾åæ°
if((className.contains("Controller") || className.contains("Action")) &&
   className.contains("Standard")) {
    // ç»ä¸æ å°ä¸ºobjectApiName
    if(paramName.contains("objectApiName") ||
       paramName.contains("objectDescribeApiName") ||
       paramName.equals("apiName")) {
        return "objectApiName"
    }
}

return paramName
```

# ========================================
# åæ°ææ¡£éç½® (Parameter Documentation)
# ========================================

# åæ°ææ¡£æè¿°
param.doc=groovy:```
def paramName = it.name()
def paramType = it.getType().name()
def className = it.containingClass().name()
def simpleClassName = className.substring(className.lastIndexOf('.') + 1)

// ä¸ºå¸¸è§åæ°æä¾é»è®¤æè¿°
if(paramName.equals("arg")) {
    // Controllerç±»ååæ°æè¿°
    if(className.contains("Controller") &&
       (className.contains("Standard"))) {
        def methodName = simpleClassName
            .replace("Standard", "")
            .replace("Controller", "")
        return methodName + "æä½åæ° - åå«ç¸å³éç½®åæ°æ®"
    }
    // Actionç±»ååæ°æè¿°
    else if(className.contains("Action") &&
            (className.contains("Standard"))) {
        def methodName = simpleClassName
            .replace("Standard", "")
            .replace("Action", "")
        return methodName + "æä½åæ° - åå«å¯¹è±¡æ°æ®åç¸å³éç½®"
    }
    else {
        return "è¯·æ±åæ°å¯¹è±¡"
    }
} else if(paramName.equals("context")) {
    return "æå¡ä¸ä¸æï¼åå«ç¨æ·ä¿¡æ¯ãç§æ·ä¿¡æ¯ç­"
} else if(paramName.equals("body")) {
    return "è¯·æ±ä½æ°æ®"
} else if(paramName.equals("requestContext")) {
    return "è¯·æ±ä¸ä¸æä¿¡æ¯"
} else {
    return it.doc()?.text ?: paramName
}
```

# ========================================
# å¶ä»éç½® (Other Configurations)
# ========================================

# å¿½ç¥æäºåæ°ç±»å
param.ignore=groovy:```
def paramType = it.getType().name()
def paramName = it.name()

// å¿½ç¥ServiceContextç±»åçåæ°ï¼å ä¸ºå®éå¸¸ç±æ¡æ¶èªå¨æ³¨å¥
if(paramType.contains("ServiceContext") || paramType.contains("RequestContext")) {
    return true
}

// å¿½ç¥æäºæ¡æ¶åé¨åæ°
if(paramName.equals("requestContext") || paramName.equals("context")) {
    return true
}

return false
```
param.type=form

# APIåç»
api.tag=groovy:```
def className = it.name()
def serviceModuleAnn = it.annMap("com.facishare.paas.appframework.core.annotation.ServiceModule")

// ServiceModuleç±»å
if(serviceModuleAnn && serviceModuleAnn.containsKey("value")){
    return serviceModuleAnn["value"] + "æå¡"
}

// Controllerç±»åï¼éç¨æ¨¡å¼ï¼
if(className.contains("Controller") &&
   (className.contains("Standard"))) {
    return "å¯¹è±¡æ§å¶å¨æ¥å£"
}

// Actionç±»åï¼éç¨æ¨¡å¼ï¼
if(className.contains("Action") &&
   (className.contains("Standard"))) {
    return "å¯¹è±¡æä½æ¥å£"
}

return "èªå®ä¹æå¡"
```

# ========================================
# è¯·æ±å¤´éç½® (Header Configuration)
# ========================================
# ä¸ºControlleråActionç±»æ·»å å¸¸ç¨è¯·æ±å¤´ï¼éç¨æ¨¡å¼ï¼
# æ ¹æ®EasyApiææ¡£ï¼method.additional.headeråºè¯¥è¿ååä¸ªheaderå¯¹è±¡ï¼èä¸æ¯æ°ç»
# éè¦ä¸ºæ¯ä¸ªheaderåç¬éç½®
# x-fs-employee-id åå·¥ID
method.additional.header={name: "x-fs-employee-id", value: "1000", desc: "åå·¥ID", required: true, example: "1000"}
# x-fs-enterprise-id ä¼ä¸ID
method.additional.header={name: "x-fs-enterprise-id", value: "74255", desc: "ä¼ä¸ID", required: true, example: "74255"}
# x-fs-ei ä¼ä¸æ è¯
method.additional.header={name: "x-fs-ei", value: "74255", desc: "ä¼ä¸æ è¯", required: false, example: "74255"}
# x-fs-userinfo ç¨æ·ä¿¡æ¯
method.additional.header={name: "x-fs-userinfo", value: "1000", desc: "ç¨æ·ä¿¡æ¯", required: false, example: "1000"}
# Content-Type åå®¹ç±»å
method.additional.header={name: "Content-Type", value: "application/json", desc: "åå®¹ç±»å", required: true, example: "application/json"}


method.return[#response]=groovy: "com.facishare.paas.appframework.jaxrs.model.RestAPIResult<" +  helper.resolveLink(it.doc("response")) +">"