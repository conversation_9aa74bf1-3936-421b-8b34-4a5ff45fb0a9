package com.facishare.paas.appframework.privilege;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.UpdateFunc;
import com.facishare.paas.appframework.privilege.dto.UpdateFuncCodeRoles;
import com.facishare.paas.appframework.privilege.util.ActionCodeConvertUtil;
import com.facishare.paas.appframework.privilege.util.AuthContextExt;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试UserDefinedButtonServiceImpl类的自定义按钮权限管理功能
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class UserDefinedButtonServiceImplTest {

    @Mock
    private FunctionPrivilegeProxy functionPrivilegeProxy;

    @Mock
    private FunctionPrivilegeService functionPrivilegeService;

    @InjectMocks
    private UserDefinedButtonServiceImpl userDefinedButtonService;

    private User testUser;
    private String describeApiName;
    private String buttonApiName;
    private String buttonName;
    private List<String> roles;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
                .tenantId("123456")
                .userId("78910")
                .build();
        describeApiName = "PriceBookObj";
        buttonApiName = "Add";
        buttonName = "添加按钮";
        roles = Lists.newArrayList("00000000000000000000000000000002", "00000000000000000000000000000009");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createUserDefinedButton方法，正常创建自定义按钮
     */
    @Test
    @DisplayName("正常场景 - 测试createUserDefinedButton方法")
    void testCreateUserDefinedButton_NormalCase() {
        try (MockedStatic<ObjectAction> objectActionMock = mockStatic(ObjectAction.class)) {

            // 配置Mock行为
            objectActionMock.when(() -> ObjectAction.getByButtonApiName(buttonApiName)).thenReturn(ObjectAction.UNKNOWN_ACTION);

            // 执行被测试方法
            Boolean result = userDefinedButtonService.createUserDefinedButton(testUser, describeApiName, buttonApiName, buttonName, roles);

            // 验证结果
            assertTrue(result);
            verify(functionPrivilegeService).createFuncCode(testUser, describeApiName, buttonApiName, buttonName);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteUserDefinedButton方法，正常删除自定义按钮
     */
    @Test
    @DisplayName("正常场景 - 测试deleteUserDefinedButton方法")
    void testDeleteUserDefinedButton_NormalCase() {
        // 执行被测试方法
        Boolean result = userDefinedButtonService.deleteUserDefinedButton(testUser, describeApiName, buttonApiName);

        // 验证结果
        assertTrue(result);
        verify(functionPrivilegeService).deleteUserDefinedActionCode(testUser, describeApiName, buttonApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getHavePrivilegeRolesByUserDefinedButton方法，正常获取拥有权限的角色
     */
    @Test
    @DisplayName("正常场景 - 测试getHavePrivilegeRolesByUserDefinedButton方法")
    void testGetHavePrivilegeRolesByUserDefinedButton_NormalCase() {
        // 准备测试数据
        List<String> expectedRoles = Lists.newArrayList("role1", "role2", "role3");

        // 配置Mock行为
        when(functionPrivilegeService.getHavePrivilegeRolesByActionCode(testUser, describeApiName, buttonApiName)).thenReturn(expectedRoles);

        // 执行被测试方法
        List<String> result = userDefinedButtonService.getHavePrivilegeRolesByUserDefinedButton(testUser, describeApiName, buttonApiName);

        // 验证结果
        assertEquals(expectedRoles, result);
        verify(functionPrivilegeService).getHavePrivilegeRolesByActionCode(testUser, describeApiName, buttonApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateUserDefinedButtonPrivilegeRoles方法，正常更新按钮权限角色
     */
    @Test
    @DisplayName("正常场景 - 测试updateUserDefinedButtonPrivilegeRoles方法")
    void testUpdateUserDefinedButtonPrivilegeRoles_NormalCase() {
        try (MockedStatic<ActionCodeConvertUtil> actionCodeMock = mockStatic(ActionCodeConvertUtil.class);
             MockedStatic<AuthContextExt> authContextMock = mockStatic(AuthContextExt.class);
             MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class)) {

            // 准备测试数据
            List<String> addRoles = Lists.newArrayList("role1", "role2");
            List<String> delRoles = Lists.newArrayList("role3");
            String funcCode = "PriceBookObj_Add";
            Map<String, String> headers = new HashMap<>();

            UpdateFuncCodeRoles.Result mockResult = mock(UpdateFuncCodeRoles.Result.class);
            when(mockResult.isSuccess()).thenReturn(true);

            // 配置Mock行为
            actionCodeMock.when(() -> ActionCodeConvertUtil.convert2FuncCode(describeApiName, buttonApiName)).thenReturn(funcCode);
            authContextMock.when(() -> AuthContextExt.getAppId(testUser)).thenReturn("testAppId");
            authContextMock.when(() -> AuthContextExt.buildProperties(testUser)).thenReturn(new HashMap<>());
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn("INTERNAL");

            try (MockedStatic<FunctionPrivilegeProxy.HeaderUtil> headerUtilMock = mockStatic(FunctionPrivilegeProxy.HeaderUtil.class)) {
                headerUtilMock.when(() -> FunctionPrivilegeProxy.HeaderUtil.buildHeaders(testUser.getTenantId())).thenReturn(headers);
                when(functionPrivilegeProxy.updateFuncCodeRoles(any(UpdateFuncCodeRoles.Arg.class), eq(headers))).thenReturn(mockResult);

                // 执行被测试方法
                Boolean result = userDefinedButtonService.updateUserDefinedButtonPrivilegeRoles(testUser, describeApiName, buttonApiName, addRoles, delRoles);

                // 验证结果
                assertTrue(result);
                verify(functionPrivilegeProxy).updateFuncCodeRoles(any(UpdateFuncCodeRoles.Arg.class), eq(headers));
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateFuncButtonName方法，正常更新按钮名称
     */
    @Test
    @DisplayName("正常场景 - 测试updateFuncButtonName方法")
    void testUpdateFuncButtonName_NormalCase() {
        try (MockedStatic<ActionCodeConvertUtil> actionCodeMock = mockStatic(ActionCodeConvertUtil.class);
             MockedStatic<AuthContextExt> authContextMock = mockStatic(AuthContextExt.class);
             MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class)) {

            // 准备测试数据
            String funcCode = "PriceBookObj_Add";
            Map<String, String> headers = new HashMap<>();

            UpdateFunc.Result mockResult = mock(UpdateFunc.Result.class);
            when(mockResult.isSuccess()).thenReturn(true);

            // 配置Mock行为
            actionCodeMock.when(() -> ActionCodeConvertUtil.convert2FuncCode(describeApiName, buttonApiName)).thenReturn(funcCode);
            authContextMock.when(() -> AuthContextExt.getAppId(testUser)).thenReturn("testAppId");
            authContextMock.when(() -> AuthContextExt.buildProperties(testUser)).thenReturn(new HashMap<>());
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn("INTERNAL");

            try (MockedStatic<FunctionPrivilegeProxy.HeaderUtil> headerUtilMock = mockStatic(FunctionPrivilegeProxy.HeaderUtil.class)) {
                headerUtilMock.when(() -> FunctionPrivilegeProxy.HeaderUtil.buildHeaders(testUser.getTenantId())).thenReturn(headers);
                when(functionPrivilegeProxy.updateFunc(any(UpdateFunc.Arg.class), eq(headers))).thenReturn(mockResult);

                // 执行被测试方法
                Boolean result = userDefinedButtonService.updateFuncButtonName(testUser, describeApiName, buttonApiName, buttonName);

                // 验证结果
                assertTrue(result);
                verify(functionPrivilegeProxy).updateFunc(any(UpdateFunc.Arg.class), eq(headers));
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateFuncButtonName方法，更新失败场景
     */
    @Test
    @DisplayName("异常场景 - 测试updateFuncButtonName方法更新失败")
    void testUpdateFuncButtonNameError_UpdateFailed() {
        try (MockedStatic<ActionCodeConvertUtil> actionCodeMock = mockStatic(ActionCodeConvertUtil.class);
             MockedStatic<AuthContextExt> authContextMock = mockStatic(AuthContextExt.class);
             MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class);
             MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {

            // 准备测试数据
            String funcCode = "PriceBookObj_Add";
            Map<String, String> headers = new HashMap<>();

            UpdateFunc.Result mockResult = mock(UpdateFunc.Result.class);
            when(mockResult.isSuccess()).thenReturn(false);

            // 配置Mock行为
            actionCodeMock.when(() -> ActionCodeConvertUtil.convert2FuncCode(describeApiName, buttonApiName)).thenReturn(funcCode);
            authContextMock.when(() -> AuthContextExt.getAppId(testUser)).thenReturn("testAppId");
            authContextMock.when(() -> AuthContextExt.buildProperties(testUser)).thenReturn(new HashMap<>());
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn("INTERNAL");
            i18nMock.when(() -> I18N.text(I18NKey.UPDATE_CUSTOM_BUTTON_FAIL)).thenReturn("更新自定义按钮失败");

            try (MockedStatic<FunctionPrivilegeProxy.HeaderUtil> headerUtilMock = mockStatic(FunctionPrivilegeProxy.HeaderUtil.class)) {
                headerUtilMock.when(() -> FunctionPrivilegeProxy.HeaderUtil.buildHeaders(testUser.getTenantId())).thenReturn(headers);
                when(functionPrivilegeProxy.updateFunc(any(UpdateFunc.Arg.class), eq(headers))).thenReturn(mockResult);

                // 执行并验证异常
                PermissionError exception = assertThrows(PermissionError.class, () -> {
                    userDefinedButtonService.updateFuncButtonName(testUser, describeApiName, buttonApiName, buttonName);
                });

                // 验证异常信息
                assertEquals("更新自定义按钮失败", exception.getMessage());
                verify(functionPrivilegeProxy).updateFunc(any(UpdateFunc.Arg.class), eq(headers));
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createUserDefinedButton方法，已知按钮类型场景
     */
    @Test
    @DisplayName("正常场景 - 测试createUserDefinedButton方法已知按钮类型")
    void testCreateUserDefinedButton_KnownButtonType() {
        try (MockedStatic<ObjectAction> objectActionMock = mockStatic(ObjectAction.class)) {

            // 准备测试数据
            ObjectAction knownAction = ObjectAction.CREATE;

            // 配置Mock行为
            objectActionMock.when(() -> ObjectAction.getByButtonApiName(buttonApiName)).thenReturn(knownAction);

            // 执行被测试方法
            Boolean result = userDefinedButtonService.createUserDefinedButton(testUser, describeApiName, buttonApiName, buttonName, roles);

            // 验证结果
            assertTrue(result);
            verify(functionPrivilegeService).createFuncCode(testUser, describeApiName, knownAction.getActionCode(), knownAction.getActionLabel());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateUserDefinedButtonPrivilegeRoles方法，失败场景
     */
    @Test
    @DisplayName("异常场景 - 测试updateUserDefinedButtonPrivilegeRoles方法失败")
    void testUpdateUserDefinedButtonPrivilegeRolesError_UpdateFailed() {
        try (MockedStatic<ActionCodeConvertUtil> actionCodeMock = mockStatic(ActionCodeConvertUtil.class);
             MockedStatic<AuthContextExt> authContextMock = mockStatic(AuthContextExt.class);
             MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class);
             MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {

            // 准备测试数据
            List<String> addRoles = Lists.newArrayList("role1", "role2");
            List<String> delRoles = Lists.newArrayList("role3");
            String funcCode = "PriceBookObj_Add";
            Map<String, String> headers = new HashMap<>();

            UpdateFuncCodeRoles.Result mockResult = mock(UpdateFuncCodeRoles.Result.class);
            when(mockResult.isSuccess()).thenReturn(false);

            // 配置Mock行为
            actionCodeMock.when(() -> ActionCodeConvertUtil.convert2FuncCode(describeApiName, buttonApiName)).thenReturn(funcCode);
            authContextMock.when(() -> AuthContextExt.getAppId(testUser)).thenReturn("testAppId");
            authContextMock.when(() -> AuthContextExt.buildProperties(testUser)).thenReturn(new HashMap<>());
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn("INTERNAL");
            i18nMock.when(() -> I18N.text(I18NKey.UPDATE_ROLE_LIST_PERMISSION_FAIL_REASON)).thenReturn("更新角色权限失败");

            try (MockedStatic<FunctionPrivilegeProxy.HeaderUtil> headerUtilMock = mockStatic(FunctionPrivilegeProxy.HeaderUtil.class)) {
                headerUtilMock.when(() -> FunctionPrivilegeProxy.HeaderUtil.buildHeaders(testUser.getTenantId())).thenReturn(headers);
                when(functionPrivilegeProxy.updateFuncCodeRoles(any(UpdateFuncCodeRoles.Arg.class), eq(headers))).thenReturn(mockResult);

                // 执行并验证异常
                PermissionError exception = assertThrows(PermissionError.class, () -> {
                    userDefinedButtonService.updateUserDefinedButtonPrivilegeRoles(testUser, describeApiName, buttonApiName, addRoles, delRoles);
                });

                // 验证异常信息
                assertEquals("更新角色权限失败", exception.getMessage());
                verify(functionPrivilegeProxy).updateFuncCodeRoles(any(UpdateFuncCodeRoles.Arg.class), eq(headers));
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateFuncButtonName方法，特殊对象场景
     */
    @Test
    @DisplayName("正常场景 - 测试updateFuncButtonName方法特殊对象")
    void testUpdateFuncButtonName_SpecialObject() {
        try (MockedStatic<ActionCodeConvertUtil> actionCodeMock = mockStatic(ActionCodeConvertUtil.class);
             MockedStatic<AuthContextExt> authContextMock = mockStatic(AuthContextExt.class);
             MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class)) {

            // 准备测试数据
            String specialDescribeApiName = Utils.SALES_ORDER_PRODUCT_API_NAME;
            String funcCode = "SalesOrderProductObj_Add";
            Map<String, String> headers = new HashMap<>();

            UpdateFunc.Result mockResult = mock(UpdateFunc.Result.class);
            when(mockResult.isSuccess()).thenReturn(false); // 模拟失败，但对于特殊对象应该返回true

            // 配置Mock行为
            actionCodeMock.when(() -> ActionCodeConvertUtil.convert2FuncCode(specialDescribeApiName, buttonApiName)).thenReturn(funcCode);
            authContextMock.when(() -> AuthContextExt.getAppId(testUser)).thenReturn("testAppId");
            authContextMock.when(() -> AuthContextExt.buildProperties(testUser)).thenReturn(new HashMap<>());
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn("INTERNAL");

            try (MockedStatic<FunctionPrivilegeProxy.HeaderUtil> headerUtilMock = mockStatic(FunctionPrivilegeProxy.HeaderUtil.class)) {
                headerUtilMock.when(() -> FunctionPrivilegeProxy.HeaderUtil.buildHeaders(testUser.getTenantId())).thenReturn(headers);
                when(functionPrivilegeProxy.updateFunc(any(UpdateFunc.Arg.class), eq(headers))).thenReturn(mockResult);

                // 执行被测试方法
                Boolean result = userDefinedButtonService.updateFuncButtonName(testUser, specialDescribeApiName, buttonApiName, buttonName);

                // 验证结果 - 对于特殊对象，即使失败也应该返回true
                assertTrue(result);
                verify(functionPrivilegeProxy).updateFunc(any(UpdateFunc.Arg.class), eq(headers));
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试私有方法buildAuthContext的逻辑
     */
    @Test
    @DisplayName("正常场景 - 测试buildAuthContext方法逻辑")
    void testBuildAuthContextLogic() {
        try (MockedStatic<AuthContextExt> authContextMock = mockStatic(AuthContextExt.class);
             MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class)) {

            // 准备测试数据
            String expectedAppId = "testAppId";
            Map<String, Object> expectedProperties = new HashMap<>();
            expectedProperties.put("key", "value");
            String expectedIdentityType = "INTERNAL";

            // 配置Mock行为
            authContextMock.when(() -> AuthContextExt.getAppId(testUser)).thenReturn(expectedAppId);
            authContextMock.when(() -> AuthContextExt.buildProperties(testUser)).thenReturn(expectedProperties);
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn(expectedIdentityType);

            // 通过调用一个使用buildAuthContext的方法来间接测试
            List<String> addRoles = Lists.newArrayList("role1");
            List<String> delRoles = Lists.newArrayList();

            UpdateFuncCodeRoles.Result mockResult = mock(UpdateFuncCodeRoles.Result.class);
            when(mockResult.isSuccess()).thenReturn(true);

            try (MockedStatic<ActionCodeConvertUtil> actionCodeConvertMock = mockStatic(ActionCodeConvertUtil.class);
                 MockedStatic<FunctionPrivilegeProxy.HeaderUtil> headerUtilMock = mockStatic(FunctionPrivilegeProxy.HeaderUtil.class)) {

                actionCodeConvertMock.when(() -> ActionCodeConvertUtil.convert2FuncCode(describeApiName, buttonApiName)).thenReturn("funcCode");
                headerUtilMock.when(() -> FunctionPrivilegeProxy.HeaderUtil.buildHeaders(testUser.getTenantId())).thenReturn(new HashMap<>());
                when(functionPrivilegeProxy.updateFuncCodeRoles(any(UpdateFuncCodeRoles.Arg.class), anyMap())).thenReturn(mockResult);

                // 执行被测试方法
                userDefinedButtonService.updateUserDefinedButtonPrivilegeRoles(testUser, describeApiName, buttonApiName, addRoles, delRoles);

                // 验证AuthContext的构建
                verify(functionPrivilegeProxy).updateFuncCodeRoles(argThat(arg -> {
                    com.facishare.paas.appframework.privilege.dto.AuthContext authContext = arg.getAuthContext();
                    return expectedAppId.equals(authContext.getAppId()) &&
                           testUser.getTenantId().equals(authContext.getTenantId()) &&
                           testUser.getUserId().equals(authContext.getUserId()) &&
                           expectedProperties.equals(authContext.getProperties()) &&
                           expectedIdentityType.equals(authContext.getIdentityType());
                }), anyMap());
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试外部用户的buildAuthContext逻辑
     */
    @Test
    @DisplayName("正常场景 - 测试外部用户buildAuthContext方法逻辑")
    void testBuildAuthContextLogicForOutUser() {
        try (MockedStatic<AuthContextExt> authContextMock = mockStatic(AuthContextExt.class);
             MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class)) {

            // 准备测试数据 - 外部用户
            User outUser = User.builder()
                    .tenantId("123456")
                    .userId("78910")
                    .outTenantId("out123")
                    .outUserId("out456")
                    .build();

            String expectedAppId = "testAppId";
            Map<String, Object> expectedProperties = new HashMap<>();
            String expectedIdentityType = "EXTERNAL";

            // 配置Mock行为
            authContextMock.when(() -> AuthContextExt.getAppId(outUser)).thenReturn(expectedAppId);
            authContextMock.when(() -> AuthContextExt.buildProperties(outUser)).thenReturn(expectedProperties);
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn(expectedIdentityType);

            // 通过调用一个使用buildAuthContext的方法来间接测试
            List<String> addRoles = Lists.newArrayList("role1");
            List<String> delRoles = Lists.newArrayList();

            UpdateFuncCodeRoles.Result mockResult = mock(UpdateFuncCodeRoles.Result.class);
            when(mockResult.isSuccess()).thenReturn(true);

            try (MockedStatic<ActionCodeConvertUtil> actionCodeConvertMock = mockStatic(ActionCodeConvertUtil.class);
                 MockedStatic<FunctionPrivilegeProxy.HeaderUtil> headerUtilMock = mockStatic(FunctionPrivilegeProxy.HeaderUtil.class)) {

                actionCodeConvertMock.when(() -> ActionCodeConvertUtil.convert2FuncCode(describeApiName, buttonApiName)).thenReturn("funcCode");
                headerUtilMock.when(() -> FunctionPrivilegeProxy.HeaderUtil.buildHeaders(outUser.getTenantId())).thenReturn(new HashMap<>());
                when(functionPrivilegeProxy.updateFuncCodeRoles(any(UpdateFuncCodeRoles.Arg.class), anyMap())).thenReturn(mockResult);

                // 执行被测试方法
                userDefinedButtonService.updateUserDefinedButtonPrivilegeRoles(outUser, describeApiName, buttonApiName, addRoles, delRoles);

                // 验证AuthContext的构建，特别是外部租户ID
                verify(functionPrivilegeProxy).updateFuncCodeRoles(argThat(arg -> {
                    AuthContext authContext = arg.getAuthContext();
                    return expectedAppId.equals(authContext.getAppId()) &&
                           outUser.getTenantId().equals(authContext.getTenantId()) &&
                           outUser.getUserId().equals(authContext.getUserId()) &&
                           outUser.getOutTenantId().equals(authContext.getOuterTenantId()) &&
                           expectedProperties.equals(authContext.getProperties()) &&
                           expectedIdentityType.equals(authContext.getIdentityType());
                }), anyMap());
            }
        }
    }
}