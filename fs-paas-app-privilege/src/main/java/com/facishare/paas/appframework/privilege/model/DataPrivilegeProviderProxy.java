package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderDefinition.BatchCheckBusinessPrivilege;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderDefinition.CheckBusinessPrivilege;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(
        value = "NCRM",
        desc = "NCRM服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface DataPrivilegeProviderProxy {
    @POST(value = "/API/v1/rest/object/DataPrivilegeProvider/service/checkBusinessPrivilege", desc = "用户的记录权限查询")
    CheckBusinessPrivilege.RestResult checkBusinessPrivilege(@Body CheckBusinessPrivilege.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/API/v1/rest/object/DataPrivilegeProvider/service/batchCheckBusinessPrivilege", desc = "用户的记录权限查询")
    BatchCheckBusinessPrivilege.RestResult batchCheckBusinessPrivilege(@Body BatchCheckBusinessPrivilege.Arg arg, @HeaderMap Map<String, String> header);
}
