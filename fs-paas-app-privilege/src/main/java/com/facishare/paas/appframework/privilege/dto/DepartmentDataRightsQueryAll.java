package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface DepartmentDataRightsQueryAll {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        DepartmentDataRightsContext context;
        List<String> entityIds;
        List<String> deptIds;
        int scene;
        int currentPage;
        int size;
    }

    @Data
    class Result {
        List<DepartmentDataRights> deptRights;
        PageInfo pageInfo;
    }
}

