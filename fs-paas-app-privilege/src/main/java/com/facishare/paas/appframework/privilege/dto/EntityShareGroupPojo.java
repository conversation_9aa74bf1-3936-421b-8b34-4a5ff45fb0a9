package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EntityShareGroupPojo {
  private String id;
  private String tenantId;
  private String appId;
  private List<String> entityIds;
  private List<String> shareIds;
  private List<String> receiveIds;
  private Integer permission;
  private Integer status;
  private String creator;
  private long createTime;
  private String modifier;
  private long modifyTime;
  private Integer delFlag;
  private Integer shareDeptCascade;
  private Integer receiveDeptCascade;
  private Integer entityShareGroupType;
  private Integer entityShareType;
  private Integer basedType;
}
