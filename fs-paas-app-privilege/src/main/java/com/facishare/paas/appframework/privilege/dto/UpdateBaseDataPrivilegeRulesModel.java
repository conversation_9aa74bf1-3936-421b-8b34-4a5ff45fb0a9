package com.facishare.paas.appframework.privilege.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by yusb on 2017/4/24.
 */
public class UpdateBaseDataPrivilegeRulesModel {
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends BasePrivilegeArg {
        private List<EntityOpennessPojo> entityOpenness;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends BasePrivilegeResult {
    }
}
