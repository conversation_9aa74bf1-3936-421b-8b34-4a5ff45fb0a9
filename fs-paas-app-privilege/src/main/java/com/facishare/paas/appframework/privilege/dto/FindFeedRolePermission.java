package com.facishare.paas.appframework.privilege.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface FindFeedRolePermission {
    @Builder
    @Data
    class Arg {
        @JSONField(name = "CurrentEmployeeID")
        private String currentEmployeeID;
        @JSONField(name = "DataRoleIDs")
        private List<String> dataRoleIDs;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private Value value;
        private boolean success;
        private String message;
        private int errorCode;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Value {
        @JSONField(name = "V5DataPermissionGroupEntities")
        private List<V5DataPermissionGroupEntity> v5DataPermissionGroupEntities;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class V5DataPermissionGroupEntity {
        @JSONField(name = "DataRoleID")
        private String dataRoleID;
        @JSONField(name = "DataRoleName")
        private String dataRoleName;
        @JSONField(name = "DataPermissionItems")
        private List<DataPermissionItem> dataPermissionItems;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class DataPermissionItem {
        @JSONField(name = "DataDisplayName")
        private String dataDisplayName;
        @JSONField(name = "DataKey")
        private String dataKey;
        @JSONField(name = "IsChecked")
        private Boolean isChecked;
        @JSONField(name = "IsEditable")
        private Boolean isEditable;
        @JSONField(name = "DataRoleID")
        private String dataRoleID;
    }
}
