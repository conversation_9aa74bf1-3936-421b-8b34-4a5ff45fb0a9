package com.facishare.paas.appframework.privilege.dto;

import lombok.*;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class QueryAllEntityShareModel {

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode(callSuper = true)
  public static class Arg extends BasePrivilegeArg {
    private List<String> entitys;
    private List<String> sharesOrReceives;
    private List<String> sources;
    private List<String> receives;
    private List<Integer> sourceTypes;
    private List<Integer> receiveTypes;
    private Integer permission;
    private Integer status;
    private Integer scope;
    private BasePageInfoDataPrivilege page;
    private Map<Integer, Set<String>> sharesId;
    private Map<Integer, Set<String>> receivesId;
    private Map<Integer, Set<String>> sharesOrReceivesId;
    private Set<String> createIds;
    private Set<String> modifyIds;
    private Map<String, Long> createTimeRange;
    private Map<String, Long> modifyTimeRange;
    private Boolean outReceive;
    private Integer basedType;

    /**
     * 缺省时不用id查询
     */
    private List<String> ids;

    /**
     * 0来源方上游 1来源方下游
     *
     * NULL 查询全部
     */
    private Integer entityShareType;

    private Set<String> groupIds;
  }


  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Result extends BasePrivilegeResult {
    @Delegate
    private BaseResultDataContent result;

    @Data
    public static class BaseResultDataContent {
      private List<EntitySharePojo> content;
      private BasePageInfoDataPrivilege page;
    }
  }
}
