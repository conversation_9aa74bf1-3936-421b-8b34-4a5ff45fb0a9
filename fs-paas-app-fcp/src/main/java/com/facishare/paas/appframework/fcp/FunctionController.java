package com.facishare.paas.appframework.fcp;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.ObjectFunctionService;
import com.facishare.paas.appframework.core.predef.service.dto.function.*;
import com.facishare.paas.appframework.core.model.ContextManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.facishare.paas.appframework.fcp.FunctionController.SERVICE_NAME;

/**
 * Created by liyiguang on 2018/2/5.
 */
@Component
@FcpService(SERVICE_NAME)
public class FunctionController {

    public static final String SERVICE_NAME = "function";

    @Autowired
    ObjectFunctionService objectFunctionService;

    @FcpMethod("create")
    public CreateFunction.Result create(CreateFunction.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME, "create");
        return objectFunctionService.create(arg, context);
    }

    @FcpMethod("update")
    public UpdateFunction.Result update(UpdateFunction.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME, "update");
        return objectFunctionService.update(arg, context);
    }

    @FcpMethod("find")
    public FindFunction.Result find(FindFunction.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME, "find");
        return objectFunctionService.find(arg, context);
    }

    @FcpMethod("query")
    public QueryFunction.Result query(QueryFunction.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME, "query");
        return objectFunctionService.query(arg, context);
    }

    @FcpMethod("enable")
    public void enable(ActiveRule.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME, "isActive");
        objectFunctionService.isActive(arg, context);
    }

    @FcpMethod("func_exist")
    public FunctionExist.Result isExist(FunctionExist.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME, "funExist");
        return objectFunctionService.funExist(arg, context);
    }

    @FcpMethod("compile_check")
    public CompileCheck.Result compile(CompileCheck.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME, "compileCheck");
        return objectFunctionService.compileCheck(arg, context);
    }

    @FcpMethod("delete")
    public void delete(DeleteFunction.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME, "delete");
        objectFunctionService.delete(arg, context);
    }

    @FcpMethod("debug_function")
    public DebugRunFunction.Result debugFunction(DebugRunFunction.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME, "debugFunction");
        return objectFunctionService.debugFunction(arg, context);
    }

    @FcpMethod("check_function_limit")
    public void checkFunctionCountLimit() {
        ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME,"checkFunctionCountLimit");
        objectFunctionService.checkFunctionCountLimit(context);
    }
}
